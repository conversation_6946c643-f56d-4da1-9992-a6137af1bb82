{"activityMonitor.copyDiagnosticInformation": "Diagnostische gegevens kopiëren", "activityMonitor.copyExecutablePath": "Uitvoerbaar pad kopiëren", "activityMonitor.copyUrl": "URL kopiëren", "activityMonitor.forceKillProcess": "Uitschakelingsproces forceren", "activityMonitor.inspectActivityMonitor": "Activiteitenmonitor inspecteren", "activityMonitor.killProcess": "Uitschakelingsproces", "activityMonitor.openDevTools": "DevTools openen", "activityMonitor.reload": "Opnieuw laden", "clientPlaceholder.placeholderDescription": "Klik om weer te geven in dit venster", "clientPlaceholder.placeholderTitle": "Bekijkt momenteel content in een ander venster", "commandSearch.window.title": "Notion - Zoekopdrachten", "crashWatchdog.dialog.abnormalExit": "Onverwachte afsluiting: de pagina is afgesloten met een afsluitcode die niet gelijk is aan nul.", "crashWatchdog.dialog.buttonCloseTab": "Tabblad sluiten", "crashWatchdog.dialog.buttonCloseWindow": "Venster sluiten", "crashWatchdog.dialog.buttonRestartApp": "Notion opnieuw starten", "crashWatchdog.dialog.crashed": "Gecrasht: De pagina is om onbekende reden gecrasht.", "crashWatchdog.dialog.details": "Pagina-URL: {url}Reden: {reason} Afsluitcode: {exitCode}", "crashWatchdog.dialog.integrityFailure": "Integriteitsfout: De pagina is niet geslaagd voor de integriteitscontrole van de code.", "crashWatchdog.dialog.killed": "Afgesloten: De pagina is afgesloten door een extern proces.", "crashWatchdog.dialog.launchFailed": "Starten is mislukt: Het proces kan niet worden gestart.", "crashWatchdog.dialog.message": "Er is iets misgegaan bij het weergeven van dit tabblad. We kunnen het tabblad niet automatisch herstellen.", "crashWatchdog.dialog.oom": "OOM: De pagina is g<PERSON><PERSON>t wegens onvoldoende geheugen.", "crashWatchdog.dialog.title": "Er is iets misgegaan", "crashWatchdog.dialog.urlUnknown": "Onbekend", "desktop.activityMonitor.all": "Alle", "desktop.activityMonitor.cpuPercent": "% CPU", "desktop.activityMonitor.cpuPercentDescription": "Het gede<PERSON><PERSON> van beschikbare CPU-tijd dat door het proces wordt gebruikt.", "desktop.activityMonitor.cpuTime": "CPU-tijd", "desktop.activityMonitor.cpuTimeDescription": "Het totale aantal seconden CPU-tijd dat is gebruikt sinds het opstarten van het proces.", "desktop.activityMonitor.creationTime": "Gemaakt", "desktop.activityMonitor.creationTimeDescription": "De hoeveelheid tijd sinds het proces is gemaakt.", "desktop.activityMonitor.frames": "Frames", "desktop.activityMonitor.framesDescription": "Het aantal frames dat door het proces wordt beheerd. Veel renderprocessen zijn verantwoordelijk voor meerdere frames.", "desktop.activityMonitor.hidden": "Verborgen", "desktop.activityMonitor.hideColumns": "Kolommen verbergen", "desktop.activityMonitor.hideFilters": "Filters verbergen", "desktop.activityMonitor.idleWakeupsPerSecond": "<PERSON><PERSON><PERSON><PERSON> wekkers", "desktop.activityMonitor.idleWakeupsPerSecondDescription": "Het aantal keren dat het proces de CPU heeft gewekt sinds de laatste update van de activiteitenmonitor.", "desktop.activityMonitor.loading": "Activiteitsgegevens worden geladen", "desktop.activityMonitor.memCurrent": "<PERSON><PERSON><PERSON><PERSON> (huidig)", "desktop.activityMonitor.memCurrentDescription": "De 'werksetgrootte' van het proces (de huidige set pagina's in het RAM-geheugen). Bij dit aantal is geen rekening gehouden met de geheugencompressie van het besturingssysteem, het beheer van inactieve en gecachte pagina's, of andere technieken voor geheugenbeheer. Het proces gebruikt waarschijnlijk veel minder fysiek geheugen.", "desktop.activityMonitor.memPeak": "Geheugen (piek)", "desktop.activityMonitor.memPeakDescription": "De maximale werksetgrootte van het proces (de maximale hoeveelheid fysiek geheugen die het proces gebruikt sinds het is gestart). De 'werksetgrootte' van het proces (de huidige set pagina's in het RAM-geheugen). <PERSON><PERSON><PERSON> dit aant<PERSON> is geen rekening gehouden met de geheugencompressie van het besturingssysteem, het beheer van inactieve en gecachte pagina's, of andere technieken voor geheugenbeheer. Het proces gebruikt waarschijnlijk veel minder fysiek geheugen.", "desktop.activityMonitor.memPrivate": "G<PERSON>eugen (privé)", "desktop.activityMonitor.memPrivateDescription": "De hoeveelheid fysiek geheugen die door het proces wordt toegewezen en niet wordt gedeeld met andere processen, zoals JS-heap of HTML-content.", "desktop.activityMonitor.memShared": "Geheugen (gedeeld)", "desktop.activityMonitor.memSharedDescription": "De hoeveelheid fysiek geheugen die door het proces wordt toegewezen en wordt gedeeld met andere processen, zoals gedeelde bibliotheken of toegewezen bestanden.", "desktop.activityMonitor.mixed": "<PERSON><PERSON><PERSON><PERSON>", "desktop.activityMonitor.parentWindowId": "<PERSON> van boven<PERSON>ggend venster", "desktop.activityMonitor.parentWindowIdDescription": "De unieke ID van het venster met dit tabblad.", "desktop.activityMonitor.pid": "PID", "desktop.activityMonitor.pidDescription": "De <PERSON> van het proces zoals het door het besturingssysteem wordt gebruikt.", "desktop.activityMonitor.processName": "Procesnaam", "desktop.activityMonitor.showColumns": "<PERSON><PERSON><PERSON><PERSON> tonen", "desktop.activityMonitor.showFilters": "Filters tonen", "desktop.activityMonitor.tabId": "Tabblad-ID", "desktop.activityMonitor.tabIdDescription": "De unieke ID van het tabblad in de Notion-app.", "desktop.activityMonitor.type": "Type", "desktop.activityMonitor.url": "URL", "desktop.activityMonitor.urlDescription": "De URL van het proces. Veel renderprocessen zijn verantwoordelijk voor meerdere frames. <PERSON>ie de kolom Frames voor meer informatie.", "desktop.activityMonitor.visibilityState": "<PERSON><PERSON><PERSON>ba<PERSON><PERSON><PERSON>", "desktop.activityMonitor.visibilityStateDescription": "De zichtbaarheidsstatus van het proces. Als het proces een renderproces is, is dit de zichtbaarheidsstatus van het hoofdframe.", "desktop.activityMonitor.visible": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktop.tabBar.backButtonLabel": "Terug", "desktop.tabBar.closeSidebarLabel": "Zijbalk vergrendelen", "desktop.tabBar.closeTabLabel": "Tabblad {tabTitle} sluiten", "desktop.tabBar.forwardButtonLabel": "Volgende", "desktop.tabBar.newTabButtonLabel": "<PERSON><PERSON><PERSON> tabblad", "desktop.tabBar.openSidebarLabel": "Zijbalk openen", "desktop.tabBar.tabSpacesLabel": "Tabblad-spaces", "desktopExtensions.install.failed.title": "Kan extensie niet installeren", "desktopExtensions.manage.cancel": "<PERSON><PERSON><PERSON>", "desktopExtensions.manage.disable": "Uitschakelen", "desktopExtensions.manage.enable": "Inschakelen", "desktopExtensions.manage.message": "Wat wil je doen met {extensionTitle} {extensionVersion}?", "desktopExtensions.manage.title": "<PERSON><PERSON><PERSON> beheren", "desktopExtensions.manage.uninstall": "Installatie ongedaan maken", "desktopExtensions.manage.unload": "<PERSON>den on<PERSON>aan maken", "desktopExtensions.openFailed.noPopupMessage": "Deze extensie heeft geen pop-up opgegeven (action.default_popup)", "desktopExtensions.openFailed.noPopupTitle": "Kan extensie niet openen", "desktopExtensions.unzip.failed.badFileRead": "Kan <PERSON>X-bestand niet lezen: {error}", "desktopExtensions.unzip.failed.badFileWrite": "Kan niet schrijven naar bestand {filePath}: {error}", "desktopExtensions.unzip.failed.badFolderCreate": "Kan geen map voor extensies maken in {extensionPath}: {error}", "desktopExtensions.unzip.failed.badManifest": "Kan manifest.json in deze extensie niet parseren. Is dit een geldige extensie? Foutmelding: {error}", "desktopExtensions.unzip.failed.badManifestNoName": "Kan geen naam vinden in manifest.json", "desktopExtensions.unzip.failed.error": "Kan CRX-bestand niet uitpa<PERSON>ken: {error}", "desktopExtensions.unzip.failed.noManifest": "Kan manifest.json niet vinden in het CRX-bestand. Is dit een geldige extensie?", "desktopInstaller.failedToMove.detail": "We zijn er niet in geslaagd de app naar je map Applicaties te verplaatsen. Verplaats deze handmatig.", "desktopInstaller.failedToMove.title": "Kan app niet verpla<PERSON>en", "desktopInstaller.invalidInstallDialog.cancelButton.label": "<PERSON><PERSON><PERSON>", "desktopInstaller.invalidInstallDialog.confirmMove": "Je Notion-applicatie is niet correct geïnstalleerd. Wil je de Notion-app verplaatsen naar je map Applicaties?", "desktopInstaller.invalidInstallDialog.okButton.label": "Ok", "desktopInstaller.invalidInstallDialog.title": "Ongeldige installatie", "desktopTopbar.appMenu.about": "Over Notion", "desktopTopbar.appMenu.checkForUpdate": "Controleren op updates...", "desktopTopbar.appMenu.checkForUpdate.noUpdateAvailable": "Je gebruikt de nieuwste versie van Notion!", "desktopTopbar.appMenu.checkForUpdate.title": "Controleren op updates...", "desktopTopbar.appMenu.checkForUpdate.updateAvailable": "Er is een nieuwe versie van Notion beschikbaar die momenteel op de achtergrond wordt gedownload. Bedankt voor het installeren van de update!", "desktopTopbar.appMenu.checkForUpdate.updateCheckFailed": "Notion kan geen verbinding maken met de updateserver vanwege een probleem met je internetverbinding of met de updateserver zelf. <PERSON>beer het later opnieuw.", "desktopTopbar.appMenu.downloadingUpdate": "Update wordt gedownload ({percentage}%)", "desktopTopbar.appMenu.hide": "Verberg Notion", "desktopTopbar.appMenu.hideOthers": "Verberg anderen", "desktopTopbar.appMenu.preferences": "<PERSON><PERSON><PERSON><PERSON>…", "desktopTopbar.appMenu.quit": "Stoppen", "desktopTopbar.appMenu.quitWithoutSavingTabs": "Afsluiten zonder tabbladen op te slaan", "desktopTopbar.appMenu.restartToApplyUpdate": "Start opnieuw op om de update toe te passen", "desktopTopbar.appMenu.services": "<PERSON><PERSON><PERSON>", "desktopTopbar.appMenu.unhide": "Toon alle", "desktopTopbar.editMenu.copy": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.copyLinkToCurrentPage": "Link naar huidige pagina kopiëren", "desktopTopbar.editMenu.copyLinkToCurrentPageBlockTitle": "<PERSON><PERSON> van h<PERSON>ige pagina kopi<PERSON>ren", "desktopTopbar.editMenu.cut": "Knippen", "desktopTopbar.editMenu.paste": "Plakken", "desktopTopbar.editMenu.pasteAndMatchStyle": "Stijl plakken en matchen", "desktopTopbar.editMenu.redo": "Opnieuw", "desktopTopbar.editMenu.selectAll": "Alles selecteren", "desktopTopbar.editMenu.speech": "<PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.speech.startSpeaking": "<PERSON><PERSON> met spreken", "desktopTopbar.editMenu.speech.stopSpeaking": "Stop met spreken", "desktopTopbar.editMenu.title": "Bewerken", "desktopTopbar.editMenu.undo": "Ongedaan maken", "desktopTopbar.extensionsMenu.install": "Extensie installeren…", "desktopTopbar.extensionsMenu.manage": "Extensies beheren", "desktopTopbar.fileMenu.close": "Venster sluiten", "desktopTopbar.fileMenu.closeTab": "Tabblad sluiten", "desktopTopbar.fileMenu.newNotionWindow": "Nieuw Notion-venster", "desktopTopbar.fileMenu.newTab": "<PERSON><PERSON><PERSON> tabblad", "desktopTopbar.fileMenu.newWindow": "<PERSON><PERSON><PERSON>", "desktopTopbar.fileMenu.print": "Afdrukken…", "desktopTopbar.fileMenu.quit": "Afsluiten", "desktopTopbar.fileMenu.quitWithoutSavingTabs": "Afsluiten zonder tabbladen op te slaan", "desktopTopbar.fileMenu.reopenClosedTab": "Laatst gesloten tabblad opnieuw openen", "desktopTopbar.fileMenu.title": "Bestand", "desktopTopbar.helpMenu.copyInstallId": "Installatienummer kopiëren", "desktopTopbar.helpMenu.disableAdvancedLogging": "Geavanceerde logboekregistratie uitzetten en opnieuw opstarten", "desktopTopbar.helpMenu.disableDebugLogging": "Geavanceerde logboekregistratie uitschakelen en opnieuw opstarten", "desktopTopbar.helpMenu.disableHardwareAcceleration": "Hardwareversnelling uitschakelen en opnieuw opstarten", "desktopTopbar.helpMenu.enableAdvancedLogging": "Geavanceerde logboekregistratie inschakelen en opnieuw opstarten", "desktopTopbar.helpMenu.enableDebugLogging": "Geavanceerde logboekregistratie inschakelen en opnieuw opstarten", "desktopTopbar.helpMenu.enableHardwareAcceleration": "Hardwareversnelling inschakelen en opnieuw opstarten", "desktopTopbar.helpMenu.openActivityMonitor": "Activiteitenmonitor openen", "desktopTopbar.helpMenu.openConsole": "Console openen", "desktopTopbar.helpMenu.openHelpAndSupport": "Hulp en documentatie openen", "desktopTopbar.helpMenu.recordingNetLog": "Netwerklogboek opnemen…", "desktopTopbar.helpMenu.recordNetLog": "Netwerklogboek opnemen...", "desktopTopbar.helpMenu.recordNetLogConfirmation": "Er wordt momenteel een netwerklogboek opgenomen in je map Downloads. Als je de opname wilt stoppen, klik je in het menu Problemen oplossen op de knop Opname van netwerklogboek stoppen of sluit je de app af.", "desktopTopbar.helpMenu.recordNetLogFailed": "Opne<PERSON> van netwerklogboek is mislukt. Probeer het opnieuw of controleer de logboeken voor meer informatie.", "desktopTopbar.helpMenu.recordNetLogFailedMessage": "Probeer het opnieuw of controleer de logboeken voor meer informatie. Foutmelding:", "desktopTopbar.helpMenu.recordNetLogFailedTitle": "Opne<PERSON> van netwerklogboek is mislukt", "desktopTopbar.helpMenu.recordNetLogStop": "Opnemen van netwerklogboek stoppen…", "desktopTopbar.helpMenu.recordPerformanceTrace": "Prestatietracering opnemen…", "desktopTopbar.helpMenu.recordPerformanceTraceConfirm": "Wilt u een prestatietracering opnemen voor de komende 30 seconden? Als u klaar bent, wordt het in uw map Downloads geplaatst.", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmCancel": "<PERSON><PERSON><PERSON>", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmOk": "Prestatietracering opnemen", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmTitle": "Een prestatietracering opnemen?", "desktopTopbar.helpMenu.resetAndEraseAllLocalData": "Alle lokale gegevens resetten en wissen", "desktopTopbar.helpMenu.showLogsInExplorer": "Logboeken weergeven in Verkenner", "desktopTopbar.helpMenu.showLogsInFinder": "Logboeken weergeven in Finder", "desktopTopbar.helpMenu.title": "Help", "desktopTopbar.historyMenu.historyBack": "Terug", "desktopTopbar.historyMenu.historyForward": "Volgende", "desktopTopbar.historyMenu.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.toggleDevTools": "Schakelen naar ontwikkelaarstools", "desktopTopbar.toggleWindowDevTools": "Schakelen naar venster ontwikkelaarstools", "desktopTopbar.troubleshootingMenu.title": "Probleemoplossen", "desktopTopbar.viewMenu.actualSize": "Werkelijke grootte", "desktopTopbar.viewMenu.forceReload": "Opnieuw laden forceren", "desktopTopbar.viewMenu.forceReloadDialog.cancel": "<PERSON><PERSON><PERSON>", "desktopTopbar.viewMenu.forceReloadDialog.message": "Je bent offline. Als je deze pagina geforceerd op<PERSON>uw la<PERSON>t, heb je pas weer toegang tot de pagina wanneer je weer online bent.", "desktopTopbar.viewMenu.forceReloadDialog.ok": "Toch opnieuw laden", "desktopTopbar.viewMenu.forceReloadDialog.title": "Weet je zeker dat je het opnieuw laden wilt forceren?", "desktopTopbar.viewMenu.reload": "Opnieuw laden", "desktopTopbar.viewMenu.showHideSidebar": "Zijbalk weergeven/verbergen", "desktopTopbar.viewMenu.showHideTabSpaceButton": "Tabbladgroepen weergeven/verbergen", "desktopTopbar.viewMenu.title": "Weergeven", "desktopTopbar.viewMenu.togglefullscreen": "<PERSON><PERSON><PERSON>n naar volledige pagina", "desktopTopbar.viewMenu.zoomIn": "Inzoomen", "desktopTopbar.viewMenu.zoomOut": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.whatsNewMac.title": "Nieuw in Notion voor macOS openen", "desktopTopbar.whatsNewWindows.title": "Nieuw in Notion voor Windows openen", "desktopTopbar.windowMenu.close": "Sluiten", "desktopTopbar.windowMenu.front": "Voor", "desktopTopbar.windowMenu.maximize": "Maximaliseren", "desktopTopbar.windowMenu.minimize": "Minimaliseren", "desktopTopbar.windowMenu.showNextTab": "Volgend tabblad weergeven", "desktopTopbar.windowMenu.showPreviousTab": "Vorig tabblad weergeven", "desktopTopbar.windowMenu.title": "<PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.zoom": "Zoom-vergadering", "desktopTroubleshooting.resetData.cancel": "<PERSON><PERSON><PERSON>", "desktopTroubleshooting.resetData.closingWindows": "Notion-vensters worden gesloten", "desktopTroubleshooting.resetData.deletingFiles": "Bestanden worden verwijderd", "desktopTroubleshooting.resetData.done": "<PERSON><PERSON><PERSON>", "desktopTroubleshooting.resetData.doneMessage": "De app is opnieuw ingesteld.", "desktopTroubleshooting.resetData.failed": "Ons herstelproces heeft een aantal bestanden niet kunnen verwijderen. Onze excuses voor het ongemak - ga naar https://www.notion.so/help voor hulp. Sluit Notion volledig om een volledige reset van de app handmatig af te dwingen. Verwijder vervolgens het volgende pad: {userDataPath}", "desktopTroubleshooting.resetData.message": "Hierdoor worden alle lokale en interne gegevens verwijderd, inclusief de cache en lokale instellingen, en wordt de Notion-app hersteld naar de staat van een nieuw geïnstalleerde app. Hiermee word je ook uitgelogd bij Notion. Je pagina's en andere in-app-content blijven ongewijzigd. Wil je doorgaan?", "desktopTroubleshooting.resetData.reset": "Alle lokale gegevens resetten", "desktopTroubleshooting.resetData.restart": "Opnieuw starten", "desktopTroubleshooting.resetData.title": "Alle lokale gegevens resetten en wissen", "desktopTroubleshooting.showLogs.error.message.mac": "Notion heeft een fout ontdekt bij het weergeven van de logboeken in Finder:", "desktopTroubleshooting.showLogs.error.message.windows": "Notion heeft een fout ontdekt bij het weergeven van de logboeken in Verkenner:", "desktopTroubleshooting.showLogs.error.title": "<PERSON><PERSON> we<PERSON><PERSON><PERSON> van de logboeken is mislukt", "desktopTroubleshooting.startRecordingNetLog": "Opnemen van netwerklogboek starten", "desktopTroubleshooting.stopRecordingNetLog": "Opnemen van netwerklogboek stoppen", "menuBarIcon.menu.changeCommandSearchAndNotionAiShortcut": "Snelkoppelingen bewerken", "menuBarIcon.menu.changeCommandSearchShortcut": "Snelkoppeling voor zoekopdrachten wijzigen", "menuBarIcon.menu.enableQuickSearch": "Snel zoeken inschakelen", "menuBarIcon.menu.keepInBackground": "<PERSON> de achtergrond houden", "menuBarIcon.menu.launchPreferences": "Voorkeuren openen", "menuBarIcon.menu.openOnLogin": "Notion openen bij aanmelden", "menuBarIcon.menu.quitNotion": "Notion afsluiten", "menuBarIcon.menu.showImmediately": "Onmiddellijk tonen", "menuBarIcon.menu.showNotionInMenuBar": "Toon Notion in de menubalk", "menuBarIcon.menu.toggleCommandSearch": "To<PERSON>", "menuBarIcon.menu.toggleNotionAi": "Notion AI in-/uitschakelen", "openAtLogin.dialog.detail": "{operatingSystem} heeft de configuratie van de instelling Openen bij inloggen geblokkeerd. Dit gebeurt meestal wanneer het opstarten van Notion is geconfigureerd in de systeeminstellingen of wanneer je onvoldoende machtigingen hebt. Je kunt deze instelling nog steeds handmatig configureren in de systeeminstellingen.", "openAtLogin.dialog.title": "Openen bij aanmelden", "tabSpaces.deleteDialog.cancelButton": "<PERSON><PERSON><PERSON>", "tabSpaces.deleteDialog.deleteButton": "Verwijderen", "tabSpaces.deleteDialog.detail": "Alle tabbladen in deze tabbladgroep worden gedegroepeerd.", "tabSpaces.deleteDialog.title": "Je tabbladgroep '{title}' verwijderen?", "tabSpaces.snackbar.switchedToTabGroup": "Overgeschakeld naar {title}", "tabSpaces.snackbar.switchedToUngroupedTabs": "Overgeschakeld naar niet-g<PERSON><PERSON><PERSON><PERSON> tabbladen", "tabSpaces.snackbar.tabGroupPlaceholder": "Tabbladgroepen", "updatePrompt.detail": "Wil je de update nu installeren? Nadat deze is voltooid, herstellen we geopende vensters en tabbladen.", "updatePrompt.installAndRelaunch": "Installeren en opnieuw opstarten", "updatePrompt.message": "Er is een nieuwe versie van Notion beschik<PERSON>ar!", "updatePrompt.remindMeLater": "<PERSON><PERSON><PERSON> mij hier later aan", "window.closeDialog.cancelButton": "<PERSON><PERSON><PERSON>", "window.closeDialog.confirmButton": "Sluiten", "window.closeDialog.title.app": "Notion sluiten?", "window.closeDialog.title.tab": "Notion-tabblad sluiten?", "window.closeDialog.title.window": "Notion-venster sluiten?", "window.loadingError.message": "Fout bij het laden van Notion. <PERSON><PERSON> verbinding met het internet om aan de slag te gaan.", "window.loadingError.reload": "Opnieuw laden", "window.movedTabSnackbarMessage": "{tabTitle} verplaatst naar {tabSpaceTitle}", "window.tabLoadingError.cancel": "<PERSON><PERSON><PERSON>", "window.tabMenu.closeOtherTabs": "Andere tabbladen sluiten", "window.tabMenu.closeTab": "Tabblad sluiten", "window.tabMenu.closeTabsToLeft": "Tabbladen links sluiten", "window.tabMenu.closeTabsToRight": "Tabbladen rechts sluiten", "window.tabMenu.copyLink": "<PERSON><PERSON><PERSON> kop<PERSON>", "window.tabMenu.duplicateTab": "<PERSON><PERSON><PERSON> dup<PERSON><PERSON>", "window.tabMenu.moveTo": "Verplaatsen naar", "window.tabMenu.moveToNewWindow": "Tabblad naar nieuw venster verplaatsen", "window.tabMenu.moveToSubmenuNewWindow": "<PERSON><PERSON><PERSON>", "window.tabMenu.pinTab": "Tabblad vastmaken", "window.tabMenu.refresh": "<PERSON><PERSON><PERSON> ve<PERSON>", "window.tabMenu.reopenClosedTab": "Laatst gesloten tabblad opnieuw openen", "window.tabMenu.replacePinnedTabUrl": "Vastgemaakte URL vervangen door de huidige", "window.tabMenu.returnToPinnedTabUrl": "Terug naar vastgezette URL", "window.tabMenu.ungroupTab": "Tabblad verwijderen uit groep", "window.tabMenu.unpinTab": "Tabblad los<PERSON>", "window.tabTitlePlaceholder": "Tabblad", "window.ungroupedTabSnackbarMessage": "{tabTitle} verwijderd uit groep"}