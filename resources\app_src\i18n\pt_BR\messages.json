{"activityMonitor.copyDiagnosticInformation": "Copiar informações de diagnóstico", "activityMonitor.copyExecutablePath": "Copiar caminho executável", "activityMonitor.copyUrl": "Copiar URL", "activityMonitor.forceKillProcess": "Forçar processo de eliminação", "activityMonitor.inspectActivityMonitor": "Inspecionar monitor de atividade", "activityMonitor.killProcess": "Processo de eliminação", "activityMonitor.openDevTools": "Open DevTools", "activityMonitor.reload": "<PERSON><PERSON><PERSON><PERSON>", "clientPlaceholder.placeholderDescription": "Clique para exibir esta janela", "clientPlaceholder.placeholderTitle": "O conteúdo está sendo visualizado em outra janela", "commandSearch.window.title": "Notion – Pesquisa por Atalho", "crashWatchdog.dialog.abnormalExit": "Saída anormal: a página foi encerrada com um código de saída diferente de zero.", "crashWatchdog.dialog.buttonCloseTab": "<PERSON><PERSON><PERSON>", "crashWatchdog.dialog.buttonCloseWindow": "<PERSON><PERSON><PERSON>", "crashWatchdog.dialog.buttonRestartApp": "Reiniciar o Notion", "crashWatchdog.dialog.crashed": "Problema: a página travou devido a um motivo desconhecido.", "crashWatchdog.dialog.details": "URL da página: {url} Motivo: {reason} Código <PERSON>ída: {exitCode}", "crashWatchdog.dialog.integrityFailure": "Falha de integridade: a página falhou nas verificações de integridade do código.", "crashWatchdog.dialog.killed": "Suprimida: a página foi suprimida por um processo externo.", "crashWatchdog.dialog.launchFailed": "Falha ao iniciar: o processo não foi iniciado.", "crashWatchdog.dialog.message": "Algo deu errado ao exibir esta guia e não foi possível recuperá-la automaticamente.", "crashWatchdog.dialog.oom": "OOM: a página ficou sem memória e travou.", "crashWatchdog.dialog.title": "<PERSON>go deu errado", "crashWatchdog.dialog.urlUnknown": "Desconhecido", "desktop.activityMonitor.all": "Todos", "desktop.activityMonitor.cpuPercent": "% CPU", "desktop.activityMonitor.cpuPercentDescription": "A fração de tempo de CPU disponível usada pelo processo.", "desktop.activityMonitor.cpuTime": "Tempo de CPU", "desktop.activityMonitor.cpuTimeDescription": "O tempo total de processamento da CPU em segundos desde o início do processo.", "desktop.activityMonitor.creationTime": "<PERSON><PERSON><PERSON>", "desktop.activityMonitor.creationTimeDescription": "A quantidade de tempo desde que o processo foi criado.", "desktop.activityMonitor.frames": "Quadros", "desktop.activityMonitor.framesDescription": "O número de quadros gerenciados pelo processo. Muitos processos de renderização são responsáveis por vários quadros.", "desktop.activityMonitor.hidden": "Oculto", "desktop.activityMonitor.hideColumns": "O<PERSON>lta<PERSON> colunas", "desktop.activityMonitor.hideFilters": "Ocultar filtros", "desktop.activityMonitor.idleWakeupsPerSecond": "Despertares ociosos", "desktop.activityMonitor.idleWakeupsPerSecondDescription": "O número de vezes que o processo despertou a CPU desde a última atualização do monitor de atividade.", "desktop.activityMonitor.loading": "Carregando dados de atividade", "desktop.activityMonitor.memCurrent": "Me<PERSON><PERSON><PERSON> (atual)", "desktop.activityMonitor.memCurrentDescription": "O tamanho do “conjunto de trabalho” do processo, que é o conjunto de páginas atualmente residentes na RAM. Esse número não leva em conta a compactação de memória do sistema operacional, a gestão de páginas inativas e em cache ou outras técnicas de gestão de memória. A quantidade de memória física usada pelo processo é provavelmente muito menor.", "desktop.activityMonitor.memPeak": "Memória (pico)", "desktop.activityMonitor.memPeakDescription": "O tamanho do conjunto de trabalho de pico do processo, que é a quantidade máxima de memória física usada pelo processo desde que foi iniciado. O tamanho do “conjunto de trabalho” do processo, que é o conjunto de páginas atualmente residentes na RAM. Esse número não leva em conta a compactação de memória do sistema operacional, a gestão de páginas inativas e em cache ou outras técnicas de gestão de memória. A quantidade de memória física usada pelo processo é provavelmente muito menor.", "desktop.activityMonitor.memPrivate": "Memória (particular)", "desktop.activityMonitor.memPrivateDescription": "A quantidade de memória física alocada pelo processo que não é compartilhada com outros processos, como JS heap ou conteúdo HTML.", "desktop.activityMonitor.memShared": "Memória (compartilhada)", "desktop.activityMonitor.memSharedDescription": "A quantidade de memória física alocada pelo processo que é compartilhada com outros processos, como bibliotecas compartilhadas ou arquivos mapeados.", "desktop.activityMonitor.mixed": "<PERSON><PERSON>", "desktop.activityMonitor.parentWindowId": "<PERSON> da janela principal", "desktop.activityMonitor.parentWindowIdDescription": "O identificador exclusivo da janela que contém esta guia.", "desktop.activityMonitor.pid": "PID", "desktop.activityMonitor.pidDescription": "O ID do processo, conforme usado pelo sistema operacional.", "desktop.activityMonitor.processName": "Nome do processo", "desktop.activityMonitor.showColumns": "<PERSON>rar colunas", "desktop.activityMonitor.showFilters": "Mostrar filtros", "desktop.activityMonitor.tabId": "ID da guia", "desktop.activityMonitor.tabIdDescription": "O identificador exclusivo da guia no app do Notion.", "desktop.activityMonitor.type": "Tipo", "desktop.activityMonitor.url": "URL", "desktop.activityMonitor.urlDescription": "O URL do processo. Muitos processos de renderização são responsáveis por vários quadros. Consulte a coluna Quadros para obter mais informações.", "desktop.activityMonitor.visibilityState": "Visibilidade", "desktop.activityMonitor.visibilityStateDescription": "O estado de visibilidade do processo. No caso de um processo de renderização, este será o estado de visibilidade do quadro principal.", "desktop.activityMonitor.visible": "Visível", "desktop.tabBar.backButtonLabel": "Voltar", "desktop.tabBar.closeSidebarLabel": "Fechar a barra lateral", "desktop.tabBar.closeTabLabel": "<PERSON><PERSON>r a aba {tabTitle}", "desktop.tabBar.forwardButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "desktop.tabBar.newTabButtonLabel": "Nova guia", "desktop.tabBar.openSidebarLabel": "Abrir barra lateral", "desktop.tabBar.tabSpacesLabel": "Espaços de guias", "desktopExtensions.install.failed.title": "Falha ao instalar extensão", "desktopExtensions.manage.cancel": "<PERSON><PERSON><PERSON>", "desktopExtensions.manage.disable": "Desabilitar", "desktopExtensions.manage.enable": "Habilitar", "desktopExtensions.manage.message": "O que você gostaria de fazer com {extensionTitle} {extensionVersion}?", "desktopExtensions.manage.title": "Gerenciar extensão", "desktopExtensions.manage.uninstall": "<PERSON><PERSON><PERSON><PERSON>", "desktopExtensions.manage.unload": "<PERSON><PERSON><PERSON><PERSON>", "desktopExtensions.openFailed.noPopupMessage": "Esta extensão não especificou um pop-up (action.default_popup)", "desktopExtensions.openFailed.noPopupTitle": "Falha ao abrir a extensão", "desktopExtensions.unzip.failed.badFileRead": "Falha ao ler arquivo CRX: {error}", "desktopExtensions.unzip.failed.badFileWrite": "Falha ao gravar arquivo {filePath}: {error}", "desktopExtensions.unzip.failed.badFolderCreate": "Falha ao criar a pasta de extensões em {extensionPath}: {error}", "desktopExtensions.unzip.failed.badManifest": "O manifest.json nesta extensão não pode ser analisado. Essa é uma extensão válida? O erro foi: {error}", "desktopExtensions.unzip.failed.badManifestNoName": "Nenhum nome encontrado no manifest.json", "desktopExtensions.unzip.failed.error": "Falha ao descompactar o arquivo CRX: {error}", "desktopExtensions.unzip.failed.noManifest": "Nenhum manifest.json foi encontrado no CRX. Essa é uma extensão válida?", "desktopInstaller.failedToMove.detail": "Falha ao mover o aplicativo para a pasta Aplicativos. Você precisa movê-lo manualmente.", "desktopInstaller.failedToMove.title": "Falha ao mover o aplicativo", "desktopInstaller.invalidInstallDialog.cancelButton.label": "<PERSON><PERSON><PERSON>", "desktopInstaller.invalidInstallDialog.confirmMove": "Seu aplicativo Notion não está instalado corretamente. Podemos movê-lo para a pasta Aplicativos?", "desktopInstaller.invalidInstallDialog.okButton.label": "OK", "desktopInstaller.invalidInstallDialog.title": "Instalação inválida", "desktopTopbar.appMenu.about": "Sobre o Notion", "desktopTopbar.appMenu.checkForUpdate": "Verifique se há atualizações…", "desktopTopbar.appMenu.checkForUpdate.noUpdateAvailable": "Você está usando a versão mais recente do Notion!", "desktopTopbar.appMenu.checkForUpdate.title": "Verifique se há atualizações", "desktopTopbar.appMenu.checkForUpdate.updateAvailable": "Uma nova versão disponível do Notion está sendo baixada em segundo plano. Obrigado por se manter atualizado!", "desktopTopbar.appMenu.checkForUpdate.updateCheckFailed": "O Notion não conseguiu estabelecer uma conexão com o servidor de atualização devido a um problema em sua conexão com a internet ou no próprio servidor. Tente de novo mais tarde.", "desktopTopbar.appMenu.downloadingUpdate": "<PERSON><PERSON><PERSON> ({percentage}%)", "desktopTopbar.appMenu.hide": "Ocultar o Notion", "desktopTopbar.appMenu.hideOthers": "Ocultar outros", "desktopTopbar.appMenu.preferences": "Preferências…", "desktopTopbar.appMenu.quit": "<PERSON><PERSON>", "desktopTopbar.appMenu.quitWithoutSavingTabs": "<PERSON>r sem salvar guias", "desktopTopbar.appMenu.restartToApplyUpdate": "Reiniciar para aplicar a atualização", "desktopTopbar.appMenu.services": "Serviços", "desktopTopbar.appMenu.unhide": "Mostrar tudo", "desktopTopbar.editMenu.copy": "Copiar", "desktopTopbar.editMenu.copyLinkToCurrentPage": "Copiar link para página atual", "desktopTopbar.editMenu.copyLinkToCurrentPageBlockTitle": "Copiar nome da página atual", "desktopTopbar.editMenu.cut": "Recortar", "desktopTopbar.editMenu.paste": "Colar", "desktopTopbar.editMenu.pasteAndMatchStyle": "Colar e usar mesmo estilo", "desktopTopbar.editMenu.redo": "<PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.selectAll": "Selecionar tudo", "desktopTopbar.editMenu.speech": "Fala", "desktopTopbar.editMenu.speech.startSpeaking": "Come<PERSON><PERSON> a falar", "desktopTopbar.editMenu.speech.stopSpeaking": "<PERSON><PERSON> <PERSON>", "desktopTopbar.editMenu.title": "<PERSON><PERSON>", "desktopTopbar.editMenu.undo": "<PERSON><PERSON><PERSON>", "desktopTopbar.extensionsMenu.install": "Instalar extensão...", "desktopTopbar.extensionsMenu.manage": "Gerenciar extensões", "desktopTopbar.fileMenu.close": "<PERSON><PERSON><PERSON>", "desktopTopbar.fileMenu.closeTab": "<PERSON><PERSON><PERSON>", "desktopTopbar.fileMenu.newNotionWindow": "Nova janela do Notion", "desktopTopbar.fileMenu.newTab": "Nova guia", "desktopTopbar.fileMenu.newWindow": "Nova janela", "desktopTopbar.fileMenu.print": "Imprimir...", "desktopTopbar.fileMenu.quit": "<PERSON><PERSON>", "desktopTopbar.fileMenu.quitWithoutSavingTabs": "<PERSON>r sem salvar guias", "desktopTopbar.fileMenu.reopenClosedTab": "Reabrir a última guia fechada", "desktopTopbar.fileMenu.title": "Arquivo", "desktopTopbar.helpMenu.copyInstallId": "Copiar ID de instalação", "desktopTopbar.helpMenu.disableAdvancedLogging": "Desabilitar o registro avançado e reiniciar", "desktopTopbar.helpMenu.disableDebugLogging": "Desabilitar o registro avançado e reiniciar", "desktopTopbar.helpMenu.disableHardwareAcceleration": "Desabilitar aceleração de hardware e reiniciar", "desktopTopbar.helpMenu.enableAdvancedLogging": "Habilitar o registro avançado e reiniciar", "desktopTopbar.helpMenu.enableDebugLogging": "Habilitar o registro avançado e reiniciar", "desktopTopbar.helpMenu.enableHardwareAcceleration": "Habilitar aceleração de hardware e reiniciar", "desktopTopbar.helpMenu.openActivityMonitor": "Abrir monitor de atividade", "desktopTopbar.helpMenu.openConsole": "<PERSON><PERSON><PERSON>el", "desktopTopbar.helpMenu.openHelpAndSupport": "Abrir Ajuda e documentação", "desktopTopbar.helpMenu.recordingNetLog": "Gravando registro de rede…", "desktopTopbar.helpMenu.recordNetLog": "Gravar registro de rede…", "desktopTopbar.helpMenu.recordNetLogConfirmation": "Um registro de rede está sendo gravado na pasta Downloads. Para interromper a gravação, clique no botão “Parar gravação do registro de rede” no menu Resolução de problemas ou saia do aplicativo.", "desktopTopbar.helpMenu.recordNetLogFailed": "Falha ao gravar registro de rede. Tente novamente ou inspecione os registros para obter mais informações.", "desktopTopbar.helpMenu.recordNetLogFailedMessage": "Tente novamente ou inspecione os registros para obter mais informações. O erro foi:", "desktopTopbar.helpMenu.recordNetLogFailedTitle": "Falha ao gravar registro de rede", "desktopTopbar.helpMenu.recordNetLogStop": "Parar gravação do registro de rede…", "desktopTopbar.helpMenu.recordPerformanceTrace": "<PERSON><PERSON>var registro de <PERSON>…", "desktopTopbar.helpMenu.recordPerformanceTraceConfirm": "Deseja gravar um registro de desempenho pelos próximos 30 segundos? Depois de concluído, ele será colocado na pasta Downloads.", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmCancel": "<PERSON><PERSON><PERSON>", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmOk": "Gravar regis<PERSON> de <PERSON>", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmTitle": "Quer gravar um registro de desempenho?", "desktopTopbar.helpMenu.resetAndEraseAllLocalData": "Redefinir e apagar todos os dados locais", "desktopTopbar.helpMenu.showLogsInExplorer": "Mostrar registros no Explorer", "desktopTopbar.helpMenu.showLogsInFinder": "Mostrar registros no Finder", "desktopTopbar.helpMenu.title": "<PERSON><PERSON><PERSON>", "desktopTopbar.historyMenu.historyBack": "Voltar", "desktopTopbar.historyMenu.historyForward": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.historyMenu.title": "Hist<PERSON><PERSON><PERSON>", "desktopTopbar.toggleDevTools": "Alternar Ferramentas do Desenvolvedor", "desktopTopbar.toggleWindowDevTools": "Alternar janela de Ferramentas do Desenvolvedor", "desktopTopbar.troubleshootingMenu.title": "Solução de problemas", "desktopTopbar.viewMenu.actualSize": "Tamanho real", "desktopTopbar.viewMenu.forceReload": "<PERSON><PERSON><PERSON>", "desktopTopbar.viewMenu.forceReloadDialog.cancel": "<PERSON><PERSON><PERSON>", "desktopTopbar.viewMenu.forceReloadDialog.message": "Você está offline no momento. Forçar a recarga desta página fará com que você perca o acesso a ela até ficar online novamente.", "desktopTopbar.viewMenu.forceReloadDialog.ok": "<PERSON><PERSON><PERSON><PERSON> assim mesmo", "desktopTopbar.viewMenu.forceReloadDialog.title": "Quer mesmo forçar a recarga?", "desktopTopbar.viewMenu.reload": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.viewMenu.showHideSidebar": "Mostrar/ocultar barra lateral", "desktopTopbar.viewMenu.showHideTabSpaceButton": "Mostrar/ocultar grupos de guias", "desktopTopbar.viewMenu.title": "Visualizar", "desktopTopbar.viewMenu.togglefullscreen": "Alternar tela cheia", "desktopTopbar.viewMenu.zoomIn": "<PERSON><PERSON> zoom", "desktopTopbar.viewMenu.zoomOut": "Menos zoom", "desktopTopbar.whatsNewMac.title": "Abra as Novidades do Notion para macOS", "desktopTopbar.whatsNewWindows.title": "A<PERSON> as Novidades do Notion para Windows", "desktopTopbar.windowMenu.close": "<PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.front": "<PERSON><PERSON>", "desktopTopbar.windowMenu.maximize": "Maximizar", "desktopTopbar.windowMenu.minimize": "<PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.showNextTab": "Mostrar próxima guia", "desktopTopbar.windowMenu.showPreviousTab": "Mostrar guia anterior", "desktopTopbar.windowMenu.title": "<PERSON><PERSON>", "desktopTopbar.windowMenu.zoom": "Zoom", "desktopTroubleshooting.resetData.cancel": "<PERSON><PERSON><PERSON>", "desktopTroubleshooting.resetData.closingWindows": "Fechando j<PERSON> do Notion", "desktopTroubleshooting.resetData.deletingFiles": "Excluindo arquivos", "desktopTroubleshooting.resetData.done": "Pronto", "desktopTroubleshooting.resetData.doneMessage": "O aplicativo foi redefinido.", "desktopTroubleshooting.resetData.failed": "Nosso processo de recuperação não conseguiu excluir alguns arquivos. Pedimos desculpas pelo incômodo - visite https://www.notion.so/help para obter assistência. Para forçar uma redefinição completa do aplicativo manualmente, feche o Notion completamente. Em seguida, exclua o seguinte caminho: {userDataPath}", "desktopTroubleshooting.resetData.message": "Isso excluirá todos os dados locais e internos, incluindo o cache e as configurações locais, restaurando o aplicativo Notion para um estado recém-instalado. Também irá desconectá-lo do Notion. Suas páginas e outros conteúdos no aplicativo ficarão intactos. Deseja continuar?", "desktopTroubleshooting.resetData.reset": "Redefinir todos os dados locais", "desktopTroubleshooting.resetData.restart": "Reiniciar", "desktopTroubleshooting.resetData.title": "Redefinir e apagar todos os dados locais", "desktopTroubleshooting.showLogs.error.message.mac": "O Notion encontrou um erro ao tentar mostrar os registros no Finder:", "desktopTroubleshooting.showLogs.error.message.windows": "O Notion encontrou um erro ao tentar mostrar os registros no Explorer:", "desktopTroubleshooting.showLogs.error.title": "Falha ao mostrar os registros", "desktopTroubleshooting.startRecordingNetLog": "Iniciar gravação do registro de rede", "desktopTroubleshooting.stopRecordingNetLog": "Parar gravação do registro de rede", "menuBarIcon.menu.changeCommandSearchAndNotionAiShortcut": "<PERSON><PERSON>", "menuBarIcon.menu.changeCommandSearchShortcut": "Alterar atalho da Pesquisa por Atalho", "menuBarIcon.menu.enableQuickSearch": "Ativar pesquisa rápida", "menuBarIcon.menu.keepInBackground": "Manter em segundo plano", "menuBarIcon.menu.launchPreferences": "Preferências de inicialização", "menuBarIcon.menu.openOnLogin": "Abrir o Notion no login", "menuBarIcon.menu.quitNotion": "Sair do Notion", "menuBarIcon.menu.showImmediately": "Mostrar imediatamente", "menuBarIcon.menu.showNotionInMenuBar": "Mostrar o Notion na barra de menu", "menuBarIcon.menu.toggleCommandSearch": "<PERSON><PERSON><PERSON> por <PERSON>ho", "menuBarIcon.menu.toggleNotionAi": "Ativar IA do Notion", "openAtLogin.dialog.detail": "{operatingSystem} impediu que o Notion definisse a configuração \"Abrir no login\". Isso geralmente acontece quando a inicialização do Notion é definida nas configurações do sistema ou quando você não tem autorizações suficientes. Você ainda pode definir essa configuração manualmente nas configurações do sistema.", "openAtLogin.dialog.title": "A<PERSON>r no login", "tabSpaces.deleteDialog.cancelButton": "<PERSON><PERSON><PERSON>", "tabSpaces.deleteDialog.deleteButton": "Excluir", "tabSpaces.deleteDialog.detail": "<PERSON><PERSON> as guias deste grupo de guias serão desagrupadas.", "tabSpaces.deleteDialog.title": "Excluir o grupo de guias “{title}”?", "tabSpaces.snackbar.switchedToTabGroup": "Trocado para {title}", "tabSpaces.snackbar.switchedToUngroupedTabs": "Trocado para guias não agrupadas", "tabSpaces.snackbar.tabGroupPlaceholder": "Grupo de guias", "updatePrompt.detail": "Gostaria de instalar agora? Reabriremos suas janelas e guias logo após.", "updatePrompt.installAndRelaunch": "Instalar e reabrir", "updatePrompt.message": "Está disponível uma nova versão do Notion!", "updatePrompt.remindMeLater": "Lemb<PERSON> mais tarde", "window.closeDialog.cancelButton": "<PERSON><PERSON><PERSON>", "window.closeDialog.confirmButton": "<PERSON><PERSON><PERSON>", "window.closeDialog.title.app": "<PERSON><PERSON><PERSON> o Notion?", "window.closeDialog.title.tab": "<PERSON><PERSON><PERSON> guia do Notion?", "window.closeDialog.title.window": "<PERSON><PERSON><PERSON> jane<PERSON> do Notion?", "window.loadingError.message": "Erro ao carregar o Notion. Conecte-se à internet para começar.", "window.loadingError.reload": "<PERSON><PERSON><PERSON><PERSON>", "window.movedTabSnackbarMessage": "Moveu {tabTitle} para {tabSpaceTitle}", "window.tabLoadingError.cancel": "<PERSON><PERSON><PERSON>", "window.tabMenu.closeOtherTabs": "<PERSON><PERSON><PERSON> guias", "window.tabMenu.closeTab": "<PERSON><PERSON><PERSON>", "window.tabMenu.closeTabsToLeft": "<PERSON><PERSON><PERSON> g<PERSON> à esquerda", "window.tabMenu.closeTabsToRight": "<PERSON><PERSON>r gui<PERSON> à direita", "window.tabMenu.copyLink": "Copiar link", "window.tabMenu.duplicateTab": "Duplicar guia", "window.tabMenu.moveTo": "Mover para", "window.tabMenu.moveToNewWindow": "Mover guia para nova janela", "window.tabMenu.moveToSubmenuNewWindow": "Nova janela", "window.tabMenu.pinTab": "Fixar guia", "window.tabMenu.refresh": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>a", "window.tabMenu.reopenClosedTab": "Reabrir a última guia fechada", "window.tabMenu.replacePinnedTabUrl": "Substituir o URL fixado pelo atual", "window.tabMenu.returnToPinnedTabUrl": "Voltar para o URL fixado", "window.tabMenu.ungroupTab": "Desagrupar guia", "window.tabMenu.unpinTab": "Desafixar guia", "window.tabTitlePlaceholder": "<PERSON><PERSON><PERSON>", "window.ungroupedTabSnackbarMessage": "{tabTitle} desagrupada"}