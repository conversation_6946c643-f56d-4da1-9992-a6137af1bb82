{"activityMonitor.copyDiagnosticInformation": "Kopioi diagnostiik<PERSON>", "activityMonitor.copyExecutablePath": "<PERSON><PERSON><PERSON> su<PERSON>", "activityMonitor.copyUrl": "Kopioi URL-osoite", "activityMonitor.forceKillProcess": "Pakota lopettam<PERSON>", "activityMonitor.inspectActivityMonitor": "<PERSON><PERSON><PERSON>", "activityMonitor.killProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "activityMonitor.openDevTools": "<PERSON><PERSON>", "activityMonitor.reload": "<PERSON><PERSON><PERSON>", "clientPlaceholder.placeholderDescription": "Näytä tässä ikkunassa napsauttamalla", "clientPlaceholder.placeholderTitle": "Tällä hetkellä sisältöä katsellaan toisessa ikkunassa", "commandSearch.window.title": "Notion – komentohaku", "crashWatchdog.dialog.abnormalExit": "Epänormaali poistuminen: sivu sulkeutui nollasta poik<PERSON><PERSON><PERSON> poistumiskoodilla.", "crashWatchdog.dialog.buttonCloseTab": "<PERSON><PERSON> v<PERSON>", "crashWatchdog.dialog.buttonCloseWindow": "Sulje i<PERSON>kuna", "crashWatchdog.dialog.buttonRestartApp": "Käynnistä Notion uudelleen", "crashWatchdog.dialog.crashed": "Kaatui: sivu kaatui tuntematto<PERSON>ta sy<PERSON>tä.", "crashWatchdog.dialog.details": "Sivun URL-osoite: {url} Syy: {reason} Poistumiskoodi: {exitCode}", "crashWatchdog.dialog.integrityFailure": "Eheyden epäonnistuminen: sivu ei läpäissyt koodin eheyden tarkistuk<PERSON>.", "crashWatchdog.dialog.killed": "Tapettu: sivu tapettiin ulkoisella prosessilla.", "crashWatchdog.dialog.launchFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> epäonnistui: prosessin käynnistys epäonnist<PERSON>.", "crashWatchdog.dialog.message": "Jokin meni vikaan tätä välilehteä näytettäessä, emmek<PERSON> voineet palauttaa sitä automaattisesti.", "crashWatchdog.dialog.oom": "OOM: sivun muisti lo<PERSON>, ja sivu kaatui.", "crashWatchdog.dialog.title": "<PERSON><PERSON> meni vikaan", "crashWatchdog.dialog.urlUnknown": "Tuntematon", "desktop.activityMonitor.all": "<PERSON><PERSON><PERSON>", "desktop.activityMonitor.cpuPercent": "% suorittimesta", "desktop.activityMonitor.cpuPercentDescription": "Prosessin k<PERSON>ämän käytettävissä olevan suorittimen ajan osu<PERSON>.", "desktop.activityMonitor.cpuTime": "<PERSON><PERSON>tti<PERSON> aika", "desktop.activityMonitor.cpuTimeDescription": "Prosessin k<PERSON>ynnistämisen jälkeen käytetyn suorittimen ajan koko<PERSON>ssekunnit.", "desktop.activityMonitor.creationTime": "<PERSON><PERSON><PERSON>", "desktop.activityMonitor.creationTimeDescription": "Prosessin luomisesta kulunut aika.", "desktop.activityMonitor.frames": "<PERSON><PERSON><PERSON><PERSON>", "desktop.activityMonitor.framesDescription": "Prosessin hallinnoimien ruutujen määrä. Useat renderöintiprosessit ovat vastuussa useista ruuduista.", "desktop.activityMonitor.hidden": "Piilotettu", "desktop.activityMonitor.hideColumns": "<PERSON><PERSON>ta sa<PERSON>", "desktop.activityMonitor.hideFilters": "<PERSON><PERSON><PERSON>", "desktop.activityMonitor.idleWakeupsPerSecond": "<PERSON><PERSON><PERSON><PERSON>", "desktop.activityMonitor.idleWakeupsPerSecondDescription": "<PERSON><PERSON><PERSON> monta kertaa prosessi on herättänyt suorittimen viimeisimmän toiminnanseurannan päivityksen jälk<PERSON>.", "desktop.activityMonitor.loading": "Ladata<PERSON> to<PERSON>", "desktop.activityMonitor.memCurrent": "<PERSON><PERSON><PERSON> (nykyinen)", "desktop.activityMonitor.memCurrentDescription": "<PERSON>ses<PERSON> \"työjou<PERSON>\" koko, joka on RAM-muistissa tällä hetkellä sijaitsevien sivujen joukko. Tämä numero ei ota huomioon käyttöjärjestelmän muistin pakka<PERSON>sta, passiivisten ja välimuistissa olevien sivujen hallintaa tai muita muistinhallintatekniikoita. Prosessin käyttämän fyysisen muistin määrä on todennäköisesti paljon pienempi.", "desktop.activityMonitor.memPeak": "<PERSON><PERSON><PERSON> (huippu)", "desktop.activityMonitor.memPeakDescription": "Prosessin työjoukon huippukoko, joka on prosessin käynnistämisen jälkeen käyttämän fyysisen muistin enimmäismäärä. <PERSON>sessin \"työjoukon\" koko, joka on RAM-muistissa tällä hetkellä sijaitsevien sivujen joukko. Tämä numero ei ota huomioon käyttöjärjestelmän muistin pakkaamista, passiivisten ja välimuistissa olevien sivujen hallintaa tai muita muistinhallintatekniikoita. Prosessin käyttämän fyysisen muistin määrä on todennäköisesti paljon pienempi.", "desktop.activityMonitor.memPrivate": "<PERSON><PERSON><PERSON> (yks<PERSON>inen)", "desktop.activityMonitor.memPrivateDescription": "<PERSON><PERSON><PERSON> fyysisen muist<PERSON> m<PERSON>, jota ei jaeta muiden prosessien, kuten JS-keon tai HTML-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kanssa.", "desktop.activityMonitor.memShared": "<PERSON><PERSON><PERSON> (jae<PERSON>u)", "desktop.activityMonitor.memSharedDescription": "<PERSON><PERSON><PERSON> fyysisen muist<PERSON>, joka jae<PERSON> muiden prosessien, kuten jaettujen kirjas<PERSON>jen tai y<PERSON><PERSON>, kans<PERSON>.", "desktop.activityMonitor.mixed": "<PERSON><PERSON><PERSON><PERSON>", "desktop.activityMonitor.parentWindowId": "Ylätason ikkunan tunniste", "desktop.activityMonitor.parentWindowIdDescription": "Tämän välilehden sisältävän ikkunan yksilöllinen tunniste.", "desktop.activityMonitor.pid": "Prosessitunnus", "desktop.activityMonitor.pidDescription": "Käyttöjärjestelmän käyttämä prosessin tunnus.", "desktop.activityMonitor.processName": "<PERSON><PERSON><PERSON> nimi", "desktop.activityMonitor.showColumns": "Näytä sarakkeet", "desktop.activityMonitor.showFilters": "Näytä <PERSON>", "desktop.activityMonitor.tabId": "<PERSON><PERSON><PERSON>le<PERSON><PERSON> tunnus", "desktop.activityMonitor.tabIdDescription": "Välilehden yksilöllinen tunniste Notion-sovelluksessa.", "desktop.activityMonitor.type": "Tyyppi", "desktop.activityMonitor.url": "URL-osoite", "desktop.activityMonitor.urlDescription": "Prosessin URL-osoite. Useat renderöintiprosessit ovat vastuussa useista ruuduista. Katso lisätietoja Ruudut-sarakkeesta.", "desktop.activityMonitor.visibilityState": "Näkyvyys", "desktop.activityMonitor.visibilityStateDescription": "<PERSON><PERSON><PERSON>. <PERSON><PERSON> on render<PERSON><PERSON><PERSON><PERSON><PERSON>, t<PERSON><PERSON><PERSON> on pä<PERSON>ruudun näkyvyystila.", "desktop.activityMonitor.visible": "Näkyvä", "desktop.tabBar.backButtonLabel": "<PERSON><PERSON><PERSON>", "desktop.tabBar.closeSidebarLabel": "<PERSON><PERSON>", "desktop.tabBar.closeTabLabel": "<PERSON><PERSON>, {tabTitle}", "desktop.tabBar.forwardButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktop.tabBar.newTabButtonLabel": "<PERSON>us<PERSON> vä<PERSON>lehti", "desktop.tabBar.openSidebarLabel": "<PERSON><PERSON>", "desktop.tabBar.tabSpacesLabel": "Välilehden tilat", "desktopExtensions.install.failed.title": "Laajennuksen asentaminen epäonnistui", "desktopExtensions.manage.cancel": "Peruuta", "desktopExtensions.manage.disable": "Poista käytöstä", "desktopExtensions.manage.enable": "<PERSON><PERSON>", "desktopExtensions.manage.message": "Mitä haluat tehdä laajennukselle {extensionTitle} {extensionVersion}?", "desktopExtensions.manage.title": "Hallitse la<PERSON>", "desktopExtensions.manage.uninstall": "Po<PERSON> asennus", "desktopExtensions.manage.unload": "Poista", "desktopExtensions.openFailed.noPopupMessage": "Tämä laajennus ei määrittänyt ponnahdusikkunaa (action.default_popup)", "desktopExtensions.openFailed.noPopupTitle": "Laajennuksen a<PERSON>aminen epäonnistui", "desktopExtensions.unzip.failed.badFileRead": "CRX-tiedost<PERSON> lukeminen epäonnistui: {error}", "desktopExtensions.unzip.failed.badFileWrite": "Tiedoston {filePath} kirjoittaminen epäonnistui: {error}", "desktopExtensions.unzip.failed.badFolderCreate": "Laajennuskansion luominen polkuun {extensionPath} epäonnistui: {error}", "desktopExtensions.unzip.failed.badManifest": "T<PERSON><PERSON>än laajennuksen manifest.json-tiedostoa ei voitu jäsentää. Onko laajennus kelvollinen? V<PERSON><PERSON> o<PERSON>: {error}", "desktopExtensions.unzip.failed.badManifestNoName": "manifest.json-tiedostosta ei l<PERSON> ni<PERSON>ä", "desktopExtensions.unzip.failed.error": "CRX-tiedoston purkaminen epäonnistui: {error}", "desktopExtensions.unzip.failed.noManifest": "CRX-tiedostosta ei löydy manifest.json-tiedostoa. <PERSON><PERSON> kelvo<PERSON>?", "desktopInstaller.failedToMove.detail": "Sovelluksen siirtäminen Sovellukset-kansioon epäonnistui. Siirrä se itse.", "desktopInstaller.failedToMove.title": "So<PERSON>luksen siirtäminen epäonnistui", "desktopInstaller.invalidInstallDialog.cancelButton.label": "Peruuta", "desktopInstaller.invalidInstallDialog.confirmMove": "Notion-sovellustasi ei ole asennettu oikein. Voimmeko siirtää Notion-sovelluksesi Sovellukset-kansioon?", "desktopInstaller.invalidInstallDialog.okButton.label": "OK", "desktopInstaller.invalidInstallDialog.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> asen<PERSON>", "desktopTopbar.appMenu.about": "Tietoja Notionista", "desktopTopbar.appMenu.checkForUpdate": "Tarkistetaan p<PERSON>ivityksiä…", "desktopTopbar.appMenu.checkForUpdate.noUpdateAvailable": "<PERSON><PERSON><PERSON> on käytössä Notionin uusin versio!", "desktopTopbar.appMenu.checkForUpdate.title": "Tarkistetaan p<PERSON>ivityksiä", "desktopTopbar.appMenu.checkForUpdate.updateAvailable": "Notionin uusi versio on saatavilla ja sitä ladataan taustalla. <PERSON><PERSON><PERSON>, että olet ajan tasalla!", "desktopTopbar.appMenu.checkForUpdate.updateCheckFailed": "Notion ei onnistunut muodostamaan yhteyttä päivityspalvelimen kanssa joko Internet-yhteytesi tai itse päivityspalvelimen ongelman vuoksi. Yritä uudelleen myöhemmin.", "desktopTopbar.appMenu.downloadingUpdate": "Ladataan päivitystä ({percentage} %)", "desktopTopbar.appMenu.hide": "Piilota Notion", "desktopTopbar.appMenu.hideOthers": "<PERSON><PERSON><PERSON> muut", "desktopTopbar.appMenu.preferences": "<PERSON><PERSON><PERSON><PERSON>…", "desktopTopbar.appMenu.quit": "Lopeta", "desktopTopbar.appMenu.quitWithoutSavingTabs": "<PERSON><PERSON> välilehti<PERSON>", "desktopTopbar.appMenu.restartToApplyUpdate": "Käynnistä uudelleen päivityksen käyttöön ottamiseksi", "desktopTopbar.appMenu.services": "Palvelut", "desktopTopbar.appMenu.unhide": "Näytä kaikki", "desktopTopbar.editMenu.copy": "Ko<PERSON>i", "desktopTopbar.editMenu.copyLinkToCurrentPage": "<PERSON><PERSON><PERSON> n<PERSON> sivulle", "desktopTopbar.editMenu.copyLinkToCurrentPageBlockTitle": "<PERSON><PERSON><PERSON> n<PERSON> sivun nimi", "desktopTopbar.editMenu.cut": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.paste": "Liit<PERSON>", "desktopTopbar.editMenu.pasteAndMatchStyle": "<PERSON>it<PERSON> ja muuta tyyli <PERSON>i", "desktopTopbar.editMenu.redo": "Toista", "desktopTopbar.editMenu.selectAll": "<PERSON><PERSON><PERSON> kaikki", "desktopTopbar.editMenu.speech": "<PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.speech.startSpeaking": "<PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.speech.stopSpeaking": "<PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.title": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.undo": "<PERSON><PERSON><PERSON>", "desktopTopbar.extensionsMenu.install": "<PERSON><PERSON><PERSON>...", "desktopTopbar.extensionsMenu.manage": "Hallitse laajennuksia", "desktopTopbar.fileMenu.close": "Sulje i<PERSON>kuna", "desktopTopbar.fileMenu.closeTab": "<PERSON><PERSON> v<PERSON>", "desktopTopbar.fileMenu.newNotionWindow": "Uusi Notion-ikkuna", "desktopTopbar.fileMenu.newTab": "<PERSON>us<PERSON> vä<PERSON>lehti", "desktopTopbar.fileMenu.newWindow": "Uusi i<PERSON>kuna", "desktopTopbar.fileMenu.print": "<PERSON><PERSON><PERSON>…", "desktopTopbar.fileMenu.quit": "<PERSON><PERSON><PERSON>", "desktopTopbar.fileMenu.quitWithoutSavingTabs": "<PERSON><PERSON><PERSON> tall<PERSON> välilehtiä", "desktopTopbar.fileMenu.reopenClosedTab": "Avaa viimeksi suljettu välilehti uudelleen", "desktopTopbar.fileMenu.title": "Tiedosto", "desktopTopbar.helpMenu.copyInstallId": "<PERSON><PERSON><PERSON> as<PERSON>", "desktopTopbar.helpMenu.disableAdvancedLogging": "Poista edistynyt kirjaus käytöstä ja käynnistä uudelleen", "desktopTopbar.helpMenu.disableDebugLogging": "Poista edistynyt kirjaus käytöstä ja käynnistä uudelleen", "desktopTopbar.helpMenu.disableHardwareAcceleration": "Poista laitteistokiihdytys käytöstä ja käynnistä uudelleen", "desktopTopbar.helpMenu.enableAdvancedLogging": "<PERSON><PERSON> edist<PERSON>yt kirjaus kä<PERSON>töön ja käynnistä uudelleen", "desktopTopbar.helpMenu.enableDebugLogging": "<PERSON><PERSON> edist<PERSON>yt kirjaus kä<PERSON>töön ja käynnistä uudelleen", "desktopTopbar.helpMenu.enableHardwareAcceleration": "<PERSON><PERSON>okiihdytys k<PERSON>yttöön ja käynnistä uudelleen", "desktopTopbar.helpMenu.openActivityMonitor": "<PERSON><PERSON> to<PERSON>a", "desktopTopbar.helpMenu.openConsole": "<PERSON><PERSON> k<PERSON>", "desktopTopbar.helpMenu.openHelpAndSupport": "<PERSON>a ohje ja dokumentaatio", "desktopTopbar.helpMenu.recordingNetLog": "Tallennetaan verkkolokia...", "desktopTopbar.helpMenu.recordNetLog": "Tallenna verkkoloki...", "desktopTopbar.helpMenu.recordNetLogConfirmation": "Lataukset-kansioon tallennetaan nyt verkkolokia. Lopeta tallennus napsauttamalla Lopeta verkkolokin tallennus -pain<PERSON>tta <PERSON>nmääritys-valikossa tai sulkemalla sovellus.", "desktopTopbar.helpMenu.recordNetLogFailed": "Verkkolokin tallennus epäonnistui. Yritä uudelleen tai tarkista lokit saadaksesi lisätietoja.", "desktopTopbar.helpMenu.recordNetLogFailedMessage": "Yritä uudelleen tai tarkista lokit saadaksesi lisätietoja. V<PERSON>he oli <PERSON>:", "desktopTopbar.helpMenu.recordNetLogFailedTitle": "Verkkolokin tallennus ep<PERSON>", "desktopTopbar.helpMenu.recordNetLogStop": "<PERSON><PERSON><PERSON> ve<PERSON> tallennus...", "desktopTopbar.helpMenu.recordPerformanceTrace": "<PERSON><PERSON><PERSON> se<PERSON>...", "desktopTopbar.helpMenu.recordPerformanceTraceConfirm": "Haluatko tallentaa suorituskyvyn seurannan seuraaville 30 sekunnille? Kun val<PERSON>, se si<PERSON><PERSON><PERSON><PERSON>-kansioon.", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmCancel": "Peruuta", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmOk": "<PERSON><PERSON><PERSON>", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON>?", "desktopTopbar.helpMenu.resetAndEraseAllLocalData": "<PERSON><PERSON>a ja poista kaikki pai<PERSON> tiedot", "desktopTopbar.helpMenu.showLogsInExplorer": "Näytä lokit resurssienhallinnassa", "desktopTopbar.helpMenu.showLogsInFinder": "Näytä lokit Finderissa", "desktopTopbar.helpMenu.title": "<PERSON><PERSON>", "desktopTopbar.historyMenu.historyBack": "<PERSON><PERSON><PERSON>", "desktopTopbar.historyMenu.historyForward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.historyMenu.title": "Historia", "desktopTopbar.toggleDevTools": "Vaihda kehittäjän työkalujen tilaa", "desktopTopbar.toggleWindowDevTools": "Vaihda ikkunoiden kehittäjän työkalujen tilaa", "desktopTopbar.troubleshootingMenu.title": "Vianetsintä", "desktopTopbar.viewMenu.actualSize": "Todellinen koko", "desktopTopbar.viewMenu.forceReload": "Pakota uudelleenlataus", "desktopTopbar.viewMenu.forceReloadDialog.cancel": "Peruuta", "desktopTopbar.viewMenu.forceReloadDialog.message": "<PERSON>t tällä hetkellä offline-tilassa. <PERSON><PERSON> pakotat tämän sivun uudelleenlatauksen, et voi käyttää sitä ennen kuin olet taas online-tilassa.", "desktopTopbar.viewMenu.forceReloadDialog.ok": "Lataa silti uude<PERSON>en", "desktopTopbar.viewMenu.forceReloadDialog.title": "<PERSON><PERSON><PERSON><PERSON> varmasti pakottaa uudelleenlatauksen?", "desktopTopbar.viewMenu.reload": "<PERSON><PERSON><PERSON>", "desktopTopbar.viewMenu.showHideSidebar": "Näytä/pii<PERSON>a si<PERSON>", "desktopTopbar.viewMenu.showHideTabSpaceButton": "Näytä/piilota välilehtiryhmät", "desktopTopbar.viewMenu.title": "Näkymä", "desktopTopbar.viewMenu.togglefullscreen": "Vaihda koko n<PERSON>ön tilaa", "desktopTopbar.viewMenu.zoomIn": "<PERSON><PERSON><PERSON>", "desktopTopbar.viewMenu.zoomOut": "Pienennä", "desktopTopbar.whatsNewMac.title": "Avaa Mitä uutta Notion macOS:lle tuo tullessaan", "desktopTopbar.whatsNewWindows.title": "<PERSON>a Mitä uutta Notion Windowsille tuo tullessaan", "desktopTopbar.windowMenu.close": "Sulje", "desktopTopbar.windowMenu.front": "Etuosa", "desktopTopbar.windowMenu.maximize": "<PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.minimize": "Pienennä", "desktopTopbar.windowMenu.showNextTab": "Näytä se<PERSON>ava välilehti", "desktopTopbar.windowMenu.showPreviousTab": "Näytä edellinen välilehti", "desktopTopbar.windowMenu.title": "Ikkuna", "desktopTopbar.windowMenu.zoom": "Zoomaa", "desktopTroubleshooting.resetData.cancel": "Peruuta", "desktopTroubleshooting.resetData.closingWindows": "Notion-ikkunoita suljetaan", "desktopTroubleshooting.resetData.deletingFiles": "<PERSON>ied<PERSON><PERSON> pois<PERSON>", "desktopTroubleshooting.resetData.done": "Val<PERSON>", "desktopTroubleshooting.resetData.doneMessage": "Sovel<PERSON> on nollattu.", "desktopTroubleshooting.resetData.failed": "Palautusprosessi ei voinut palauttaa joitakin tiedost<PERSON>. Pahoittelemme vaivaa – vieraile osoitteessa https://www.notion.so/help saadaksesi apua. Jos haluat pakottaa sovelluksen täyden nollauksen manuaalisesti, su<PERSON><PERSON> koko<PERSON>. Poista sitten seuraava polku: {userDataPath}", "desktopTroubleshooting.resetData.message": "Tämä poistaa kaikki paikalliset ja sis<PERSON><PERSON>t tiedot, mukaan lukien välimuistin ja paikalliset asetukset, ja palauttaa Notion-sovelluksen juuri asennettuun tilaan. Se myös kirjaa sinut ulos Notionista. Sivut ja muu sovelluksen sisäinen sisältö säilyvät ennallaan. Haluatko jatkaa?", "desktopTroubleshooting.resetData.reset": "<PERSON><PERSON><PERSON> kaikki pai<PERSON> tiedot", "desktopTroubleshooting.resetData.restart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopTroubleshooting.resetData.title": "<PERSON><PERSON><PERSON><PERSON> ja poistetaan kaikki pai<PERSON> tiedot", "desktopTroubleshooting.showLogs.error.message.mac": "Notion havaitsi virheen yrittäessään näyttää lokit Finderissa:", "desktopTroubleshooting.showLogs.error.message.windows": "Notion havaitsi virheen yrittäessään näyttää lokit Explorerissa:", "desktopTroubleshooting.showLogs.error.title": "<PERSON><PERSON> n<PERSON>ä<PERSON>n epäonnistui", "desktopTroubleshooting.startRecordingNetLog": "<PERSON><PERSON><PERSON> ve<PERSON> tallennus", "desktopTroubleshooting.stopRecordingNetLog": "<PERSON><PERSON><PERSON> ve<PERSON> tallennus", "menuBarIcon.menu.changeCommandSearchAndNotionAiShortcut": "Muokkaa pikanäppäimiä", "menuBarIcon.menu.changeCommandSearchShortcut": "<PERSON><PERSON><PERSON><PERSON> komento<PERSON>un pika<PERSON>pp<PERSON>", "menuBarIcon.menu.enableQuickSearch": "<PERSON>ta pika<PERSON>u k<PERSON>öö<PERSON>", "menuBarIcon.menu.keepInBackground": "Säilytä taustalla", "menuBarIcon.menu.launchPreferences": "<PERSON><PERSON>", "menuBarIcon.menu.openOnLogin": "Avaa Notion sisäänkirjautuessa", "menuBarIcon.menu.quitNotion": "Lopeta Notion", "menuBarIcon.menu.showImmediately": "Näyt<PERSON>", "menuBarIcon.menu.showNotionInMenuBar": "Näytä Notion valikkopalkissa", "menuBarIcon.menu.toggleCommandSearch": "<PERSON>a tai sulje komentohaku", "menuBarIcon.menu.toggleNotionAi": "Näytä/piilota Notion AI", "openAtLogin.dialog.detail": "{operatingSystem} esti Notionia määrittämästä Avaa sisäänkirjautuessa -asetusta. Tämä tapahtuu yleens<PERSON> silloin, kun <PERSON><PERSON> käynnistys on määritetty järjestelmäasetuksissa tai jos käyttöoikeudet eivät ole riittävät. Voit silti määrittää tämän asetuksen manuaalisesti järjestelmäasetuksissa.", "openAtLogin.dialog.title": "Avaa si<PERSON>äänkirjautuessa", "tabSpaces.deleteDialog.cancelButton": "Peruuta", "tabSpaces.deleteDialog.deleteButton": "Poista", "tabSpaces.deleteDialog.detail": "Kaikki tämän välilehtiryhmän välilehdet poistetaan ryhmästä.", "tabSpaces.deleteDialog.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> välilehtiryhmä {title}?", "tabSpaces.snackbar.switchedToTabGroup": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> {title}", "tabSpaces.snackbar.switchedToUngroupedTabs": "Vaihdettu ryhmittelemättömiin välilehtiin", "tabSpaces.snackbar.tabGroupPlaceholder": "Välilehtiryhmä", "updatePrompt.detail": "Haluatko asentaa sen nyt? Avaamme ikkunat ja välilehdet uudelleen puolestasi.", "updatePrompt.installAndRelaunch": "<PERSON><PERSON><PERSON> ja kä<PERSON>ist<PERSON> u<PERSON>en", "updatePrompt.message": "Uusi Notion-versio on saatavilla!", "updatePrompt.remindMeLater": "<PERSON><PERSON><PERSON>", "window.closeDialog.cancelButton": "Peruuta", "window.closeDialog.confirmButton": "Sulje", "window.closeDialog.title.app": "Suljetaanko Notion?", "window.closeDialog.title.tab": "Suljetaanko Notion-välilehti?", "window.closeDialog.title.window": "Suljetaanko Notion-ikkuna?", "window.loadingError.message": "<PERSON><PERSON><PERSON>, aloita muodos<PERSON>a yht<PERSON>.", "window.loadingError.reload": "<PERSON><PERSON><PERSON>", "window.movedTabSnackbarMessage": "{tabTitle} siir<PERSON><PERSON> kohteeseen {tabSpaceTitle}", "window.tabLoadingError.cancel": "Peruuta", "window.tabMenu.closeOtherTabs": "<PERSON><PERSON> muut välilehdet", "window.tabMenu.closeTab": "<PERSON><PERSON> v<PERSON>", "window.tabMenu.closeTabsToLeft": "<PERSON>je <PERSON>la olevat välilehdet", "window.tabMenu.closeTabsToRight": "Sulje oikealla olevat välilehdet", "window.tabMenu.copyLink": "<PERSON><PERSON><PERSON>", "window.tabMenu.duplicateTab": "Kopioi välilehti", "window.tabMenu.moveTo": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>n", "window.tabMenu.moveToNewWindow": "Siirrä välilehti uuteen ikkunaan", "window.tabMenu.moveToSubmenuNewWindow": "Uusi i<PERSON>kuna", "window.tabMenu.pinTab": "Kiinnitä välilehti", "window.tabMenu.refresh": "Päivitä välilehti", "window.tabMenu.reopenClosedTab": "Avaa viimeksi suljettu välilehti uudelleen", "window.tabMenu.replacePinnedTabUrl": "<PERSON><PERSON>a kiinnitetty URL nykyisellä", "window.tabMenu.returnToPinnedTabUrl": "<PERSON><PERSON><PERSON> kii<PERSON><PERSON><PERSON> U<PERSON>-o<PERSON>itteeseen", "window.tabMenu.ungroupTab": "Poista välilehden ryhmitys", "window.tabMenu.unpinTab": "Poista välilehden kiinnitys", "window.tabTitlePlaceholder": "Välilehti", "window.ungroupedTabSnackbarMessage": "Ry<PERSON><PERSON>lemätön {tabTitle}"}