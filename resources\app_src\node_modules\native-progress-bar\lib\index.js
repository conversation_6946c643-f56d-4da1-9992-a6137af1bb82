"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProgressBar = void 0;
const bindings_1 = __importDefault(require("bindings"));
const native = (0, bindings_1.default)("progress_bar");
const activeProgressBars = new Set();
process.on("exit", () => {
    // Attempt to close any remaining progress bars
    for (const progressBar of activeProgressBars) {
        if (progressBar.isClosed) {
            continue;
        }
        try {
            progressBar.close();
        }
        catch (e) {
            // Ignore errors during cleanup
        }
    }
    activeProgressBars.clear();
});
const DEFAULT_ARGUMENTS = {
    title: "Progress",
    message: "",
    style: "default",
    progress: 0,
    buttons: [],
    onClose: () => { },
};
class ProgressBar {
    title;
    style;
    handle = null;
    isClosed = false;
    onClose;
    /**
     * The progress of the progress bar, between 0 and 100
     */
    get progress() {
        return this._progress;
    }
    set progress(value) {
        if (value === this._progress) {
            return;
        }
        if (value < 0 || value > 100) {
            throw new Error("Progress must be between 0 and 100");
        }
        this._progress = value;
        this.update();
    }
    _progress = 0;
    /**
     * The message of the progress bar
     */
    get message() {
        return this._message;
    }
    set message(value) {
        if (value === this._message) {
            return;
        }
        this._message = value;
        this.update();
    }
    _message = "";
    /**
     * The buttons of the progress bar. Can be dynamically set.
     */
    get buttons() {
        return this._buttons;
    }
    set buttons(value) {
        if (value === this._buttons) {
            return;
        }
        this._buttons = value;
        this.update({ buttons: value });
    }
    _buttons = [];
    _internalButtons = [];
    constructor(args = DEFAULT_ARGUMENTS) {
        const title = args.title || DEFAULT_ARGUMENTS.title;
        const style = args.style || DEFAULT_ARGUMENTS.style;
        this._buttons = args.buttons || DEFAULT_ARGUMENTS.buttons;
        this._message = args.message || DEFAULT_ARGUMENTS.message;
        this.onClose = args.onClose;
        this.handle = native.showProgressBar(title, this._message, style, this.getButtons(this._buttons));
        // Prevent general GC from closing the progress bar
        activeProgressBars.add(this);
    }
    update(args) {
        if (!this.validateHandle()) {
            return;
        }
        this._progress = args?.progress || this._progress;
        this._message = args?.message || this._message;
        const shouldUpdateButtons = this.getButtonsUpdateNecessary(args?.buttons);
        const buttons = shouldUpdateButtons ? this.getButtons(args?.buttons) : [];
        native.updateProgress(this.handle, this._progress, this._message, shouldUpdateButtons, buttons);
    }
    close() {
        if (!this.isClosed && this.handle) {
            native.closeProgress(this.handle);
            this.isClosed = true;
            this.handle = null;
            this.onClose?.(this);
            // Allow general GC to close the progress bar
            activeProgressBars.delete(this);
        }
    }
    validateHandle() {
        if (this.isClosed || !this.handle) {
            return false;
        }
        return true;
    }
    /**
     * Get updated button elements with a transformed click
     */
    getButtons(newButtons) {
        if (!newButtons || newButtons.length === 0) {
            return [];
        }
        this._internalButtons = newButtons.map((button) => ({
            label: button.label,
            click: () => {
                if (button.click) {
                    button.click(this);
                }
            },
            source: button,
        }));
        return this._internalButtons;
    }
    /**
     * Determine whether or not we even need to create a new buttons element.
     *
     * @returns {boolean} true if we need to update, false otherwise
     */
    getButtonsUpdateNecessary(newButtons) {
        if (!newButtons) {
            return false;
        }
        if (newButtons.length !== this._internalButtons.length) {
            return true;
        }
        for (let i = 0; i < newButtons.length; i++) {
            if (newButtons[i] !== this._internalButtons[i].source) {
                return true;
            }
        }
        return false;
    }
}
exports.ProgressBar = ProgressBar;
