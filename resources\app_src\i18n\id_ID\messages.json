{"activityMonitor.copyDiagnosticInformation": "Salin Informasi Diagnostik", "activityMonitor.copyExecutablePath": "<PERSON><PERSON> yang <PERSON>", "activityMonitor.copyUrl": "Salin URL", "activityMonitor.forceKillProcess": "Hentikan Paksa Proses", "activityMonitor.inspectActivityMonitor": "Periksa Pemantau Aktivitas", "activityMonitor.killProcess": "Hentikan Proses", "activityMonitor.openDevTools": "<PERSON><PERSON> DevTools", "activityMonitor.reload": "<PERSON><PERSON> ul<PERSON>", "clientPlaceholder.placeholderDescription": "Klik untuk menampilkan di jendela ini", "clientPlaceholder.placeholderTitle": "Sedang melihat konten di jendela lain", "commandSearch.window.title": "Notion - Command Search", "crashWatchdog.dialog.abnormalExit": "Eksit Abnormal: <PERSON><PERSON> keluar dengan kode keluar yang bukan nol.", "crashWatchdog.dialog.buttonCloseTab": "<PERSON><PERSON><PERSON>", "crashWatchdog.dialog.buttonCloseWindow": "<PERSON><PERSON><PERSON>", "crashWatchdog.dialog.buttonRestartApp": "<PERSON><PERSON>", "crashWatchdog.dialog.crashed": "Crash: <PERSON><PERSON> gagal dimuat karena alasan yang tidak diketahui.", "crashWatchdog.dialog.details": "URL Halaman: {url} Alasan: {reason} Kode eksit: {exitCode}", "crashWatchdog.dialog.integrityFailure": "Kegagalan Integritas: <PERSON><PERSON> gagal memeriksa integritas kode.", "crashWatchdog.dialog.killed": "Ditutup: <PERSON><PERSON> ditutup oleh proses eksternal.", "crashWatchdog.dialog.launchFailed": "Peluncuran Gagal: <PERSON><PERSON> gagal diluncurkan.", "crashWatchdog.dialog.message": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat menampilkan tab ini dan kami tidak dapat memulihkannya secara otomatis.", "crashWatchdog.dialog.oom": "OOM: <PERSON><PERSON> keh<PERSON> memori dan men<PERSON> crash.", "crashWatchdog.dialog.title": "<PERSON><PERSON><PERSON><PERSON>", "crashWatchdog.dialog.urlUnknown": "Tidak diketahui", "desktop.activityMonitor.all": "<PERSON><PERSON><PERSON>", "desktop.activityMonitor.cpuPercent": "% CPU", "desktop.activityMonitor.cpuPercentDescription": "Porsi waktu CPU yang tersedia yang digunakan oleh proses.", "desktop.activityMonitor.cpuTime": "Waktu CPU", "desktop.activityMonitor.cpuTimeDescription": "Total detik waktu CPU yang digunakan sejak proses dimulai.", "desktop.activityMonitor.creationTime": "Dibuat", "desktop.activityMonitor.creationTimeDescription": "<PERSON><PERSON><PERSON> waktu sejak proses dibuat.", "desktop.activityMonitor.frames": "<PERSON>ame", "desktop.activityMonitor.framesDescription": "<PERSON><PERSON><PERSON> frame yang di<PERSON>ola o<PERSON>h proses. Banyak proses perender mengel<PERSON> beber<PERSON> frame.", "desktop.activityMonitor.hidden": "Tersemb<PERSON><PERSON>", "desktop.activityMonitor.hideColumns": "Sembunyikan Kolom", "desktop.activityMonitor.hideFilters": "Sembunyikan Filter", "desktop.activityMonitor.idleWakeupsPerSecond": "Idle Wakeup", "desktop.activityMonitor.idleWakeupsPerSecondDescription": "<PERSON><PERSON><PERSON><PERSON> proses membangunkan CPU sejak pembaruan pemantau aktivitas terakhir.", "desktop.activityMonitor.loading": "Memuat data aktivitas", "desktop.activityMonitor.memCurrent": "Memori (Saat ini)", "desktop.activityMonitor.memCurrentDescription": "Ukuran “working set” dari suatu proses mengacu pada kumpulan halaman memori yang saat ini berada di RAM. Angka ini tidak memperhitungkan kompresi memori oleh sistem operasi, manajemen halaman yang tidak aktif dan dicache, atau teknik manajemen memori lainnya. Ju<PERSON>lah memori fisik yang benar-benar digunakan oleh proses tersebutkemungkinan jauh lebih kecil.", "desktop.activityMonitor.memPeak": "Memori (Puncak)", "desktop.activityMonitor.memPeakDescription": "Ukuran working set puncak dari suatu proses adalah jumlah maksimum memori fisik yang digunakan oleh proses tersebut sejak pertama kali dijalankan. “Working set” sendiri merujuk pada kumpulan halaman memori yang saat ini berada di RAM. Angka ini tidak memperhitungkan kompresi memori sistem operasi, manajemen halaman yang tidak aktif maupun yang dicache, serta teknik manajemen memori lainnya. <PERSON><PERSON> karena itu, jumlah memori fisik yang benar-benar digunakan oleh proses tersebut kemungkinan jauh lebih kecil.", "desktop.activityMonitor.memPrivate": "<PERSON><PERSON><PERSON> (Privat)", "desktop.activityMonitor.memPrivateDescription": "<PERSON><PERSON><PERSON> memori fisik yang dialokasikan oleh proses yang tidak digunakan bersama proses lain, seperti heap JS atau konten HTML.", "desktop.activityMonitor.memShared": "<PERSON><PERSON><PERSON> (Bersama)", "desktop.activityMonitor.memSharedDescription": "<PERSON><PERSON><PERSON> memori fisik yang dialokasikan oleh proses yang digunakan bersama proses lain, seperti pustaka bersama atau file yang dipetakan.", "desktop.activityMonitor.mixed": "<PERSON>ura<PERSON>", "desktop.activityMonitor.parentWindowId": "ID Jendela Induk", "desktop.activityMonitor.parentWindowIdDescription": "Pengidentifikasi unik jendela yang berisi tab ini.", "desktop.activityMonitor.pid": "PID", "desktop.activityMonitor.pidDescription": "ID proses dari proses sebagaimana digunakan oleh sistem operasi.", "desktop.activityMonitor.processName": "Nama Proses", "desktop.activityMonitor.showColumns": "<PERSON><PERSON><PERSON><PERSON>", "desktop.activityMonitor.showFilters": "<PERSON><PERSON><PERSON><PERSON>", "desktop.activityMonitor.tabId": "ID Tab", "desktop.activityMonitor.tabIdDescription": "Pengidentifikasi unik dari tab di aplikasi Notion.", "desktop.activityMonitor.type": "<PERSON><PERSON>", "desktop.activityMonitor.url": "URL", "desktop.activityMonitor.urlDescription": "URL proses. Banyak proses renderer men<PERSON><PERSON> be<PERSON>, lihat kolom Frame untuk mengetahui informasi selengkapnya.", "desktop.activityMonitor.visibilityState": "Visibilitas", "desktop.activityMonitor.visibilityStateDescription": "Status visibilitas proses. <PERSON><PERSON> proses me<PERSON>akan proses renderer, ini adalah status visibilitas frame utama.", "desktop.activityMonitor.visible": "Terlihat", "desktop.tabBar.backButtonLabel": "Kembali", "desktop.tabBar.closeSidebarLabel": "Tutup <PERSON>", "desktop.tabBar.closeTabLabel": "<PERSON><PERSON><PERSON>b, {tabTitle}", "desktop.tabBar.forwardButtonLabel": "Teruskan", "desktop.tabBar.newTabButtonLabel": "<PERSON><PERSON>", "desktop.tabBar.openSidebarLabel": "<PERSON><PERSON>", "desktop.tabBar.tabSpacesLabel": "Tab Space", "desktopExtensions.install.failed.title": "<PERSON><PERSON> e<PERSON>", "desktopExtensions.manage.cancel": "Batalkan", "desktopExtensions.manage.disable": "Nonaktifkan", "desktopExtensions.manage.enable": "Aktifkan", "desktopExtensions.manage.message": "Apa yang ingin Anda lakukan pada {extensionTitle} {extensionVersion}?", "desktopExtensions.manage.title": "<PERSON><PERSON><PERSON>", "desktopExtensions.manage.uninstall": "<PERSON><PERSON>", "desktopExtensions.openFailed.noPopupMessage": "Ekstensi ini tidak menetapkan popup (action.default_popup)", "desktopExtensions.openFailed.noPopupTitle": "Gagal membuka e<PERSON>i", "desktopExtensions.unzip.failed.badFileRead": "Gagal membaca file CRX: {error}", "desktopExtensions.unzip.failed.badFileWrite": "Gagal menulis file {filePath}: {error}", "desktopExtensions.unzip.failed.badFolderCreate": "Gagal membuat folder ekstensi di {extensionPath}: {error}", "desktopExtensions.unzip.failed.badManifest": "manifest.json di ekstensi ini tidak dapat diurai. Apakah ini ekstensi yang valid? Kesalahannya adalah: {error}", "desktopExtensions.unzip.failed.badManifestNoName": "Tidak ada nama yang ditemukan di manifest.json", "desktopExtensions.unzip.failed.error": "Gagal membuka zip file CRX: {error}", "desktopExtensions.unzip.failed.noManifest": "Tidak ada manifest.json yang ditemukan di CRX. Apakah ini ekstensi yang valid?", "desktopInstaller.failedToMove.detail": "<PERSON><PERSON> gagal memindah aplikasi ke folder Aplikasi. Pindahkan secara manual.", "desktopInstaller.failedToMove.title": "<PERSON><PERSON><PERSON>", "desktopInstaller.invalidInstallDialog.cancelButton.label": "Batalkan", "desktopInstaller.invalidInstallDialog.confirmMove": "Aplikasi Notion tidak diinstal dengan benar. Bolehkah memindahkan aplikasi Notion ke folder Aplikasi?", "desktopInstaller.invalidInstallDialog.okButton.label": "OKE", "desktopInstaller.invalidInstallDialog.title": "Penginstalan Tidak Valid", "desktopTopbar.appMenu.about": "Tentang Notion", "desktopTopbar.appMenu.checkForUpdate": "Cek <PERSON>…", "desktopTopbar.appMenu.checkForUpdate.noUpdateAvailable": "Anda menggunakan Notion versi terbaru!", "desktopTopbar.appMenu.checkForUpdate.title": "Cek Pembaruan", "desktopTopbar.appMenu.checkForUpdate.updateAvailable": "Versi baru Notion tersedia dan sedang diunduh di latar belakang. Te<PERSON> kasih telah memperbarui versi!", "desktopTopbar.appMenu.checkForUpdate.updateCheckFailed": "Notion gagal terhubung dengan server pembaruan karena masalah pada koneksi internet Anda atau server pembaruan itu sendiri. Coba lagi nanti.", "desktopTopbar.appMenu.downloadingUpdate": "<PERSON><PERSON><PERSON><PERSON> ({percentage}%)", "desktopTopbar.appMenu.hide": "Sembunyikan Notion", "desktopTopbar.appMenu.hideOthers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.appMenu.preferences": "Preferensi…", "desktopTopbar.appMenu.quit": "<PERSON><PERSON><PERSON>", "desktopTopbar.appMenu.quitWithoutSavingTabs": "<PERSON><PERSON><PERSON>yi<PERSON>", "desktopTopbar.appMenu.restartToApplyUpdate": "<PERSON><PERSON> untuk Menerapkan Pembaruan", "desktopTopbar.appMenu.services": "<PERSON><PERSON><PERSON>", "desktopTopbar.appMenu.unhide": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>a", "desktopTopbar.editMenu.copy": "<PERSON><PERSON>", "desktopTopbar.editMenu.copyLinkToCurrentPage": "<PERSON><PERSON> ke <PERSON>aman Saat Ini", "desktopTopbar.editMenu.copyLinkToCurrentPageBlockTitle": "<PERSON><PERSON>", "desktopTopbar.editMenu.cut": "Potong", "desktopTopbar.editMenu.paste": "Tempel", "desktopTopbar.editMenu.pasteAndMatchStyle": "<PERSON><PERSON><PERSON> dan <PERSON><PERSON>", "desktopTopbar.editMenu.redo": "<PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.selectAll": "<PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.speech": "Ucapan", "desktopTopbar.editMenu.speech.startSpeaking": "<PERSON><PERSON>", "desktopTopbar.editMenu.speech.stopSpeaking": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.title": "Edit", "desktopTopbar.editMenu.undo": "Urungkan", "desktopTopbar.extensionsMenu.install": "Instal Ekstensi...", "desktopTopbar.extensionsMenu.manage": "<PERSON><PERSON><PERSON>", "desktopTopbar.fileMenu.close": "<PERSON><PERSON><PERSON>", "desktopTopbar.fileMenu.closeTab": "<PERSON><PERSON><PERSON>", "desktopTopbar.fileMenu.newNotionWindow": "Jendela Notion Baru", "desktopTopbar.fileMenu.newTab": "<PERSON><PERSON>", "desktopTopbar.fileMenu.newWindow": "<PERSON><PERSON>", "desktopTopbar.fileMenu.print": "Cetak…", "desktopTopbar.fileMenu.quit": "<PERSON><PERSON><PERSON>", "desktopTopbar.fileMenu.quitWithoutSavingTabs": "<PERSON><PERSON><PERSON>yi<PERSON>", "desktopTopbar.fileMenu.reopenClosedTab": "<PERSON><PERSON> yang <PERSON>", "desktopTopbar.fileMenu.title": "File", "desktopTopbar.helpMenu.copyInstallId": "<PERSON><PERSON>", "desktopTopbar.helpMenu.disableAdvancedLogging": "Nonaktifkan Pencatatan Log Lanjutan dan <PERSON>", "desktopTopbar.helpMenu.disableDebugLogging": "Nonaktifkan Pencatatan Log Lanjutan dan <PERSON>", "desktopTopbar.helpMenu.disableHardwareAcceleration": "Nonaktifkan Akselerasi <PERSON> dan <PERSON>", "desktopTopbar.helpMenu.enableAdvancedLogging": "Aktifkan Pencatatan Log Lanjutan dan <PERSON>", "desktopTopbar.helpMenu.enableDebugLogging": "Aktifkan Pencatatan Log Lanjutan dan <PERSON>", "desktopTopbar.helpMenu.enableHardwareAcceleration": "Aktifkan Akselerasi Perang<PERSON> dan <PERSON>", "desktopTopbar.helpMenu.openActivityMonitor": "Buka Pemantau Aktivitas", "desktopTopbar.helpMenu.openConsole": "<PERSON><PERSON>", "desktopTopbar.helpMenu.openHelpAndSupport": "Buka Bantuan & Dokumentasi", "desktopTopbar.helpMenu.recordingNetLog": "Merekam log jaringan…", "desktopTopbar.helpMenu.recordNetLog": "Rekam <PERSON>…", "desktopTopbar.helpMenu.recordNetLogConfirmation": "Log jaringan sedang direkam ke folder Unduhan. Untuk menghent<PERSON>n per<PERSON>, klik tombol ‘<PERSON><PERSON><PERSON><PERSON> Merekam Log Jaring<PERSON>’ di menu Pemecahan Masalah atau keluar dari aplikasi.", "desktopTopbar.helpMenu.recordNetLogFailed": "Perekaman log jaringan gagal. Coba lagi atau periksa log untuk mengetahui informasi selengkapnya.", "desktopTopbar.helpMenu.recordNetLogFailedMessage": "Coba lagi atau periksa log untuk mengetahui informasi selengkapnya. Kesalahannya adalah:", "desktopTopbar.helpMenu.recordNetLogFailedTitle": "Perekaman log jaringan gagal", "desktopTopbar.helpMenu.recordNetLogStop": "<PERSON><PERSON><PERSON><PERSON>…", "desktopTopbar.helpMenu.recordPerformanceTrace": "<PERSON><PERSON><PERSON>…", "desktopTopbar.helpMenu.recordPerformanceTraceConfirm": "Anda ingin merekam jejak performa selama 30 detik ke depan? <PERSON><PERSON><PERSON>, reka<PERSON> akan ditempatkan di folder Unduhan <PERSON>.", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmCancel": "Batalkan", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmOk": "<PERSON><PERSON><PERSON> jejak <PERSON>a", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmTitle": "<PERSON><PERSON>m jejak performa?", "desktopTopbar.helpMenu.resetAndEraseAllLocalData": "Reset & Hapus Semua Data Lokal", "desktopTopbar.helpMenu.showLogsInExplorer": "<PERSON><PERSON><PERSON><PERSON> Log di <PERSON>", "desktopTopbar.helpMenu.showLogsInFinder": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.helpMenu.title": "Bantuan", "desktopTopbar.historyMenu.historyBack": "Kembali", "desktopTopbar.historyMenu.historyForward": "Teruskan", "desktopTopbar.historyMenu.title": "Riwayat", "desktopTopbar.toggleDevTools": "<PERSON><PERSON>r Tombol Alat Developer", "desktopTopbar.toggleWindowDevTools": "<PERSON><PERSON>r Tombol Alat Developer Jendela", "desktopTopbar.troubleshootingMenu.title": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.viewMenu.actualSize": "Ukuran Aktual", "desktopTopbar.viewMenu.forceReload": "<PERSON><PERSON>", "desktopTopbar.viewMenu.forceReloadDialog.cancel": "Batalkan", "desktopTopbar.viewMenu.forceReloadDialog.message": "Anda sedang offline. Memaksa pemuatan ulang halaman ini akan menyebabkan Anda kehilangan akses ke halaman tersebut sampai Anda kembali online.", "desktopTopbar.viewMenu.forceReloadDialog.ok": "<PERSON>tap muat ulang", "desktopTopbar.viewMenu.forceReloadDialog.title": "Anda yakin ingin memaksa pemuatan ulang?", "desktopTopbar.viewMenu.reload": "<PERSON><PERSON> ul<PERSON>", "desktopTopbar.viewMenu.showHideSidebar": "<PERSON><PERSON>lkan/Sembunyikan Bilah <PERSON>", "desktopTopbar.viewMenu.showHideTabSpaceButton": "Tampilkan/Sembunyikan Grup Tab", "desktopTopbar.viewMenu.title": "Lihat", "desktopTopbar.viewMenu.togglefullscreen": "<PERSON><PERSON><PERSON>", "desktopTopbar.viewMenu.zoomIn": "<PERSON><PERSON><PERSON>", "desktopTopbar.viewMenu.zoomOut": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.whatsNewMac.title": "Ketahui Yang Baru di Notion untuk macOS", "desktopTopbar.whatsNewWindows.title": "Ke<PERSON>ui Yang Baru di Notion untuk Windows", "desktopTopbar.windowMenu.close": "<PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.front": "<PERSON><PERSON>", "desktopTopbar.windowMenu.maximize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.minimize": "Minimalkan", "desktopTopbar.windowMenu.showNextTab": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.showPreviousTab": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.title": "<PERSON><PERSON>", "desktopTopbar.windowMenu.zoom": "<PERSON><PERSON><PERSON>", "desktopTroubleshooting.resetData.cancel": "Batalkan", "desktopTroubleshooting.resetData.closingWindows": "<PERSON><PERSON><PERSON> j<PERSON>", "desktopTroubleshooting.resetData.deletingFiles": "Menghapus file", "desktopTroubleshooting.resetData.done": "Se<PERSON><PERSON>", "desktopTroubleshooting.resetData.doneMessage": "Aplikasi telah direset.", "desktopTroubleshooting.resetData.failed": "<PERSON><PERSON>, proses pem<PERSON>han gagal menghapus beberapa file. Kunjungi https://www.notion.so/help untuk mendapatkan bantuan. Untuk memaksa reset penuh aplikasi secara manual, tutup Notion sepenuhnya, lalu hapus jalur berikut: {userDataPath}", "desktopTroubleshooting.resetData.message": "Tindakan ini akan menghapus semua data lokal dan internal, termasuk pengaturan lokal dan cache, sehingga memulihkan aplikasi Notion ke status baru diinstal. Tindakan ini juga akan mengeluarkan Anda dari Notion. Halaman dan konten dalam aplikasi lain tidak akan terpengaruh. Anda ingin melanjutkan?", "desktopTroubleshooting.resetData.reset": "Reset semua data lokal", "desktopTroubleshooting.resetData.restart": "<PERSON><PERSON>", "desktopTroubleshooting.resetData.title": "Mereset dan menghapus semua data lokal", "desktopTroubleshooting.showLogs.error.message.mac": "Notion mengalami masalah saat mencoba menampilkan log di Finder:", "desktopTroubleshooting.showLogs.error.message.windows": "Notion mengalami masalah saat mencoba menampilkan log di Explorer:", "desktopTroubleshooting.showLogs.error.title": "<PERSON><PERSON> menampilkan log", "desktopTroubleshooting.startRecordingNetLog": "<PERSON><PERSON>", "desktopTroubleshooting.stopRecordingNetLog": "<PERSON><PERSON><PERSON><PERSON>", "menuBarIcon.menu.changeCommandSearchAndNotionAiShortcut": "<PERSON>", "menuBarIcon.menu.changeCommandSearchShortcut": "Ubah Pinta<PERSON>", "menuBarIcon.menu.enableQuickSearch": "Aktifkan Pencarian Cepat", "menuBarIcon.menu.keepInBackground": "Simpan di Latar <PERSON>", "menuBarIcon.menu.launchPreferences": "Luncurkan Preferensi", "menuBarIcon.menu.openOnLogin": "Buka Notion saat Masuk", "menuBarIcon.menu.quitNotion": "<PERSON><PERSON><PERSON> da<PERSON>", "menuBarIcon.menu.showImmediately": "<PERSON><PERSON><PERSON>", "menuBarIcon.menu.showNotionInMenuBar": "Tampilkan Notion di bilah menu", "menuBarIcon.menu.toggleCommandSearch": "<PERSON><PERSON><PERSON>", "menuBarIcon.menu.toggleNotionAi": "G<PERSON>r Notion AI", "openAtLogin.dialog.detail": "{operatingSystem} mencegah Notion mengonfigurasi pengaturan ‘Buka saat Ma<PERSON>k’. Ini biasanya terjadi ketika pembukaan Notion telah dikonfigurasi di pengaturan sistem atau jika Anda memiliki izin yang tidak memadai. Anda tetap dapat mengonfigurasi pengaturan ini secara manual dalam pengaturan sistem.", "openAtLogin.dialog.title": "<PERSON>uka saat <PERSON>", "tabSpaces.deleteDialog.cancelButton": "Batalkan", "tabSpaces.deleteDialog.deleteButton": "Hapus", "tabSpaces.deleteDialog.detail": "Semua tab di grup tab ini akan dibatalkan pengelompokannya.", "tabSpaces.deleteDialog.title": "Hapus grup tab ‘{title}’ <PERSON><PERSON>?", "tabSpaces.snackbar.switchedToTabGroup": "<PERSON><PERSON><PERSON> ke {title}", "tabSpaces.snackbar.switchedToUngroupedTabs": "<PERSON><PERSON><PERSON> ke tab yang tidak dikelompokkan", "tabSpaces.snackbar.tabGroupPlaceholder": "Grup Tab", "updatePrompt.detail": "Anda ingin men<PERSON>nya sekarang? Kami akan membuka kembali jendela dan tab <PERSON>a.", "updatePrompt.installAndRelaunch": "Instal dan luncurkan kembali", "updatePrompt.message": "Versi baru Notion tersedia!", "updatePrompt.remindMeLater": "Ingatkan saya nanti", "window.closeDialog.cancelButton": "Batalkan", "window.closeDialog.confirmButton": "<PERSON><PERSON><PERSON>", "window.closeDialog.title.app": "Tutup Notion?", "window.closeDialog.title.tab": "Tutup tab Notion?", "window.closeDialog.title.window": "<PERSON><PERSON><PERSON> j<PERSON>?", "window.loadingError.message": "Kesalahan saat memuat Notion, hubungkan ke internet untuk memulai.", "window.loadingError.reload": "<PERSON><PERSON> ul<PERSON>", "window.movedTabSnackbarMessage": "Memindah<PERSON> {tabTitle} ke {tabSpaceTitle}", "window.tabLoadingError.cancel": "Batalkan", "window.tabMenu.closeOtherTabs": "<PERSON><PERSON><PERSON>", "window.tabMenu.closeTab": "<PERSON><PERSON><PERSON>", "window.tabMenu.closeTabsToLeft": "Tutup <PERSON>", "window.tabMenu.closeTabsToRight": "Tutup <PERSON>", "window.tabMenu.copyLink": "<PERSON><PERSON>", "window.tabMenu.duplicateTab": "Duplikatkan Tab", "window.tabMenu.moveTo": "Pindahkan ke", "window.tabMenu.moveToNewWindow": "Pindahkan Tab ke Jendela Baru", "window.tabMenu.moveToSubmenuNewWindow": "<PERSON><PERSON>", "window.tabMenu.pinTab": "<PERSON><PERSON>", "window.tabMenu.refresh": "Refresh <PERSON>b", "window.tabMenu.reopenClosedTab": "<PERSON><PERSON> yang <PERSON>", "window.tabMenu.replacePinnedTabUrl": "Ganti URL yang disematkan dengan Saat Ini", "window.tabMenu.returnToPinnedTabUrl": "Kembali ke URL yang Disematkan", "window.tabMenu.ungroupTab": "Pisah<PERSON>", "window.tabMenu.unpinTab": "<PERSON><PERSON>", "window.tabTitlePlaceholder": "Tab", "window.ungroupedTabSnackbarMessage": "<PERSON><PERSON>n {tabTitle}"}