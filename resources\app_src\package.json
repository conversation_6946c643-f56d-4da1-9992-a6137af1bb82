{"name": "Notion", "author": "Notion Labs, Inc", "version": "4.18.0", "private": true, "description": "Notion", "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "1.4.0", "@atlaskit/pragmatic-drag-and-drop-hitbox": "1.0.3", "@noble/hashes": "1.7.1", "@reduxjs/toolkit": "2.2.3", "base-64": "0.1.0", "better-sqlite3": "11.8.1", "bindings": "1.5.0", "colord": "2.9.3", "debug": "4.4.0", "electron-log": "4.4.8", "electron-store": "8.2.0", "electron-updater": "4.1.2", "fflate": "0.7.4", "fs-extra": "8.1.0", "get-port": "5.1.1", "intl-messageformat": "10.5.0", "lodash": "4.17.21", "loudness": "0.4.2", "luxon": "git://github.com/makenotion/luxon.git#cc297653343a8ef2dda2a7e3a8c820c9593c9954", "md5": "2.2.1", "native-progress-bar": "1.0.3", "node-addon-api": "6.1.0", "node-mac-utils": "git://github.com/jaim-zuber-notion/node-mac-utils#03355039b418a75b55600427e7c5f0cad3d6a91b", "react": "18.2.0", "react-dom": "18.2.0", "react-intl": "6.6.8", "utf8": "3.0.0", "@notionhq/shared": "file:../shared", "@notionhq/shared-intl": "file:../shared-intl", "@notionhq/shared-sqlite": "file:../shared-sqlite", "@notionhq/shared-utils": "file:../shared-utils", "@notionhq/test-framework": "file:../test-framework"}, "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "playwright": "playwright test", "postinstall": "test -n \"$SKIP_POSTINSTALL\" || node scripts/postinstall.js", "ci-dummy-job1": "echo 'Hello from dummy-job1.'", "ci-dummy-job2": "echo 'Hello from dummy-job2.'"}, "main": "./.webpack/main", "overrides": {"asar": "3.2.6", "@electron/universal": "2.0.1", "webpack": "5.98.0"}, "devDependencies": {"@electron-forge/cli": "7.8.1", "@electron-forge/core": "7.8.1", "@electron-forge/maker-dmg": "7.8.1", "@electron-forge/maker-zip": "7.8.1", "@electron-forge/plugin-fuses": "7.8.1", "@electron-forge/plugin-webpack": "7.8.1", "@electron-forge/shared-types": "7.8.1", "@electron/fuses": "1.8.0", "@felixrieseberg/electron-forge-maker-nsis": "7.2.0", "@hitarth-gg/devtron": "0.0.0-development.11", "@jasonscheirer/electron-forge-maker-msix": "0.6.15", "@playwright/test": "1.45.0", "@vercel/webpack-asset-relocator-loader": "1.7.3", "css-loader": "6.10.0", "electron": "36.7.1", "electron-packager": "17.1.2", "electron-updater-yaml": "1.1.2", "node-loader": "2.0.0", "style-loader": "3.3.3"}, "files": ["*.js", "*.html"], "productName": "Notion"}