{"activityMonitor.copyDiagnosticInformation": "Copiar información de diagnóstico", "activityMonitor.copyExecutablePath": "Copiar ruta ejecutable", "activityMonitor.copyUrl": "Copiar URL", "activityMonitor.forceKillProcess": "<PERSON>zar proceso <PERSON>", "activityMonitor.inspectActivityMonitor": "Inspeccionar el monitor de actividad", "activityMonitor.killProcess": "Proceso Kill", "activityMonitor.openDevTools": "<PERSON><PERSON><PERSON> DevTools", "activityMonitor.reload": "Actualizar", "clientPlaceholder.placeholderDescription": "Haz clic para mostrar en esta ventana", "clientPlaceholder.placeholderTitle": "Actualmente se muestra el contenido en otra ventana", "commandSearch.window.title": "Notion - Atajo de búsqueda", "crashWatchdog.dialog.abnormalExit": "Salida anormal: La página ha salido con un código de salida distinto de cero.", "crashWatchdog.dialog.buttonCloseTab": "<PERSON><PERSON>r pesta<PERSON>", "crashWatchdog.dialog.buttonCloseWindow": "<PERSON><PERSON><PERSON> ventana", "crashWatchdog.dialog.buttonRestartApp": "Reiniciar Notion", "crashWatchdog.dialog.crashed": "Bloqueo: La página se bloqueó debido a una razón desconocida.", "crashWatchdog.dialog.details": "URL de la página: {url} Motivo: {reason} Código de <PERSON>lida: {exitCode}", "crashWatchdog.dialog.integrityFailure": "Error de integridad: La página ha fallado en las comprobaciones de integridad del código.", "crashWatchdog.dialog.killed": "Cierre: La página se ha cerrado debido a un proceso externo.", "crashWatchdog.dialog.launchFailed": "Error de inicio: El proceso no se pudo iniciar.", "crashWatchdog.dialog.message": "Se ha producido un error al mostrar esta pestaña y no hemos podido recuperarla automáticamente.", "crashWatchdog.dialog.oom": "OOM: La página se ha quedado sin memoria y se ha bloqueado.", "crashWatchdog.dialog.title": "Se ha producido un error", "crashWatchdog.dialog.urlUnknown": "Desconocido", "desktop.activityMonitor.all": "Todos", "desktop.activityMonitor.cpuPercent": "% CPU", "desktop.activityMonitor.cpuPercentDescription": "La fracción de tiempo de CPU disponible utilizada por el proceso.", "desktop.activityMonitor.cpuTime": "Tiempo de CPU", "desktop.activityMonitor.cpuTimeDescription": "El total de segundos de tiempo de CPU utilizado desde el inicio del proceso.", "desktop.activityMonitor.creationTime": "<PERSON><PERSON><PERSON>", "desktop.activityMonitor.creationTimeDescription": "La cantidad de tiempo desde que se creó el proceso.", "desktop.activityMonitor.frames": "<PERSON>", "desktop.activityMonitor.framesDescription": "El número de marcos administrados por el proceso. Muchos procesos de renderización son responsables de múltiples marcos.", "desktop.activityMonitor.hidden": "Oculta", "desktop.activityMonitor.hideColumns": "Ocultar columnas", "desktop.activityMonitor.hideFilters": "Ocultar filtros", "desktop.activityMonitor.idleWakeupsPerSecond": "Despertares inactivos", "desktop.activityMonitor.idleWakeupsPerSecondDescription": "El número de veces que el proceso ha despertado la CPU desde la última actualización del monitor de actividad.", "desktop.activityMonitor.loading": "Cargando datos de actividad", "desktop.activityMonitor.memCurrent": "Memoria (actual)", "desktop.activityMonitor.memCurrentDescription": "El tamaño de «conjunto de trabajo» del proceso, es decir, el conjunto de páginas que contiene actualmente la RAM. Este número no tiene en cuenta la compresión de memoria del sistema operativo, la administración de páginas inactivas y en caché u otras técnicas de gestión de memoria. Es probable que la cantidad de memoria física utilizada por el proceso sea mucho menor.", "desktop.activityMonitor.memPeak": "Memoria (máxima)", "desktop.activityMonitor.memPeakDescription": "El tamaño máximo del conjunto de trabajo del proceso, es decir, la cantidad máxima de memoria física utilizada por el proceso desde que se inició. El tamaño de «conjunto de trabajo» del proceso, que es el conjunto de páginas que contiene actualmente la RAM. Este número no tiene en cuenta la compresión de memoria del sistema operativo, la administración de páginas inactivas y en caché u otras técnicas de gestión de memoria. Es probable que la cantidad de memoria física utilizada por el proceso sea mucho menor.", "desktop.activityMonitor.memPrivate": "Memoria (privada)", "desktop.activityMonitor.memPrivateDescription": "La cantidad de memoria física asignada por el proceso que no se comparte con otros procesos, como el montón JS o el contenido HTML.", "desktop.activityMonitor.memShared": "Memoria (compartida)", "desktop.activityMonitor.memSharedDescription": "La cantidad de memoria física asignada por el proceso que se comparte con otros procesos, como bibliotecas compartidas o archivos mapeados.", "desktop.activityMonitor.mixed": "Mixta", "desktop.activityMonitor.parentWindowId": "Identificador de la ventana principal", "desktop.activityMonitor.parentWindowIdDescription": "El identificador único de la ventana que contiene esta pestaña.", "desktop.activityMonitor.pid": "PID", "desktop.activityMonitor.pidDescription": "El ID de proceso del proceso utilizado por el sistema operativo.", "desktop.activityMonitor.processName": "Nombre del proceso", "desktop.activityMonitor.showColumns": "Mostrar columnas", "desktop.activityMonitor.showFilters": "Mostrar filtros", "desktop.activityMonitor.tabId": "Identificador de la pestaña", "desktop.activityMonitor.tabIdDescription": "El identificador único de la pestaña en la app de Notion.", "desktop.activityMonitor.type": "Tipo", "desktop.activityMonitor.url": "URL", "desktop.activityMonitor.urlDescription": "La URL del proceso. Muchos procesos de renderización son responsables de múltiples marcos. Consulta la columna Marcos para obtener más información.", "desktop.activityMonitor.visibilityState": "Visibilidad", "desktop.activityMonitor.visibilityStateDescription": "El estado de visibilidad del proceso. Si el proceso es un proceso de renderización, este será el estado de visibilidad del marco principal.", "desktop.activityMonitor.visible": "Visible", "desktop.tabBar.backButtonLabel": "Atrás", "desktop.tabBar.closeSidebarLabel": "Cerrar la barra lateral", "desktop.tabBar.closeTabLabel": "<PERSON><PERSON>r pestaña {tabTitle}", "desktop.tabBar.forwardButtonLabel": "Adelante", "desktop.tabBar.newTabButtonLabel": "Nueva pestaña", "desktop.tabBar.openSidebarLabel": "Abrir la barra lateral", "desktop.tabBar.tabSpacesLabel": "Espacios de pestañas", "desktopExtensions.install.failed.title": "Error al instalar la extensión", "desktopExtensions.manage.cancel": "<PERSON><PERSON><PERSON>", "desktopExtensions.manage.disable": "Desactivar", "desktopExtensions.manage.enable": "Activar", "desktopExtensions.manage.message": "¿Qué te gustaría hacer con {extensionTitle} {extensionVersion}?", "desktopExtensions.manage.title": "Gestionar extensión", "desktopExtensions.manage.uninstall": "<PERSON><PERSON><PERSON><PERSON>", "desktopExtensions.manage.unload": "<PERSON><PERSON><PERSON>", "desktopExtensions.openFailed.noPopupMessage": "Esta extensión no especificó una ventana emergente (action.default_popup)", "desktopExtensions.openFailed.noPopupTitle": "No se ha podido abrir la extensión", "desktopExtensions.unzip.failed.badFileRead": "Error al leer el archivo CRX: {error}", "desktopExtensions.unzip.failed.badFileWrite": "Error al escribir el archivo {filePath}: {error}", "desktopExtensions.unzip.failed.badFolderCreate": "Error al crear la carpeta de extensiones en {extensionPath}: {error}", "desktopExtensions.unzip.failed.badManifest": "No se ha podido analizar el archivo manifest.json de esta extensión. ¿Es una extensión válida? El error es: {error}", "desktopExtensions.unzip.failed.badManifestNoName": "No se ha encontrado ningún nombre en manifest.json", "desktopExtensions.unzip.failed.error": "Error al descomprimir el archivo CRX: {error}", "desktopExtensions.unzip.failed.noManifest": "No se ha encontrado manifest.json en el CRX. ¿Es una extensión válida?", "desktopInstaller.failedToMove.detail": "No hemos podido mover la aplicación a la carpeta Aplicaciones. Intenta moverla de forma manual.", "desktopInstaller.failedToMove.title": "Error al mover la aplicación", "desktopInstaller.invalidInstallDialog.cancelButton.label": "<PERSON><PERSON><PERSON>", "desktopInstaller.invalidInstallDialog.confirmMove": "La aplicación de Notion no se ha instalado correctamente. ¿Quieres que movamos la aplicación de Notion a la carpeta Aplicaciones?", "desktopInstaller.invalidInstallDialog.okButton.label": "Aceptar", "desktopInstaller.invalidInstallDialog.title": "Instalación no válida", "desktopTopbar.appMenu.about": "Acerca de Notion", "desktopTopbar.appMenu.checkForUpdate": "Buscar actualizaciones...", "desktopTopbar.appMenu.checkForUpdate.noUpdateAvailable": "¡Tienes la última versión de Notion!", "desktopTopbar.appMenu.checkForUpdate.title": "Buscar actualizaciones", "desktopTopbar.appMenu.checkForUpdate.updateAvailable": "Hay una nueva versión de Notion disponible y se está descargando en segundo plano. ¡Gracias por mantenerte al día!", "desktopTopbar.appMenu.checkForUpdate.updateCheckFailed": "Notion no ha podido establecer una conexión con el servidor de actualizaciones debido a un problema de la conexión a Internet o del propio servidor. Inténtalo de nuevo más tarde.", "desktopTopbar.appMenu.downloadingUpdate": "Descargando la actualización ({percentage} %)", "desktopTopbar.appMenu.hide": "Ocultar Notion", "desktopTopbar.appMenu.hideOthers": "Ocultar otros", "desktopTopbar.appMenu.preferences": "Preferencias…", "desktopTopbar.appMenu.quit": "Salir", "desktopTopbar.appMenu.quitWithoutSavingTabs": "<PERSON><PERSON><PERSON> sin guardar pestañas", "desktopTopbar.appMenu.restartToApplyUpdate": "Reiniciar para aplicar la actualización", "desktopTopbar.appMenu.services": "<PERSON><PERSON><PERSON>", "desktopTopbar.appMenu.unhide": "<PERSON><PERSON> todo", "desktopTopbar.editMenu.copy": "Copiar", "desktopTopbar.editMenu.copyLinkToCurrentPage": "<PERSON><PERSON><PERSON> enlace a la página actual", "desktopTopbar.editMenu.copyLinkToCurrentPageBlockTitle": "Copiar nombre de la página actual", "desktopTopbar.editMenu.cut": "Cortar", "desktopTopbar.editMenu.paste": "<PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.pasteAndMatchStyle": "Pegar y combinar formato", "desktopTopbar.editMenu.redo": "<PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "desktopTopbar.editMenu.speech": "Voz", "desktopTopbar.editMenu.speech.startSpeaking": "Empezar lo<PERSON>", "desktopTopbar.editMenu.speech.stopSpeaking": "Detener locución", "desktopTopbar.editMenu.title": "<PERSON><PERSON>", "desktopTopbar.editMenu.undo": "<PERSON><PERSON><PERSON>", "desktopTopbar.extensionsMenu.install": "Instalar extensión", "desktopTopbar.extensionsMenu.manage": "Gestionar extensiones", "desktopTopbar.fileMenu.close": "<PERSON><PERSON><PERSON> ventana", "desktopTopbar.fileMenu.closeTab": "<PERSON><PERSON>r pesta<PERSON>", "desktopTopbar.fileMenu.newNotionWindow": "Nueva ventana de Notion", "desktopTopbar.fileMenu.newTab": "Nueva pestaña", "desktopTopbar.fileMenu.newWindow": "Nueva ventana", "desktopTopbar.fileMenu.print": "<PERSON><PERSON><PERSON><PERSON>…", "desktopTopbar.fileMenu.quit": "Salir", "desktopTopbar.fileMenu.quitWithoutSavingTabs": "<PERSON>ir sin guardar pestañas", "desktopTopbar.fileMenu.reopenClosedTab": "Abrir la última pestaña cerrada", "desktopTopbar.fileMenu.title": "Archivo", "desktopTopbar.helpMenu.copyInstallId": "Copiar el ID de instalación", "desktopTopbar.helpMenu.disableAdvancedLogging": "Desactivar el registro avanzado y reiniciar", "desktopTopbar.helpMenu.disableDebugLogging": "Desactivar el registro avanzado y reiniciar", "desktopTopbar.helpMenu.disableHardwareAcceleration": "Desactivar aceleración por hardware y reiniciar", "desktopTopbar.helpMenu.enableAdvancedLogging": "Activar el registro avanzado y reiniciar", "desktopTopbar.helpMenu.enableDebugLogging": "Activar el registro avanzado y reiniciar", "desktopTopbar.helpMenu.enableHardwareAcceleration": "Activar aceleración por hardware y reiniciar", "desktopTopbar.helpMenu.openActivityMonitor": "Abrir el monitor de actividad", "desktopTopbar.helpMenu.openConsole": "<PERSON><PERSON><PERSON> consola", "desktopTopbar.helpMenu.openHelpAndSupport": "Abrir Ayuda y documentación", "desktopTopbar.helpMenu.recordingNetLog": "Grabando registro de red...", "desktopTopbar.helpMenu.recordNetLog": "Grabar registro de red...", "desktopTopbar.helpMenu.recordNetLogConfirmation": "Se está grabando un registro de red en tu carpeta de descargas. Para detener el proceso, haz clic en el botón «Detener grabación del registro de red» en el menú de Solución de problemas o sal de la aplicación.", "desktopTopbar.helpMenu.recordNetLogFailed": "Se ha producido un error al grabar un registro de red. Inténtalo de nuevo o revisa los registros para obtener más información.", "desktopTopbar.helpMenu.recordNetLogFailedMessage": "Inténtalo de nuevo o revisa los registros para obtener más información. El error fue:", "desktopTopbar.helpMenu.recordNetLogFailedTitle": "Se ha producido un error al grabar un registro de red", "desktopTopbar.helpMenu.recordNetLogStop": "Detener la grabación del registro de red...", "desktopTopbar.helpMenu.recordPerformanceTrace": "Grabar registro de rendimiento...", "desktopTopbar.helpMenu.recordPerformanceTraceConfirm": "¿Quieres grabar un registro de rendimiento para los próximos 30 segundos? Una vez esté listo, se guardará en tu carpeta de Descargas.", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmCancel": "<PERSON><PERSON><PERSON>", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmOk": "Grabar registro de rendimiento", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmTitle": "¿Quieres grabar un registro de rendimiento?", "desktopTopbar.helpMenu.resetAndEraseAllLocalData": "Reiniciar y borrar los datos locales", "desktopTopbar.helpMenu.showLogsInExplorer": "Mostrar registros en Explorer", "desktopTopbar.helpMenu.showLogsInFinder": "Mostrar registros en Finder", "desktopTopbar.helpMenu.title": "<PERSON><PERSON><PERSON>", "desktopTopbar.historyMenu.historyBack": "Atrás", "desktopTopbar.historyMenu.historyForward": "Adelante", "desktopTopbar.historyMenu.title": "Historial", "desktopTopbar.toggleDevTools": "Mostrar herramientas de desarrollo", "desktopTopbar.toggleWindowDevTools": "Mostrar herramientas de desarrollo", "desktopTopbar.troubleshootingMenu.title": "Solución de problemas", "desktopTopbar.viewMenu.actualSize": "Tamaño real", "desktopTopbar.viewMenu.forceReload": "<PERSON><PERSON> recarga", "desktopTopbar.viewMenu.forceReloadDialog.cancel": "<PERSON><PERSON><PERSON>", "desktopTopbar.viewMenu.forceReloadDialog.message": "En este momento no tienes conexión. Si fuerzas la recarga de esta página, no podrás volver a acceder a ella hasta que te conectes de nuevo.", "desktopTopbar.viewMenu.forceReloadDialog.ok": "<PERSON><PERSON><PERSON> de todos modos", "desktopTopbar.viewMenu.forceReloadDialog.title": "¿Se<PERSON>ro que quieres forzar la recarga?", "desktopTopbar.viewMenu.reload": "Actualizar", "desktopTopbar.viewMenu.showHideSidebar": "Mostrar/ocultar barra lateral", "desktopTopbar.viewMenu.showHideTabSpaceButton": "Mostrar/ocultar grupos de pestañas", "desktopTopbar.viewMenu.title": "<PERSON>er", "desktopTopbar.viewMenu.togglefullscreen": "Cambiar a pantalla completa", "desktopTopbar.viewMenu.zoomIn": "Acercar", "desktopTopbar.viewMenu.zoomOut": "<PERSON><PERSON><PERSON>", "desktopTopbar.whatsNewMac.title": "Descubre las novedades de Notion para macOS", "desktopTopbar.whatsNewWindows.title": "Descubre las novedades de Notion para Windows", "desktopTopbar.windowMenu.close": "<PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.front": "Primer plano", "desktopTopbar.windowMenu.maximize": "Maximizar", "desktopTopbar.windowMenu.minimize": "<PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.showNextTab": "Mostrar pestaña siguiente", "desktopTopbar.windowMenu.showPreviousTab": "Mostrar pestaña anterior", "desktopTopbar.windowMenu.title": "Ventana", "desktopTopbar.windowMenu.zoom": "Zoom", "desktopTroubleshooting.resetData.cancel": "<PERSON><PERSON><PERSON>", "desktopTroubleshooting.resetData.closingWindows": "Cerrando las ventanas de Notion", "desktopTroubleshooting.resetData.deletingFiles": "Eliminando archivos", "desktopTroubleshooting.resetData.done": "Listo", "desktopTroubleshooting.resetData.doneMessage": "Se ha restablecido la aplicación.", "desktopTroubleshooting.resetData.failed": "El proceso de recuperación no ha podido eliminar algunos archivos. Sentimos las molestias. Dirígete a https://www.notion.so/help para obtener ayuda. Para forzar el restablecimiento de la aplicación manualmente, cierra Notion por completo. A continuación, elimina la siguiente ruta: {userDataPath}", "desktopTroubleshooting.resetData.message": "Esto eliminará todos los datos locales e internos, incluida la caché y la configuración local, y restaurará la aplicación de Notion a como estaba recién instalada. También forzará el cierre de sesión. Tus páginas y el resto del contenido de la aplicación no se verán afectados. ¿Quieres continuar?", "desktopTroubleshooting.resetData.reset": "Restablecer todos los datos locales", "desktopTroubleshooting.resetData.restart": "Reiniciar", "desktopTroubleshooting.resetData.title": "Restablecer y borrar todos los datos locales", "desktopTroubleshooting.showLogs.error.message.mac": "Notion ha encontrado un error al intentar mostrar los registros en Finder:", "desktopTroubleshooting.showLogs.error.message.windows": "Notion ha encontrado un error al intentar mostrar los registros en Explorer:", "desktopTroubleshooting.showLogs.error.title": "Se ha producido un error al mostrar los registros", "desktopTroubleshooting.startRecordingNetLog": "Iniciar la grabación del registro de red", "desktopTroubleshooting.stopRecordingNetLog": "Detener la grabación del registro de red", "menuBarIcon.menu.changeCommandSearchAndNotionAiShortcut": "Editar accesos directos", "menuBarIcon.menu.changeCommandSearchShortcut": "Cambiar acceso directo del Atajo de búsqueda", "menuBarIcon.menu.enableQuickSearch": "Activar b<PERSON>que<PERSON> rá<PERSON>a", "menuBarIcon.menu.keepInBackground": "Mantener en segundo plano", "menuBarIcon.menu.launchPreferences": "Preferencias de inicio", "menuBarIcon.menu.openOnLogin": "Abrir Notion al iniciar sesión", "menuBarIcon.menu.quitNotion": "Salir de Notion", "menuBarIcon.menu.showImmediately": "Mostrar inmediatamente", "menuBarIcon.menu.showNotionInMenuBar": "Mostrar Notion en la barra de menús", "menuBarIcon.menu.toggleCommandSearch": "Activar/desactivar atajo de búsqueda", "menuBarIcon.menu.toggleNotionAi": "Activar o desactivar la IA de Notion", "openAtLogin.dialog.detail": "{operatingSystem} ha impedido que Notion cambie la opción «Abrir al iniciar sesión». Esto puede ocurrir cuando has configurado el inicio de Notion mediante los ajustes del sistema o si careces de los permisos necesarios. Puedes modificar esta opción de forma manual en los ajustes del sistema.", "openAtLogin.dialog.title": "Abrir al iniciar sesión", "tabSpaces.deleteDialog.cancelButton": "<PERSON><PERSON><PERSON>", "tabSpaces.deleteDialog.deleteButton": "Eliminar", "tabSpaces.deleteDialog.detail": "Se desagruparán todas las pestañas de este grupo de pestañas.", "tabSpaces.deleteDialog.title": "¿Quieres eliminar el grupo de pestañas «{title}»?", "tabSpaces.snackbar.switchedToTabGroup": "<PERSON><PERSON><PERSON> a {title}", "tabSpaces.snackbar.switchedToUngroupedTabs": "Se ha cambiado a pestañas no agrupadas", "tabSpaces.snackbar.tabGroupPlaceholder": "Grupo de pestañas", "updatePrompt.detail": "¿Te gustaría instalar la actualización ahora? Volveremos a abrir las ventanas y pestañas que tenías abiertas.", "updatePrompt.installAndRelaunch": "Instalar y reiniciar", "updatePrompt.message": "Hay una nueva versión de Notion disponible.", "updatePrompt.remindMeLater": "Recordármelo más tarde", "window.closeDialog.cancelButton": "<PERSON><PERSON><PERSON>", "window.closeDialog.confirmButton": "<PERSON><PERSON><PERSON>", "window.closeDialog.title.app": "¿Quieres cerrar <PERSON>ion?", "window.closeDialog.title.tab": "¿Quieres cerrar la pestaña de Notion?", "window.closeDialog.title.window": "¿Quieres cerrar la ventana de Notion?", "window.loadingError.message": "Error al cargar Notion. Conéctate a Internet para comenzar.", "window.loadingError.reload": "Actualizar", "window.movedTabSnackbarMessage": "Se ha movido {tabTitle} a {tabSpaceTitle}", "window.tabLoadingError.cancel": "<PERSON><PERSON><PERSON>", "window.tabMenu.closeOtherTabs": "Cerrar otras pestañas", "window.tabMenu.closeTab": "<PERSON><PERSON>r pesta<PERSON>", "window.tabMenu.closeTabsToLeft": "Cerrar pestañas a la izquierda", "window.tabMenu.closeTabsToRight": "Cerrar pestañas a la derecha", "window.tabMenu.copyLink": "<PERSON><PERSON><PERSON> enlace", "window.tabMenu.duplicateTab": "Duplicar pestaña", "window.tabMenu.moveTo": "Mover a", "window.tabMenu.moveToNewWindow": "Mover pestaña a nueva ventana", "window.tabMenu.moveToSubmenuNewWindow": "Nueva ventana", "window.tabMenu.pinTab": "<PERSON>jar pesta<PERSON>", "window.tabMenu.refresh": "Actualizar pestaña", "window.tabMenu.reopenClosedTab": "Reabrir la última pestaña cerrada", "window.tabMenu.replacePinnedTabUrl": "Reemplazar la URL fijada por la actual", "window.tabMenu.returnToPinnedTabUrl": "Volver a la URL fijada", "window.tabMenu.ungroupTab": "Desagrupar pestaña", "window.tabMenu.unpinTab": "<PERSON><PERSON><PERSON> pesta<PERSON>", "window.tabTitlePlaceholder": "Pestaña", "window.ungroupedTabSnackbarMessage": "{tabTitle} sin agrupar"}