{"activityMonitor.copyDiagnosticInformation": "<PERSON><PERSON><PERSON><PERSON> diagnos<PERSON>ke oplysninger", "activityMonitor.copyExecutablePath": "<PERSON><PERSON><PERSON><PERSON> sti", "activityMonitor.copyUrl": "Kopiér URL", "activityMonitor.forceKillProcess": "Gennemtving afslutning af processen", "activityMonitor.inspectActivityMonitor": "Tjek aktivitetsmonitoren", "activityMonitor.killProcess": "Afslut processen", "activityMonitor.openDevTools": "Åbn DevTools", "activityMonitor.reload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clientPlaceholder.placeholderDescription": "<PERSON><PERSON> for at vise i dette vindue", "clientPlaceholder.placeholderTitle": "Visning af indhold i et andet vindue", "commandSearch.window.title": "Notion - Kommandosøgning", "crashWatchdog.dialog.abnormalExit": "Unormal afslutning: Siden afsluttes med en afslutningskode, der ikke er nul.", "crashWatchdog.dialog.buttonCloseTab": "<PERSON><PERSON> fane", "crashWatchdog.dialog.buttonCloseWindow": "Luk vindue", "crashWatchdog.dialog.buttonRestartApp": "Genstart Notion", "crashWatchdog.dialog.crashed": "Nedbrud: <PERSON>n gik ned på grund af en ukendt årsag.", "crashWatchdog.dialog.details": "Side-URL: {url} Årsag: {reason} Afslutningskode: {exitCode}", "crashWatchdog.dialog.integrityFailure": "Integritetsfejl: <PERSON>n bestod ikke kodeintegritetstjek.", "crashWatchdog.dialog.killed": "Stoppet: Siden blev stoppet af en ekstern proces.", "crashWatchdog.dialog.launchFailed": "Lancering mislykkedes: <PERSON><PERSON> kunne ikke lanceres.", "crashWatchdog.dialog.message": "<PERSON><PERSON> gik galt, da denne fane skulle vises, og vi kunne ikke automatisk gendanne den.", "crashWatchdog.dialog.oom": "OOM: <PERSON><PERSON> lø<PERSON> tør for hukommelse (out of memory) og gik ned.", "crashWatchdog.dialog.title": "Noget gik galt", "crashWatchdog.dialog.urlUnknown": "Ukendt", "desktop.activityMonitor.all": "Alle", "desktop.activityMonitor.cpuPercent": "% CPU", "desktop.activityMonitor.cpuPercentDescription": "Den del af tilgængelig CPU-tid, der anvendes af processen.", "desktop.activityMonitor.cpuTime": "CPU-tid", "desktop.activityMonitor.cpuTimeDescription": "Det samlede antal sekunder af CPU-tid, der er brugt siden processtart.", "desktop.activityMonitor.creationTime": "Oprettet", "desktop.activityMonitor.creationTimeDescription": "Den tid der er gået, siden processen blev oprettet.", "desktop.activityMonitor.frames": "<PERSON><PERSON>", "desktop.activityMonitor.framesDescription": "Antal<PERSON> af rammer, der administreres af processen. Mange renderingsprocesser er ansvarlige for flere rammer.", "desktop.activityMonitor.hidden": "Skjult", "desktop.activityMonitor.hideColumns": "Skjul kolonner", "desktop.activityMonitor.hideFilters": "Skjul filtre", "desktop.activityMonitor.idleWakeupsPerSecond": "Inaktive opvågninger", "desktop.activityMonitor.idleWakeupsPerSecondDescription": "Antallet af gange, processen har vækket CPU'en siden den sidste opdatering af aktivitetsmonitoren.", "desktop.activityMonitor.loading": "Indlæser aktivitetsdata", "desktop.activityMonitor.memCurrent": "Hukommelse (aktuel)", "desktop.activityMonitor.memCurrentDescription": "<PERSON><PERSON><PERSON><PERSON> på processens \"arbejdssæt\", som er det sæt af sider, der aktuelt er indlæst i RAM. Dette nummer tager ikke højde for operativsystemets hukommelseskomprimering, administrering af inaktive og cachelagrede sider eller andre teknikker til hukommelsesadministration. Mængden af fysisk hukommelse, der anvendes af processen, er sandsynligvis meget mindre.", "desktop.activityMonitor.memPeak": "<PERSON><PERSON><PERSON><PERSON><PERSON> (maksimal)", "desktop.activityMonitor.memPeakDescription": "Den maksimale størrelse på processens arbejdssæt, som er den maksimale mængde fysisk hukommelse, der anvendes af processen, siden den blev lanceret. Størrelsen på processens \"arbejdssæt\", som er det sæt af sider, der aktuelt er indlæst i RAM. Dette nummer tager ikke højde for operativsystemets hukommelseskomprimering, administrering af inaktive og cachelagrede sider eller andre teknikker til hukommelsesadministration. Mængden af fysisk hukommelse, der anvendes af processen, er sandsynligvis meget mindre.", "desktop.activityMonitor.memPrivate": "Hu<PERSON><PERSON><PERSON><PERSON> (privat)", "desktop.activityMonitor.memPrivateDescription": "Mængden af fysisk hukommelse allokeret af processen, der ikke deles med andre processer, såsom JS-heap eller HTML-indhold.", "desktop.activityMonitor.memShared": "Hukommelse (delt)", "desktop.activityMonitor.memSharedDescription": "Mængden af fysisk hukommelse allokeret af processen, der deles med andre processer, såsom delte biblioteker eller tilknyttede filer.", "desktop.activityMonitor.mixed": "<PERSON><PERSON><PERSON>", "desktop.activityMonitor.parentWindowId": "Det overordnede vindues id", "desktop.activityMonitor.parentWindowIdDescription": "Den unikke identifikator for det vindue, der indeholder denne fane.", "desktop.activityMonitor.pid": "PID", "desktop.activityMonitor.pidDescription": "Processens proces-ID, som anvendes af operativsystemet.", "desktop.activityMonitor.processName": "Procesnavn", "desktop.activityMonitor.showColumns": "<PERSON><PERSON> k<PERSON>", "desktop.activityMonitor.showFilters": "Vis filtre", "desktop.activityMonitor.tabId": "Fanens id", "desktop.activityMonitor.tabIdDescription": "Den unikke identifikator for fanen i Notion-appen.", "desktop.activityMonitor.type": "Type", "desktop.activityMonitor.url": "Webadresse", "desktop.activityMonitor.urlDescription": "Processens URL. Mange renderingsprocesser er ansvarlige for flere rammer. Få flere oplysninger i kolonnen Rammer.", "desktop.activityMonitor.visibilityState": "<PERSON><PERSON><PERSON>gh<PERSON>", "desktop.activityMonitor.visibilityStateDescription": "Processens synlighedstilstand. Hvis processen er en renderingsproces, vil dette være hovedrammens synlighedstilstand.", "desktop.activityMonitor.visible": "<PERSON><PERSON><PERSON><PERSON>", "desktop.tabBar.backButtonLabel": "Tilbage", "desktop.tabBar.closeSidebarLabel": "Luk sidemenu", "desktop.tabBar.closeTabLabel": "<PERSON><PERSON> fanen {tabTitle}", "desktop.tabBar.forwardButtonLabel": "<PERSON><PERSON><PERSON>", "desktop.tabBar.newTabButtonLabel": "<PERSON><PERSON> fane", "desktop.tabBar.openSidebarLabel": "Åbn sidemenu", "desktop.tabBar.tabSpacesLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopExtensions.install.failed.title": "Kunne ikke installere udvidelsen", "desktopExtensions.manage.cancel": "<PERSON><PERSON><PERSON>", "desktopExtensions.manage.disable": "<PERSON><PERSON><PERSON><PERSON>", "desktopExtensions.manage.enable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopExtensions.manage.message": "Hvad vil du gøre med {extensionTitle} {extensionVersion}?", "desktopExtensions.manage.title": "Administrer udvidelse", "desktopExtensions.manage.uninstall": "Afinstaller", "desktopExtensions.manage.unload": "<PERSON><PERSON><PERSON><PERSON>", "desktopExtensions.openFailed.noPopupMessage": "Denne udvidelse specificerede ikke en popup (action.default_popup)", "desktopExtensions.openFailed.noPopupTitle": "Kunne ikke åbne udvidelse", "desktopExtensions.unzip.failed.badFileRead": "<PERSON><PERSON> ikke læse CRX-fil: {error}", "desktopExtensions.unzip.failed.badFileWrite": "<PERSON><PERSON> ikke skrive fil {filePath}: {error}", "desktopExtensions.unzip.failed.badFolderCreate": "Kunne ikke oprette udvidelsesmappen på {extensionPath}: {error}", "desktopExtensions.unzip.failed.badManifest": "Det var ikke muligt at parse manifest.json i denne udvidelse. Er dette en gyldig udvidelse? Fe<PERSON>len var: {error}", "desktopExtensions.unzip.failed.badManifestNoName": "Intet navn fundet i manifest.json", "desktopExtensions.unzip.failed.error": "<PERSON>nne ikke pakke CRX-fil ud: {error}", "desktopExtensions.unzip.failed.noManifest": "Intet manifest.json fundet i CRX. Er dette en gyldig udvidelse?", "desktopInstaller.failedToMove.detail": "Vi kunne ikke flytte appen til din applikationsmappe. Flyt den manuelt.", "desktopInstaller.failedToMove.title": "Kunne ikke flytte app", "desktopInstaller.invalidInstallDialog.cancelButton.label": "<PERSON><PERSON><PERSON><PERSON>", "desktopInstaller.invalidInstallDialog.confirmMove": "Din Notion-applikation er ikke installeret korrekt. Må vi flytte din Notion-app til din applikationsmappe?", "desktopInstaller.invalidInstallDialog.okButton.label": "OK", "desktopInstaller.invalidInstallDialog.title": "Ugyldig installation", "desktopTopbar.appMenu.about": "Om Notion", "desktopTopbar.appMenu.checkForUpdate": "Tjek for opdateringer...", "desktopTopbar.appMenu.checkForUpdate.noUpdateAvailable": "Du bruger den nyeste version af Notion.", "desktopTopbar.appMenu.checkForUpdate.title": "Tjek for opdateringer", "desktopTopbar.appMenu.checkForUpdate.updateAvailable": "En ny version af Notion er tilgængelig og downloades i øjeblikket i baggrunden. Tak, fordi du holder dig opdateret!", "desktopTopbar.appMenu.checkForUpdate.updateCheckFailed": "Notion kunne ikke oprette forbindelse til opdateringsserveren, enten på grund af et problem med din internetforbindelse eller selve opdateringsserveren. Prøv igen senere.", "desktopTopbar.appMenu.downloadingUpdate": "Downloader opdatering ({percentage} %)", "desktopTopbar.appMenu.hide": "Skjul Notion", "desktopTopbar.appMenu.hideOthers": "<PERSON><PERSON><PERSON><PERSON> and<PERSON>", "desktopTopbar.appMenu.preferences": "Indstillinger...", "desktopTopbar.appMenu.quit": "A<PERSON>lut", "desktopTopbar.appMenu.quitWithoutSavingTabs": "<PERSON><PERSON><PERSON> uden at gemme faner", "desktopTopbar.appMenu.restartToApplyUpdate": "Genstart for at anvende opdateringen", "desktopTopbar.appMenu.services": "T<PERSON>nes<PERSON>", "desktopTopbar.appMenu.unhide": "Vis alle", "desktopTopbar.editMenu.copy": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.copyLinkToCurrentPage": "Kopiér link til aktuel side", "desktopTopbar.editMenu.copyLinkToCurrentPageBlockTitle": "Kopiér aktuel sides navn", "desktopTopbar.editMenu.cut": "<PERSON><PERSON>", "desktopTopbar.editMenu.paste": "Indsæt", "desktopTopbar.editMenu.pasteAndMatchStyle": "Indsæt og match stil", "desktopTopbar.editMenu.redo": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.selectAll": "<PERSON><PERSON><PERSON><PERSON> alle", "desktopTopbar.editMenu.speech": "Tale", "desktopTopbar.editMenu.speech.startSpeaking": "Begynd at tale", "desktopTopbar.editMenu.speech.stopSpeaking": "Stop med at tale", "desktopTopbar.editMenu.title": "<PERSON><PERSON>", "desktopTopbar.editMenu.undo": "<PERSON><PERSON><PERSON>", "desktopTopbar.extensionsMenu.install": "Installer udvidelse ...", "desktopTopbar.extensionsMenu.manage": "Administrer udvidelser", "desktopTopbar.fileMenu.close": "Luk vindue", "desktopTopbar.fileMenu.closeTab": "<PERSON><PERSON> fane", "desktopTopbar.fileMenu.newNotionWindow": "Nyt Notion-vindue", "desktopTopbar.fileMenu.newTab": "<PERSON><PERSON> fane", "desktopTopbar.fileMenu.newWindow": "Nyt vindue", "desktopTopbar.fileMenu.print": "Udskriv ...", "desktopTopbar.fileMenu.quit": "A<PERSON>lut", "desktopTopbar.fileMenu.quitWithoutSavingTabs": "<PERSON><PERSON><PERSON> uden at gemme faner", "desktopTopbar.fileMenu.reopenClosedTab": "Åbn den senest lukkede fane igen", "desktopTopbar.fileMenu.title": "Fil", "desktopTopbar.helpMenu.copyInstallId": "Kopiér installations-id", "desktopTopbar.helpMenu.disableAdvancedLogging": "Deak<PERSON>r avanceret logning og genstart", "desktopTopbar.helpMenu.disableDebugLogging": "Deak<PERSON>r avanceret logning og genstart", "desktopTopbar.helpMenu.disableHardwareAcceleration": "Deaktiver hardwareacceleration og genstart", "desktopTopbar.helpMenu.enableAdvancedLogging": "Aktivér avanceret logning og genstart", "desktopTopbar.helpMenu.enableDebugLogging": "Aktiver avanceret logning og genstart", "desktopTopbar.helpMenu.enableHardwareAcceleration": "Aktivér hardwareacceleration og genstart", "desktopTopbar.helpMenu.openActivityMonitor": "Åbn aktivitetsmonitoren", "desktopTopbar.helpMenu.openConsole": "Åbn konsollen", "desktopTopbar.helpMenu.openHelpAndSupport": "Åbn hjælp og dokumentation", "desktopTopbar.helpMenu.recordingNetLog": "Registrerer netværkslog ...", "desktopTopbar.helpMenu.recordNetLog": "Registrer netværkslog ...", "desktopTopbar.helpMenu.recordNetLogConfirmation": "En netlog registreres nu i din mappe Downloads. For at stoppe registreringen skal du klikke på knappen \"Stop registrering af netværkslog\" i fejlfindingsmenuen eller forlade appen.", "desktopTopbar.helpMenu.recordNetLogFailed": "Netværksloggen blev ikke registreret. Prøv igen, eller inspicer logfilerne for at få flere oplysninger.", "desktopTopbar.helpMenu.recordNetLogFailedMessage": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, eller inspicer logfilerne for at få flere oplysninger. Fejlen var:", "desktopTopbar.helpMenu.recordNetLogFailedTitle": "Netværksloggen blev ikke registreret", "desktopTopbar.helpMenu.recordNetLogStop": "Stop registrering af netværkslog ...", "desktopTopbar.helpMenu.recordPerformanceTrace": "Optag præstationssporing...", "desktopTopbar.helpMenu.recordPerformanceTraceConfirm": "Ønsker du at optage en præstationssporing for de næste 30 sekunder? <PERSON><PERSON>r det er gjort, placeres den i din mappe Downloads.", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmCancel": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmOk": "Registrer præstationssporing", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmTitle": "Optage en præstationssporing?", "desktopTopbar.helpMenu.resetAndEraseAllLocalData": "Nulstil og slet alle lokale data", "desktopTopbar.helpMenu.showLogsInExplorer": "Vis logfiler i Stifinder", "desktopTopbar.helpMenu.showLogsInFinder": "Vis logfiler i Finder", "desktopTopbar.helpMenu.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.historyMenu.historyBack": "Tilbage", "desktopTopbar.historyMenu.historyForward": "<PERSON><PERSON><PERSON>", "desktopTopbar.historyMenu.title": "Historik", "desktopTopbar.toggleDevTools": "Slå udviklerværktøjer til/fra", "desktopTopbar.toggleWindowDevTools": "Slå vinduesudviklerværktøjer til/fra", "desktopTopbar.troubleshootingMenu.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.viewMenu.actualSize": "Faktisk størrelse", "desktopTopbar.viewMenu.forceReload": "<PERSON><PERSON> geni<PERSON>", "desktopTopbar.viewMenu.forceReloadDialog.cancel": "<PERSON><PERSON><PERSON>", "desktopTopbar.viewMenu.forceReloadDialog.message": "Du er i øjeblikket offline. Hvis du gennemtvinger genindlæsning af denne side, mister du adgangen til den, indtil du er online igen.", "desktopTopbar.viewMenu.forceReloadDialog.ok": "<PERSON><PERSON><PERSON><PERSON><PERSON> allige<PERSON>", "desktopTopbar.viewMenu.forceReloadDialog.title": "Er du sikker på, at du vil gennemtvinge genindlæsning?", "desktopTopbar.viewMenu.reload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.viewMenu.showHideSidebar": "Vis/skjul sidemenu", "desktopTopbar.viewMenu.showHideTabSpaceButton": "Vis/skjul fanebladsgrupper", "desktopTopbar.viewMenu.title": "V<PERSON><PERSON>", "desktopTopbar.viewMenu.togglefullscreen": "Slå fuld skærm til/fra", "desktopTopbar.viewMenu.zoomIn": "Zoom ind", "desktopTopbar.viewMenu.zoomOut": "Zoom ud", "desktopTopbar.whatsNewMac.title": "Åbn Nyheder i Notion til macOS", "desktopTopbar.whatsNewWindows.title": "Åbn Nyheder i Notion til Windows", "desktopTopbar.windowMenu.close": "Luk", "desktopTopbar.windowMenu.front": "Front", "desktopTopbar.windowMenu.maximize": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.minimize": "<PERSON><PERSON>", "desktopTopbar.windowMenu.showNextTab": "Vis næste fane", "desktopTopbar.windowMenu.showPreviousTab": "Vis forrige fane", "desktopTopbar.windowMenu.title": "Vindue", "desktopTopbar.windowMenu.zoom": "Zoom", "desktopTroubleshooting.resetData.cancel": "<PERSON><PERSON><PERSON>", "desktopTroubleshooting.resetData.closingWindows": "Luk Notion-vinduer", "desktopTroubleshooting.resetData.deletingFiles": "Sletter filer", "desktopTroubleshooting.resetData.done": "<PERSON><PERSON><PERSON><PERSON>", "desktopTroubleshooting.resetData.doneMessage": "Appen er blevet nulstillet.", "desktopTroubleshooting.resetData.failed": "Nogle filer blev ikke slettet under vores gendannelsesproces. Vi beklager ulejligheden. Gå til https://www.notion.so/help for at få hjælp. Hvis du vil gennemtvinge en komplet nulstilling af appen manuelt, skal du lukke Notion helt. Slet derefter følgende sti: {userDataPath}", "desktopTroubleshooting.resetData.message": "Dette vil slette alle lokale og interne data, herunder cachen og lokale indstillinger, og gendanne Notion-appen i en nyinstalleret tilstand. Det vil også logge dig ud af Notion. Dine sider og andet indhold i appen berøres ikke. Vil du fortsætte?", "desktopTroubleshooting.resetData.reset": "Nulstil alle lokale data", "desktopTroubleshooting.resetData.restart": "Genstart", "desktopTroubleshooting.resetData.title": "Nulstil og slet alle lokale data", "desktopTroubleshooting.showLogs.error.message.mac": "Notion stødte på en fejl, mens programmet forsøgte at vise logfilerne i Finder:", "desktopTroubleshooting.showLogs.error.message.windows": "Notion stødte på en fejl, mens programmet forsøgte at vise logfilerne i Explorer:", "desktopTroubleshooting.showLogs.error.title": "Visning af logfilerne mislykkedes", "desktopTroubleshooting.startRecordingNetLog": "Start registrering af netværkslog", "desktopTroubleshooting.stopRecordingNetLog": "Stop registrering af netværkslog", "menuBarIcon.menu.changeCommandSearchAndNotionAiShortcut": "Rediger genveje", "menuBarIcon.menu.changeCommandSearchShortcut": "Skift genvej til Kommandosøgning", "menuBarIcon.menu.enableQuickSearch": "Aktiver Hurtig søgning", "menuBarIcon.menu.keepInBackground": "Hold i baggrunden", "menuBarIcon.menu.launchPreferences": "Åbn Indstillinger", "menuBarIcon.menu.openOnLogin": "Åbn Notion ved Login", "menuBarIcon.menu.quitNotion": "Afslut Notion", "menuBarIcon.menu.showImmediately": "<PERSON><PERSON>", "menuBarIcon.menu.showNotionInMenuBar": "Vis Notion i menulinjen", "menuBarIcon.menu.toggleCommandSearch": "Slå Kommandosøgning til/fra", "menuBarIcon.menu.toggleNotionAi": "Skjul/vis Notion AI", "openAtLogin.dialog.detail": "{operatingSystem} forhindrede Notion i at konfigurere indstillingen \"Åbn ved login\". Dette sker som regel, når Notions opstart er blevet konfigureret i systemindstillingerne, eller hvis du ikke har tilstrækkelig tilladelse. Du kan stadig konfigurere denne indstilling manuelt i systemindstillingerne.", "openAtLogin.dialog.title": "Åbn ved Login", "tabSpaces.deleteDialog.cancelButton": "<PERSON><PERSON><PERSON>", "tabSpaces.deleteDialog.deleteButton": "Slet", "tabSpaces.deleteDialog.detail": "Alle faner i denne fanegruppe vil blive fjernet fra gruppen.", "tabSpaces.deleteDialog.title": "Vil du slette fanegruppen \"{title}\"?", "tabSpaces.snackbar.switchedToTabGroup": "Skiftede til {title}", "tabSpaces.snackbar.switchedToUngroupedTabs": "Skiftede til ugrupperede faner", "tabSpaces.snackbar.tabGroupPlaceholder": "Fanegruppe", "updatePrompt.detail": "Vil du installere den nu? Vi <PERSON><PERSON> dine vinduer og faner igen for dig.", "updatePrompt.installAndRelaunch": "Installer og genstart", "updatePrompt.message": "Ny version af Notion er tilgængelig!", "updatePrompt.remindMeLater": "<PERSON><PERSON><PERSON> mig senere", "window.closeDialog.cancelButton": "<PERSON><PERSON><PERSON>", "window.closeDialog.confirmButton": "Luk", "window.closeDialog.title.app": "Luk Notion?", "window.closeDialog.title.tab": "<PERSON><PERSON> Notion-fanen?", "window.closeDialog.title.window": "Luk Notion-vinduet?", "window.loadingError.message": "<PERSON><PERSON>l ved indlæsning af Notion, opret forbindelse til internettet for at komme i gang.", "window.loadingError.reload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "window.movedTabSnackbarMessage": "Flyttede {tabTitle} til {tabSpaceTitle}", "window.tabLoadingError.cancel": "<PERSON><PERSON><PERSON><PERSON>", "window.tabMenu.closeOtherTabs": "<PERSON><PERSON> andre faner", "window.tabMenu.closeTab": "<PERSON><PERSON> fane", "window.tabMenu.closeTabsToLeft": "Luk faner til venstre", "window.tabMenu.closeTabsToRight": "Luk faner til højre", "window.tabMenu.copyLink": "Kopiér link", "window.tabMenu.duplicateTab": "<PERSON><PERSON><PERSON><PERSON> fane", "window.tabMenu.moveTo": "Flyt til", "window.tabMenu.moveToNewWindow": "Flyt fanen til nyt vindue", "window.tabMenu.moveToSubmenuNewWindow": "Nyt vindue", "window.tabMenu.pinTab": "<PERSON><PERSON><PERSON><PERSON> fane", "window.tabMenu.refresh": "<PERSON><PERSON><PERSON> fane", "window.tabMenu.reopenClosedTab": "Åbn den senest lukkede fane igen", "window.tabMenu.replacePinnedTabUrl": "Udskift Fastgjort URL med Aktuel", "window.tabMenu.returnToPinnedTabUrl": "Tilbage til fastgjort URL", "window.tabMenu.ungroupTab": "<PERSON><PERSON> fane", "window.tabMenu.unpinTab": "<PERSON><PERSON><PERSON><PERSON> fane", "window.tabTitlePlaceholder": "<PERSON><PERSON>", "window.ungroupedTabSnackbarMessage": "Opdelte {tabTitle}"}