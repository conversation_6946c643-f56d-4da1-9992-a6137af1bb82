{"name": "native-progress-bar", "version": "1.0.3", "description": "This module allows your Electron app to display native dialogs with progress bars in them on Windows and macOS.", "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"build": "npm run build-ts && npm run build-native", "build-ts": "tsc", "build-native": "node-gyp clean && node-gyp configure && node-gyp build", "test": "cd test && npm run start && cd -", "prettier": "npx prettier --write .", "prepack": "npm run build-ts"}, "keywords": ["progress", "progressbar", "bar", "dialog", "cocoa", "native", "macos", "windows", "win32", "electron"], "repository": {"type": "git", "url": "https://github.com/felixrieseberg/native-progress-bar.git"}, "author": {"email": "<EMAIL>", "name": "<PERSON>", "url": "http://www.felixrieseberg.com"}, "license": "MIT", "dependencies": {"bindings": "^1.5.0"}, "devDependencies": {"@types/bindings": "^1.5.5", "typescript": "^5.6.3"}}