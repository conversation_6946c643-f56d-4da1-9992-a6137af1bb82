(()=>{"use strict";var e={4:(e,n)=>{function t(e){return null!==e}function o(e){return null!=e}Object.defineProperty(n,"__esModule",{value:!0}),n.Info=n.DeprecatedAPI=n.objectAssign=n.objectEntries=n.objectKeys=void 0,n.isNonEmptyArray=function(e){return e.length>0},n.isKeyInObject=function(e,n){return n in e},n.isKeyInMap=function(e,n){return e.has(n)},n.getKeyInMap=function(e,n){return e.get(n)},n.arrayIncludes=function(e,n){return e.includes(n)},n.setIncludes=function(e,n){return e.has(n)},n.isNotNull=t,n.isDefined=function(e){return void 0!==e},n.isNotNullish=o,n.isNullish=function(e){return!o(e)},n.nullableToUndefinable=function(e){return t(e)?e:void 0},n.unreachable=function(e,n){if(n)throw new r(n());let t="(unknown)";try{try{t=JSON.stringify(e)??"undefined"}catch(n){t=String(e);const o=n instanceof Error?n.message:void 0;o&&(t+=` (Not serializable: ${o})`)}}catch{}throw new r(`Expected value to never occur: ${t}`)},n.isObject=function(e){return"object"==typeof e&&null!==e},n.oneOf=function(e){return n=>function(e,n){return n.some(n=>n(e))}(n,e)},n.propertyOf=function(e){return e.toString()},n.Opaque=function(e,n){return e},n.stringStartsWith=function(e,n){return e.startsWith(n)},n.safeCast=function(e){return e},n.mapObject=function(e,t){const o={};for(const[r,i]of(0,n.objectEntries)(e))o[r]=t(i,r);return o},n.objectKeys=Object.keys,n.objectEntries=Object.entries,n.objectAssign=Object.assign;class r extends Error{}n.DeprecatedAPI=Symbol("deprecated api name"),Symbol("abstracted api name"),n.Info=Symbol("info message"),Symbol("warning message")},25:(e,n,t)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.preferences=void 0;const o=t(4),r=t(326);n.preferences={isProtocolRegistered:(0,r.invokerInMain)("notion:get-is-protocol-registered"),setTheme:(0,r.senderToMain)("notion:set-theme"),electronAppFeatures:{get:(0,r.invokerInMain)("notion:get-electron-app-features"),setPreference:(0,r.senderToMain)("notion:set-user-preference"),...(0,r.getSimpleEmitter)("notion:set-electron-app-features")},setGlobalShortcutsEnabled:(0,r.senderToMain)("notion:set-global-shortcuts-enabled"),onOpenSettings:(0,r.getSimpleEmitter)("notion:open-settings"),openSystemSettings:(0,o.safeCast)((0,r.senderToMain)("notion:open-system-settings"))}},232:(e,n,t)=>{n.R=void 0;const o=t(288),r=t(326),i=t(306);n.R={tabBarState:(0,r.getSimpleEmitter)("tabs:set-state"),tabSpacesState:(0,r.getSimpleEmitter)("tabs:set-tab-spaces-state"),tabSpacesMenuOpenState:(0,r.getSimpleEmitter)("tabs:set-tab-spaces-menu-open-state"),windowSidebarState:(0,r.getSimpleEmitter)("tabs:set-window-sidebar-state"),zenModeState:(0,r.getSimpleEmitter)("tabs:set-zen-mode"),navigationState:(0,r.getSimpleEmitter)("tabs:set-navigation"),navigationHistory:{handleBackMetaClick:()=>{(0,r.sendToMain)("notion:navigation-meta-click","back")},handleForwardMetaClick:()=>{(0,r.sendToMain)("notion:navigation-meta-click","forward")},handleTabHistoryMenu:(0,r.senderToMain)("notion:show-tab-history-menu")},tabBarClicked:(0,r.senderToMain)("notion:tab-bar-clicked"),goBack:(0,r.senderToMain)("notion:go-back"),goForward:(0,r.senderToMain)("notion:go-forward"),newTab:(0,r.senderToMain)("notion:new-tab-from-tab-bar"),toggleSidebarExpansion:(0,r.senderToMain)("notion:toggle-sidebar-expanded"),setSidebarOpen:(0,r.senderToMain)("notion:set-sidebar-open"),toggleTabSpacesMenuOpen:(0,r.senderToMain)("notion:toggle-tab-spaces-menu-open"),toggleMaximized:(0,r.senderToMain)("notion:toggle-maximized"),handleTabClicked:(0,r.senderToMain)("notion:tab-clicked"),handleTabClose:(0,r.senderToMain)("notion:close-tab"),handleTabMenu:(0,r.senderToMain)("notion:show-tab-menu"),updateTabOrder:(0,r.senderToMain)("notion:set-tab-order"),openApplicationMenu:(0,r.senderToMain)("notion:open-app-menu"),moveTabToNewWindow:(0,r.senderToMain)("notion:move-tab-to-new-window"),insertTabFromExternalWindow:(0,r.senderToMain)("notion:insert-tab-from-external-window"),setHoveredTab:(0,r.senderToMain)("notion:set-hovered-tab"),clearHoveredTab:(0,r.senderToMain)("notion:clear-hovered-tab")},window.addEventListener("dragstart",e=>{e.dataTransfer&&(e.dataTransfer.effectAllowed="copyMove")}),window.addEventListener("dragleave",e=>{e.dataTransfer&&(e.dataTransfer.dropEffect="copy")}),o.contextBridge.exposeInMainWorld("tabsApi",n.R),(0,i.setupProcessListeners)()},239:(e,n,t)=>{Object.defineProperty(n,"__esModule",{value:!0});const o=t(272);n.default=o},272:e=>{e.exports=JSON.parse('{"env":"production","commit":"05c80f1","isLocalhost":false,"domainName":"www.notion.so","domainBaseUrl":"https://www.notion.so","protocol":"notion","desktopAppId":"notion.id","offline":true,"desktopS3Url":"https://desktop-release.notion-static.com","splunkConfig":{"host":"http-inputs-notion.splunkcloud.com","path":"services/collector/raw","token":"EA76605A-F565-4B17-A496-34435622A1EB","maxBatchCount":0,"port":443},"mail":{"domainBaseUrl":"https://mail.notion.so"},"syncSession":{"cookieName":"p_sync_session","domain":".notion.so"},"targetPlatform":"windows"}')},288:e=>{e.exports=require("electron")},306:function(e,n,t){var o,r=this&&this.__createBinding||(Object.create?function(e,n,t,o){void 0===o&&(o=t);var r=Object.getOwnPropertyDescriptor(n,t);r&&!("get"in r?!n.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return n[t]}}),Object.defineProperty(e,o,r)}:function(e,n,t,o){void 0===o&&(o=t),e[o]=n[t]}),i=this&&this.__setModuleDefault||(Object.create?function(e,n){Object.defineProperty(e,"default",{enumerable:!0,value:n})}:function(e,n){e.default=n}),a=this&&this.__importStar||(o=function(e){return o=Object.getOwnPropertyNames||function(e){var n=[];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},o(e)},function(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var t=o(e),a=0;a<t.length;a++)"default"!==t[a]&&r(n,e,t[a]);return i(n,e),n}),s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(n,"__esModule",{value:!0}),n.setupProcessListeners=function(){(0,c.handleMainToRendererRequest)("notion:get-process-heap-stats",()=>[process.getHeapStatistics()]),(0,c.handleMainToRendererRequest)("notion:get-blink-memory-info",()=>[process.getBlinkMemoryInfo()]),(0,c.handleMainToRendererRequest)("notion:get-argv",()=>[process.argv]),c.handleMainToRendererEvent.addListener("notion:crash",async()=>{if("production"===d.default.env){const{preferences:e}=await Promise.resolve().then(()=>a(t(25))),{preferences:n}=await e.electronAppFeatures.get(),o=n?.isDebugMenuEnabled;if(!o)return}console.warn("Inducing crash as requested "),process.crash()})};const d=s(t(239)),c=t(326)},326:function(e,n,t){var o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(n,"__esModule",{value:!0}),n.handleMainToRendererEvent=void 0,n.getSimpleEmitter=function(e){return{addListener:t=>n.handleMainToRendererEvent.addListener(e,t),removeListener(t){n.handleMainToRendererEvent.removeListener(e,t)},listeners:()=>n.handleMainToRendererEvent.listeners(e)}},n.invokeInMainAndReturnResult=i,n.invokerInMain=function(e){return(...n)=>i(e,...n)},n.sendToMain=a,n.senderToMain=function(e){return(...n)=>a(e,...n)},n.handleMainToRendererRequest=function(e,n){const t=async(t,...o)=>{const i=await n(...o);r.default.ipcRenderer.send(e,...i)};return r.default.ipcRenderer.addListener(e,t),()=>r.default.ipcRenderer.removeListener(e,t)};const r=o(t(288));function i(e,...n){return r.default.ipcRenderer.invoke(e,...n)}function a(e,...n){r.default.ipcRenderer.send(e,...n)}n.handleMainToRendererEvent={addListener(e,n){const t=n;return r.default.ipcRenderer.addListener(e,t),()=>r.default.ipcRenderer.removeListener(e,t)},removeListener(e,n){r.default.ipcRenderer.removeListener(e,n)},listeners:e=>r.default.ipcRenderer.listeners(e),once(e,n){r.default.ipcRenderer.once(e,n)}}}},n={};function t(o){var r=n[o];if(void 0!==r)return r.exports;var i=n[o]={exports:{}};return e[o].call(i.exports,i,i.exports,t),i.exports}void 0!==t&&(t.ab="/native_modules/"),t(232)})();
//# sourceMappingURL=preload.js.map