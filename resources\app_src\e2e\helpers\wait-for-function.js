"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.waitForFunction = waitForFunction;
async function waitForFunction(fn, timeout = 10000) {
    let delay = 100;
    const maxDelay = 5000;
    const multiplier = 1.5;
    const startTime = Date.now();
    while (true) {
        if (Date.now() - startTime > timeout) {
            throw new Error(`Timed out after ${timeout}ms waiting for function to return value`);
        }
        const result = await fn();
        if (result !== undefined) {
            return result;
        }
        await new Promise(resolve => setTimeout(resolve, delay));
        delay = Math.min(delay * multiplier, maxDelay);
    }
}
