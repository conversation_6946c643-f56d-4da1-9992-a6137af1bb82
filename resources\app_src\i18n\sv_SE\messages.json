{"activityMonitor.copyDiagnosticInformation": "<PERSON><PERSON><PERSON> diagnostikinformation", "activityMonitor.copyExecutablePath": "<PERSON><PERSON>ra körbar sökväg", "activityMonitor.copyUrl": "<PERSON><PERSON><PERSON> web<PERSON>", "activityMonitor.forceKillProcess": "Tvinga processen att avslutas", "activityMonitor.inspectActivityMonitor": "Granska aktivitetsmonitorn", "activityMonitor.killProcess": "Avsluta process", "activityMonitor.openDevTools": "Öppna utvecklingsverktyg", "activityMonitor.reload": "<PERSON><PERSON><PERSON> in igen", "clientPlaceholder.placeholderDescription": "<PERSON><PERSON><PERSON> för att visa i det här fönstret", "clientPlaceholder.placeholderTitle": "Visas för närvarande i ett annat fönster", "commandSearch.window.title": "Notion – Kommandosökning", "crashWatchdog.dialog.abnormalExit": "Onormal stängning: sidan stängdes med en utgångskod som inte är noll.", "crashWatchdog.dialog.buttonCloseTab": "Stäng flik", "crashWatchdog.dialog.buttonCloseWindow": "<PERSON>äng fönster", "crashWatchdog.dialog.buttonRestartApp": "Starta om Notion", "crashWatchdog.dialog.crashed": "Kraschade: sidan har kraschat av okänd anledning.", "crashWatchdog.dialog.details": "Sidans webbadress: {url} Anledning: {reason} Utgångskod: {exitCode}", "crashWatchdog.dialog.integrityFailure": "Integritetsfel: sidan kunde inte genomföra kodintegritetskontroller.", "crashWatchdog.dialog.killed": "Avslutad: sidan har avslutats av en extern process.", "crashWatchdog.dialog.launchFailed": "Start misslyckades: processen kunde inte startas.", "crashWatchdog.dialog.message": "<PERSON><PERSON><PERSON> gick fel när den här fliken skulle visas och vi kunde inte återställa den automatiskt.", "crashWatchdog.dialog.oom": "Slut på minne: sidan fick slut på minne och har kraschat.", "crashWatchdog.dialog.title": "<PERSON><PERSON><PERSON> gick fel", "crashWatchdog.dialog.urlUnknown": "Okä<PERSON>", "desktop.activityMonitor.all": "<PERSON>a", "desktop.activityMonitor.cpuPercent": "% CPU", "desktop.activityMonitor.cpuPercentDescription": "Den del av tillgänglig CPU-tid som används av processen.", "desktop.activityMonitor.cpuTime": "CPU-tid", "desktop.activityMonitor.cpuTimeDescription": "Den totala CPU-tiden i sekunder som har använts sedan processen startade.", "desktop.activityMonitor.creationTime": "Skapad", "desktop.activityMonitor.creationTimeDescription": "Tiden sedan processen skapades.", "desktop.activityMonitor.frames": "Bilder", "desktop.activityMonitor.framesDescription": "Antalet bilder som hanteras av processen. Många renderingsprocesser ansvarar för flera bilder.", "desktop.activityMonitor.hidden": "<PERSON><PERSON>", "desktop.activityMonitor.hideColumns": "<PERSON><PERSON><PERSON><PERSON>", "desktop.activityMonitor.hideFilters": "<PERSON><PERSON><PERSON><PERSON> filter", "desktop.activityMonitor.idleWakeupsPerSecond": "Väckningar från viloläge", "desktop.activityMonitor.idleWakeupsPerSecondDescription": "Antalet gånger som processen har väckt CPU:n sedan den senaste uppdateringen av aktivitetsmonitorn.", "desktop.activityMonitor.loading": "Laddar aktivitetsdata", "desktop.activityMonitor.memCurrent": "<PERSON><PERSON> (nuvarande)", "desktop.activityMonitor.memCurrentDescription": "Processens ”arbetsuppsättning”, vilket är den uppsättning sidor som för närvarande finns i RAM-minnet. Detta tal tar inte hänsyn till operativsystemets minneskomprimering, hantering av inaktiva och cachade sidor eller andra minnes<PERSON>teringstekniker. Den mängd fysiskt minne som används av processen är sannolikt mycket mindre.", "desktop.activityMonitor.memPeak": "<PERSON><PERSON> (högst)", "desktop.activityMonitor.memPeakDescription": "Processens maximala storlek för arbetsuppsättningen, vilket är den maximala mängden fysiskt minne som används av processen sedan den startades. Storleken på processens ”arbetsuppsättning”, vilket är den uppsättning sidor som för närvarande finns i RAM-minnet. Detta tal tar inte hänsyn till operativsystemets minneskomprimering, hantering av inaktiva och cachade sidor eller andra minneshanteringstekniker. Den mängd fysiskt minne som används av processen är sannolikt mycket mindre.", "desktop.activityMonitor.memPrivate": "<PERSON><PERSON> (privat)", "desktop.activityMonitor.memPrivateDescription": "Mängden fysiskt minne som tilldelas av processen och som inte delas med andra processer, till exempel JS-heap eller HTML-innehåll.", "desktop.activityMonitor.memShared": "<PERSON><PERSON> (delat)", "desktop.activityMonitor.memSharedDescription": "Mängden fysiskt minne som tilldelas av processen och som delas med andra processer, till exempel delade bibliotek eller mappade filer.", "desktop.activityMonitor.mixed": "<PERSON><PERSON><PERSON>", "desktop.activityMonitor.parentWindowId": "ID för överordnat fönster", "desktop.activityMonitor.parentWindowIdDescription": "Den unika identifieraren för fönstret som innehåller den här fliken.", "desktop.activityMonitor.pid": "Permanenta identifikatorer (PID)", "desktop.activityMonitor.pidDescription": "Processens process-id som används av operativsystemet.", "desktop.activityMonitor.processName": "Processnamn", "desktop.activityMonitor.showColumns": "Visa kolumner", "desktop.activityMonitor.showFilters": "Visa filter", "desktop.activityMonitor.tabId": "Flik-ID", "desktop.activityMonitor.tabIdDescription": "Den unika identifieraren för fliken i Notion-appen.", "desktop.activityMonitor.type": "<PERSON><PERSON>", "desktop.activityMonitor.url": "URL", "desktop.activityMonitor.urlDescription": "Processens webbadress. Många renderingsprocesser ansvarar för flera bilder. Se kolumnen Bilder för mer information.", "desktop.activityMonitor.visibilityState": "synlighet", "desktop.activityMonitor.visibilityStateDescription": "Processens synlighetsstatus. Om processen är en renderingsprocess är detta huvudbildens synlighetsstatus.", "desktop.activityMonitor.visible": "synlig", "desktop.tabBar.backButtonLabel": "Tillbaka", "desktop.tabBar.closeSidebarLabel": "Stäng sidofältet", "desktop.tabBar.closeTabLabel": "<PERSON><PERSON><PERSON> flik, {tabTitle}", "desktop.tabBar.forwardButtonLabel": "<PERSON><PERSON><PERSON>", "desktop.tabBar.newTabButtonLabel": "Ny flik", "desktop.tabBar.openSidebarLabel": "<PERSON><PERSON><PERSON> sido<PERSON>", "desktop.tabBar.tabSpacesLabel": "Flikutrymmen", "desktopExtensions.install.failed.title": "Det gick inte att installera tillägget", "desktopExtensions.manage.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopExtensions.manage.disable": "Inaktivera", "desktopExtensions.manage.enable": "Aktivera", "desktopExtensions.manage.message": "Vad vill du göra med {extensionTitle} {extensionVersion}?", "desktopExtensions.manage.title": "<PERSON><PERSON>", "desktopExtensions.manage.uninstall": "Avin<PERSON>lera", "desktopExtensions.manage.unload": "Lasta av", "desktopExtensions.openFailed.noPopupMessage": "Inget popupfönster har specificerats för det här tillägget (action.default_popup)", "desktopExtensions.openFailed.noPopupTitle": "Det gick inte att öppna tillägget", "desktopExtensions.unzip.failed.badFileRead": "Det gick inte att läsa CRX-filen: {error}", "desktopExtensions.unzip.failed.badFileWrite": "Det gick inte att skriva filen {filePath}: {error}", "desktopExtensions.unzip.failed.badFolderCreate": "Det gick inte att skapa tilläggsmappen på {extensionPath}: {error}", "desktopExtensions.unzip.failed.badManifest": "manifest.json kunde inte parsas i det här tillägget. Är det ett giltig tillägg? Felet var: {error}", "desktopExtensions.unzip.failed.badManifestNoName": "Inget namn hittades i manifest.json", "desktopExtensions.unzip.failed.error": "Det gick inte att packa upp CRX-filen: {error}", "desktopExtensions.unzip.failed.noManifest": "Ingen manifest.json hittades i CRX. Är det ett giltig tillägg?", "desktopInstaller.failedToMove.detail": "Det gick inte att flytta appen till din Applikations-mapp. Flytta den manuellt.", "desktopInstaller.failedToMove.title": "Det gick inte att flytta appen", "desktopInstaller.invalidInstallDialog.cancelButton.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopInstaller.invalidInstallDialog.confirmMove": "Din Notion-applikation är inte korrekt installerad. Kan vi flytta din Notion-app till din Applikations-mapp?", "desktopInstaller.invalidInstallDialog.okButton.label": "OK", "desktopInstaller.invalidInstallDialog.title": "Ogiltig installation", "desktopTopbar.appMenu.about": "Om Notion", "desktopTopbar.appMenu.checkForUpdate": "<PERSON><PERSON><PERSON> efter uppdateringar …", "desktopTopbar.appMenu.checkForUpdate.noUpdateAvailable": "Du har den senaste versionen av Notion!", "desktopTopbar.appMenu.checkForUpdate.title": "<PERSON><PERSON><PERSON> efter upp<PERSON><PERSON><PERSON>", "desktopTopbar.appMenu.checkForUpdate.updateAvailable": "En ny version av Notion är tillgänglig och laddas för närvarande ner i bakgrunden. Tack för att du håller dig uppdaterad!", "desktopTopbar.appMenu.checkForUpdate.updateCheckFailed": "Notion lyckades inte upprätta en anslutning till uppdateringsservern, antingen på grund av problem med din Internetanslutning eller uppdateringsservern. Försök igen senare.", "desktopTopbar.appMenu.downloadingUpdate": "<PERSON>dda ned uppdatering ({percentage}%)", "desktopTopbar.appMenu.hide": "D<PERSON><PERSON>j <PERSON>", "desktopTopbar.appMenu.hideOthers": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "desktopTopbar.appMenu.preferences": "Inställningar …", "desktopTopbar.appMenu.quit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.appMenu.quitWithoutSavingTabs": "Stäng utan att spara flikarna", "desktopTopbar.appMenu.restartToApplyUpdate": "Starta om för att tillämpa uppdatering", "desktopTopbar.appMenu.services": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.appMenu.unhide": "Visa alla", "desktopTopbar.editMenu.copy": "<PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.copyLinkToCurrentPage": "Kopiera länken till den aktuella sidan", "desktopTopbar.editMenu.copyLinkToCurrentPageBlockTitle": "Kopiera den aktuella sidans namn", "desktopTopbar.editMenu.cut": "<PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.paste": "Klistra in", "desktopTopbar.editMenu.pasteAndMatchStyle": "<PERSON><PERSON><PERSON> in och matcha stil", "desktopTopbar.editMenu.redo": "<PERSON><PERSON><PERSON> om", "desktopTopbar.editMenu.selectAll": "<PERSON><PERSON><PERSON><PERSON> alla", "desktopTopbar.editMenu.speech": "<PERSON>l", "desktopTopbar.editMenu.speech.startSpeaking": "<PERSON><PERSON><PERSON><PERSON> tala", "desktopTopbar.editMenu.speech.stopSpeaking": "<PERSON><PERSON>a tala", "desktopTopbar.editMenu.title": "Rediger<PERSON>", "desktopTopbar.editMenu.undo": "Å<PERSON><PERSON>", "desktopTopbar.extensionsMenu.install": "Installera tillägg ...", "desktopTopbar.extensionsMenu.manage": "<PERSON><PERSON>", "desktopTopbar.fileMenu.close": "<PERSON>äng fönster", "desktopTopbar.fileMenu.closeTab": "Stäng flik", "desktopTopbar.fileMenu.newNotionWindow": "Nytt Notion-fönster", "desktopTopbar.fileMenu.newTab": "Ny flik", "desktopTopbar.fileMenu.newWindow": "<PERSON><PERSON><PERSON> fönster", "desktopTopbar.fileMenu.print": "Skriv ut ...", "desktopTopbar.fileMenu.quit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.fileMenu.quitWithoutSavingTabs": "Av<PERSON><PERSON>a utan att spara flikarna", "desktopTopbar.fileMenu.reopenClosedTab": "Öppna senast stängda flik igen", "desktopTopbar.fileMenu.title": "Fil", "desktopTopbar.helpMenu.copyInstallId": "Kopiera installations-ID", "desktopTopbar.helpMenu.disableAdvancedLogging": "Inaktivera avancerad loggning och starta om", "desktopTopbar.helpMenu.disableDebugLogging": "Avaktivera avancerad loggning och omstart", "desktopTopbar.helpMenu.disableHardwareAcceleration": "Inaktivera hårdvaruacceleration och omstart", "desktopTopbar.helpMenu.enableAdvancedLogging": "Aktivera avancerad loggning och starta om", "desktopTopbar.helpMenu.enableDebugLogging": "Aktivera avancerad loggning och omstart", "desktopTopbar.helpMenu.enableHardwareAcceleration": "Aktivera hårdvaruacceleration och omstart", "desktopTopbar.helpMenu.openActivityMonitor": "Öppna aktivitetsmonitorn", "desktopTopbar.helpMenu.openConsole": "Ö<PERSON>na konsolen", "desktopTopbar.helpMenu.openHelpAndSupport": "Öppna hjälpen och dokumentationen", "desktopTopbar.helpMenu.recordingNetLog": "Registrerar nätverkslogg …", "desktopTopbar.helpMenu.recordNetLog": "Registrera nätverkslogg …", "desktopTopbar.helpMenu.recordNetLogConfirmation": "En nätverkslogg registreras nu i mappen Nedladdningar. F<PERSON>r att stoppa registreringen klickar du på knappen ”Stoppa registrering av nätverkslogg” i felsökningsmenyn eller avslutar appen.", "desktopTopbar.helpMenu.recordNetLogFailed": "Det gick inte att registrera nätverksloggen. Försök igen eller inspektera loggarna för mer information.", "desktopTopbar.helpMenu.recordNetLogFailedMessage": "Försök igen eller inspektera loggarna för mer information. Felet var:", "desktopTopbar.helpMenu.recordNetLogFailedTitle": "Det gick inte att registrera nätverksloggen", "desktopTopbar.helpMenu.recordNetLogStop": "Stoppa registrering av nätverkslogg …", "desktopTopbar.helpMenu.recordPerformanceTrace": "<PERSON><PERSON><PERSON> in prestandaspårning …", "desktopTopbar.helpMenu.recordPerformanceTraceConfirm": "Vill du spela in ett prestandaspår under de kommande 30 sekunderna? När du är klar kommer den att placeras i mappen Nedladdningar.", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmOk": "<PERSON><PERSON>a in prestandaspårning", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmTitle": "S<PERSON>a in ett prestandaspår?", "desktopTopbar.helpMenu.resetAndEraseAllLocalData": "Återställ och radera alla lokala data", "desktopTopbar.helpMenu.showLogsInExplorer": "Visa loggar i Explorer", "desktopTopbar.helpMenu.showLogsInFinder": "Visa loggar i Finder", "desktopTopbar.helpMenu.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.historyMenu.historyBack": "Tillbaka", "desktopTopbar.historyMenu.historyForward": "<PERSON><PERSON><PERSON>", "desktopTopbar.historyMenu.title": "Historik", "desktopTopbar.toggleDevTools": "Växla utvecklarverktyg", "desktopTopbar.toggleWindowDevTools": "Växla Window Developer Tools", "desktopTopbar.troubleshootingMenu.title": "Felsökning", "desktopTopbar.viewMenu.actualSize": "Faktisk storlek", "desktopTopbar.viewMenu.forceReload": "Tvinga omladdning", "desktopTopbar.viewMenu.forceReloadDialog.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.viewMenu.forceReloadDialog.message": "Du är för närvarande offline. Om du tvingar fram en omladdning av den här sidan förlorar du åtkomst till den tills du är online igen.", "desktopTopbar.viewMenu.forceReloadDialog.ok": "Ladda om ändå", "desktopTopbar.viewMenu.forceReloadDialog.title": "Är du säker på att du vill tvinga fram en omladdning?", "desktopTopbar.viewMenu.reload": "<PERSON><PERSON><PERSON> in igen", "desktopTopbar.viewMenu.showHideSidebar": "Visa/dölj sidofält", "desktopTopbar.viewMenu.showHideTabSpaceButton": "Visa/dölj flikgrupper", "desktopTopbar.viewMenu.title": "Visa", "desktopTopbar.viewMenu.togglefullscreen": "Växla <PERSON>ä<PERSON>", "desktopTopbar.viewMenu.zoomIn": "Zooma in", "desktopTopbar.viewMenu.zoomOut": "Zooma ut", "desktopTopbar.whatsNewMac.title": "Öppna Nyheter i Notion för macOS", "desktopTopbar.whatsNewWindows.title": "Öppna Nyheter i Notion för Windows", "desktopTopbar.windowMenu.close": "Stäng", "desktopTopbar.windowMenu.front": "Framsida", "desktopTopbar.windowMenu.maximize": "<PERSON><PERSON>", "desktopTopbar.windowMenu.minimize": "Minimera", "desktopTopbar.windowMenu.showNextTab": "Visa nästa flik", "desktopTopbar.windowMenu.showPreviousTab": "Visa föregående flik", "desktopTopbar.windowMenu.title": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.zoom": "Zoom", "desktopTroubleshooting.resetData.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopTroubleshooting.resetData.closingWindows": "Stänger Notion-fönster", "desktopTroubleshooting.resetData.deletingFiles": "<PERSON><PERSON><PERSON> filer", "desktopTroubleshooting.resetData.done": "<PERSON><PERSON><PERSON>", "desktopTroubleshooting.resetData.doneMessage": "<PERSON><PERSON><PERSON> har <PERSON>ä<PERSON>.", "desktopTroubleshooting.resetData.failed": "Vår återställningsprocess kunde inte radera vissa filer. Vi ber om ursäkt för besväret – besök https://www.notion.so/help för att få hjälp. Stäng Notion helt för att manuellt tvinga fram en fullständig återställning av appen. Sedan raderar du följande sökväg: {userDataPath}", "desktopTroubleshooting.resetData.message": "Det här raderar alla lokala och interna data, inklusive cacheminnet och lokala inställningar, och återställer Notion-appen till ett nyinstallerat tillstånd. Det kommer också att logga ut dig från Notion. Dina sidor och annat innehåll i appen kommer att lämnas orörda. Vill du fortsätta?", "desktopTroubleshooting.resetData.reset": "Återställ alla lokala data", "desktopTroubleshooting.resetData.restart": "Starta om", "desktopTroubleshooting.resetData.title": "Återställa och radera alla lokala data", "desktopTroubleshooting.showLogs.error.message.mac": "Notion stötte på ett fel när du försökte visa loggarna i Finder:", "desktopTroubleshooting.showLogs.error.message.windows": "Notion stötte på ett fel när du försökte visa loggarna i Explorer:", "desktopTroubleshooting.showLogs.error.title": "Det gick inte att visa loggarna", "desktopTroubleshooting.startRecordingNetLog": "Starta registrering av nätverkslogg", "desktopTroubleshooting.stopRecordingNetLog": "Stoppa registrering av nätverkslogg", "menuBarIcon.menu.changeCommandSearchAndNotionAiShortcut": "<PERSON><PERSON><PERSON> snabb<PERSON>ndon", "menuBarIcon.menu.changeCommandSearchShortcut": "<PERSON><PERSON> sna<PERSON>mmandon för kommandosökning", "menuBarIcon.menu.enableQuickSearch": "Aktivera snabbsökning", "menuBarIcon.menu.keepInBackground": "Behåll i bakgrunden", "menuBarIcon.menu.launchPreferences": "Startinställningar", "menuBarIcon.menu.openOnLogin": "Öppna Notion vid inloggning", "menuBarIcon.menu.quitNotion": "Avsluta Notion", "menuBarIcon.menu.showImmediately": "Visa omedelbart", "menuBarIcon.menu.showNotionInMenuBar": "Visa Notion i menyfältet", "menuBarIcon.menu.toggleCommandSearch": "Växla kommandosökning", "menuBarIcon.menu.toggleNotionAi": "Öppna/stäng Notion AI", "openAtLogin.dialog.detail": "{operatingSystem} förhindrade att Notion konfigurerade inställningen Öppna vid inloggning. Det här inträffar vanligtvis när Notions uppstart har konfigurerats i systeminställningarna eller om du inte har tillräcklig behörighet. Du kan fortfarande konfigurera inställningen manuellt i systeminställningarna.", "openAtLogin.dialog.title": "Öppna vid inloggning", "tabSpaces.deleteDialog.cancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tabSpaces.deleteDialog.deleteButton": "<PERSON><PERSON><PERSON>", "tabSpaces.deleteDialog.detail": "Alla flikar i den här flikgruppen kommer att avgrupperas.", "tabSpaces.deleteDialog.title": "Vill du radera flikgruppen ”{title}”?", "tabSpaces.snackbar.switchedToTabGroup": "<PERSON><PERSON><PERSON><PERSON> till {title}", "tabSpaces.snackbar.switchedToUngroupedTabs": "Växlade till ogrupperade flikar", "tabSpaces.snackbar.tabGroupPlaceholder": "Flikgrupp", "updatePrompt.detail": "Vill du installera den nu? Vi öppnar dina fönster och flikar igen.", "updatePrompt.installAndRelaunch": "Installera och starta om", "updatePrompt.message": "En ny version av Notion finns tillgänglig.", "updatePrompt.remindMeLater": "<PERSON><PERSON><PERSON><PERSON> mig senare", "window.closeDialog.cancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "window.closeDialog.confirmButton": "Stäng", "window.closeDialog.title.app": "Stänga Notion?", "window.closeDialog.title.tab": "Stänga Notion-fliken?", "window.closeDialog.title.window": "Stänga Notion-fönstret?", "window.loadingError.message": "Notion kunde inte laddas. Anslut till internet för att komma igång.", "window.loadingError.reload": "<PERSON><PERSON><PERSON> in igen", "window.movedTabSnackbarMessage": "Flyttade {tabTitle} till {tabSpaceTitle}", "window.tabLoadingError.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "window.tabMenu.closeOtherTabs": "<PERSON><PERSON><PERSON> andra flikar", "window.tabMenu.closeTab": "Stäng flik", "window.tabMenu.closeTabsToLeft": "Stäng flikar till vänster", "window.tabMenu.closeTabsToRight": "<PERSON><PERSON><PERSON> flikar till höger", "window.tabMenu.copyLink": "<PERSON><PERSON><PERSON> länk", "window.tabMenu.duplicateTab": "<PERSON><PERSON><PERSON><PERSON> fliken", "window.tabMenu.moveTo": "Flytta till", "window.tabMenu.moveToNewWindow": "Flytta fliken till ett nytt fönster", "window.tabMenu.moveToSubmenuNewWindow": "<PERSON><PERSON><PERSON> fönster", "window.tabMenu.pinTab": "<PERSON><PERSON><PERSON> flik", "window.tabMenu.refresh": "Upp<PERSON>ra fliken", "window.tabMenu.reopenClosedTab": "Öppna senast den stängda fliken igen", "window.tabMenu.replacePinnedTabUrl": "Ersätt den fästa URL:n med den aktuella URL:n", "window.tabMenu.returnToPinnedTabUrl": "Återgå till den fästa webbadressen", "window.tabMenu.ungroupTab": "Dela upp flik", "window.tabMenu.unpinTab": "<PERSON><PERSON> flik", "window.tabTitlePlaceholder": "Flik", "window.ungroupedTabSnackbarMessage": "{tabTitle} har delats upp"}