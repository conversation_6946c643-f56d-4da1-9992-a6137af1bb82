{"activityMonitor.copyDiagnosticInformation": "复制诊断信息", "activityMonitor.copyExecutablePath": "复制可执行路径", "activityMonitor.copyUrl": "复制网址", "activityMonitor.forceKillProcess": "强制终止进程", "activityMonitor.inspectActivityMonitor": "检查活动监视器", "activityMonitor.killProcess": "终止进程", "activityMonitor.openDevTools": "打开开发工具", "activityMonitor.reload": "重新加载", "clientPlaceholder.placeholderDescription": "点击以在此窗口中显示", "clientPlaceholder.placeholderTitle": "当前正在另一个窗口中查看内容", "commandSearch.window.title": "Notion - 命令搜索", "crashWatchdog.dialog.abnormalExit": "异常退出：页面出现 non-zero exit code。", "crashWatchdog.dialog.buttonCloseTab": "关闭选项卡", "crashWatchdog.dialog.buttonCloseWindow": "关闭窗口", "crashWatchdog.dialog.buttonRestartApp": "重启 Notion", "crashWatchdog.dialog.crashed": "崩溃：页面因未知原因崩溃。", "crashWatchdog.dialog.details": "页面网址：{url} 原因：{reason} 退出代码：{exitCode}", "crashWatchdog.dialog.integrityFailure": "完整性错误：页面未通过代码完整性检查。", "crashWatchdog.dialog.killed": "已终止：页面被外部进程终止。", "crashWatchdog.dialog.launchFailed": "启动失败：进程未能启动。", "crashWatchdog.dialog.message": "显示此选项卡时出现问题，无法自动恢复。", "crashWatchdog.dialog.oom": "OOM：页面内存不足并崩溃。", "crashWatchdog.dialog.title": "出现问题", "crashWatchdog.dialog.urlUnknown": "未知", "desktop.activityMonitor.all": "全部", "desktop.activityMonitor.cpuPercent": "CPU 使用百分比", "desktop.activityMonitor.cpuPercentDescription": "进程在 CPU 中所占用时间的比例。", "desktop.activityMonitor.cpuTime": "CPU 时间", "desktop.activityMonitor.cpuTimeDescription": "进程启动后所用的 CPU 总时间，以秒为单位。", "desktop.activityMonitor.creationTime": "创建时间", "desktop.activityMonitor.creationTimeDescription": "进程创建以来的时间。", "desktop.activityMonitor.frames": "框架", "desktop.activityMonitor.framesDescription": "进程所要处理的框架数量。多个渲染器进程要处理多个框架。", "desktop.activityMonitor.hidden": "隐藏", "desktop.activityMonitor.hideColumns": "隐藏列", "desktop.activityMonitor.hideFilters": "隐藏筛选条件", "desktop.activityMonitor.idleWakeupsPerSecond": "待机唤醒", "desktop.activityMonitor.idleWakeupsPerSecondDescription": "自活动监视器上次更新以来进程唤醒 CPU 的次数。", "desktop.activityMonitor.loading": "正在载入活动数据", "desktop.activityMonitor.memCurrent": "内存（当前）", "desktop.activityMonitor.memCurrentDescription": "进程的“工作集”（即当前驻留在内存中的页面集）大小。此数字不涉及操作系统的内存压缩、非活动和缓存页面的管理和其他内存管理技术。进程所使用的物理内存可能要小得多。", "desktop.activityMonitor.memPeak": "内存（峰值）", "desktop.activityMonitor.memPeakDescription": "进程的峰值工作集大小，即进程自启动以来使用的最大物理内存量。进程的“工作集”大小，即当前驻留在内存中的页面集。此数字不涉及操作系统的内存压缩、非活动和缓存页面的管理和其他内存管理技术。进程所使用的物理内存可能要小得多。", "desktop.activityMonitor.memPrivate": "内存（私密）", "desktop.activityMonitor.memPrivateDescription": "进程所分配的不与其他进程（如 JS 堆或 HTML 内容）共享的物理内存量。", "desktop.activityMonitor.memShared": "内存（共享）", "desktop.activityMonitor.memSharedDescription": "进程所分配的与其他进程（如共享库或映射文件）共享的物理内存量。", "desktop.activityMonitor.mixed": "混合", "desktop.activityMonitor.parentWindowId": "上级窗口 ID", "desktop.activityMonitor.parentWindowIdDescription": "包含此选项卡的窗口的唯一标识符。", "desktop.activityMonitor.pid": "PID", "desktop.activityMonitor.pidDescription": "操作系统所使用的进程的进程 ID。", "desktop.activityMonitor.processName": "进程名称", "desktop.activityMonitor.showColumns": "显示列", "desktop.activityMonitor.showFilters": "显示筛选条件", "desktop.activityMonitor.tabId": "选项卡 ID", "desktop.activityMonitor.tabIdDescription": "Notion 应用中选项卡的唯一标识符。", "desktop.activityMonitor.type": "类型", "desktop.activityMonitor.url": "URL", "desktop.activityMonitor.urlDescription": "进程的网址。多个渲染器进程要处理多个框架。详情请参阅框架列。", "desktop.activityMonitor.visibilityState": "是否可见", "desktop.activityMonitor.visibilityStateDescription": "进程的可见性状态。如果进程是渲染器进程，此信息为主框架的可见性状态。", "desktop.activityMonitor.visible": "可见", "desktop.tabBar.backButtonLabel": "返回", "desktop.tabBar.closeSidebarLabel": "关闭侧边栏", "desktop.tabBar.closeTabLabel": "关闭选项卡 {tabTitle}", "desktop.tabBar.forwardButtonLabel": "前进", "desktop.tabBar.newTabButtonLabel": "新选项卡", "desktop.tabBar.openSidebarLabel": "打开侧边栏", "desktop.tabBar.tabSpacesLabel": "选项卡空间", "desktopExtensions.install.failed.title": "扩展程序安装失败", "desktopExtensions.manage.cancel": "取消", "desktopExtensions.manage.disable": "禁用", "desktopExtensions.manage.enable": "启用", "desktopExtensions.manage.message": "你想对 {extensionTitle} {extensionVersion} 执行什么操作？", "desktopExtensions.manage.title": "管理扩展程序", "desktopExtensions.manage.uninstall": "卸载", "desktopExtensions.manage.unload": "取消加载", "desktopExtensions.openFailed.noPopupMessage": "此扩展未指定弹出窗口 (action.default_popup)", "desktopExtensions.openFailed.noPopupTitle": "无法打开扩展对话框", "desktopExtensions.unzip.failed.badFileRead": "无法读取 CRX 文件：{error}", "desktopExtensions.unzip.failed.badFileWrite": "无法写入文件 {filePath}：{error}", "desktopExtensions.unzip.failed.badFolderCreate": "无法在 {extensionPath} 中创建扩展程序文件夹：{error}", "desktopExtensions.unzip.failed.badManifest": "无法解析此扩展程序中的 manifest.json。是否为有效的扩展程序？发生错误：{error}", "desktopExtensions.unzip.failed.badManifestNoName": "在 manifest.json 中未找到名称", "desktopExtensions.unzip.failed.error": "无法解压 CRX 文件：{error}", "desktopExtensions.unzip.failed.noManifest": "在 CRX 文件中未找到 manifest.json。是否为有效的扩展程序？", "desktopInstaller.failedToMove.detail": "我们未能将应用移动到“应用程序”文件夹。请手动移动。", "desktopInstaller.failedToMove.title": "移动应用失败", "desktopInstaller.invalidInstallDialog.cancelButton.label": "取消", "desktopInstaller.invalidInstallDialog.confirmMove": "你的 Notion 应用未被正确安装。我们能否将 Notion 应用移到“应用程序”文件夹中？", "desktopInstaller.invalidInstallDialog.okButton.label": "好的", "desktopInstaller.invalidInstallDialog.title": "安装无效", "desktopTopbar.appMenu.about": "关于 Notion", "desktopTopbar.appMenu.checkForUpdate": "检查更新…", "desktopTopbar.appMenu.checkForUpdate.noUpdateAvailable": "你正在使用的是 Notion 的最新版本！", "desktopTopbar.appMenu.checkForUpdate.title": "检查更新", "desktopTopbar.appMenu.checkForUpdate.updateAvailable": "正在后端下载 Notion 的最新版本。谢谢你的及时更新！", "desktopTopbar.appMenu.checkForUpdate.updateCheckFailed": "Notion 无法与更新服务器建立连接，可能是因为你的互联网连接或更新服务器本身存在的问题。请稍后再试。", "desktopTopbar.appMenu.downloadingUpdate": "正在下载更新（{percentage}%）", "desktopTopbar.appMenu.hide": "隐藏 Notion", "desktopTopbar.appMenu.hideOthers": "隐藏其他", "desktopTopbar.appMenu.preferences": "首选项...", "desktopTopbar.appMenu.quit": "退出", "desktopTopbar.appMenu.quitWithoutSavingTabs": "退出且不保存选项卡", "desktopTopbar.appMenu.restartToApplyUpdate": "重新启动以应用更新", "desktopTopbar.appMenu.services": "服务", "desktopTopbar.appMenu.unhide": "显示所有", "desktopTopbar.editMenu.copy": "拷贝", "desktopTopbar.editMenu.copyLinkToCurrentPage": "拷贝当前页面链接", "desktopTopbar.editMenu.copyLinkToCurrentPageBlockTitle": "拷贝当前页面名称", "desktopTopbar.editMenu.cut": "剪切", "desktopTopbar.editMenu.paste": "粘贴", "desktopTopbar.editMenu.pasteAndMatchStyle": "粘贴和匹配样式", "desktopTopbar.editMenu.redo": "重做", "desktopTopbar.editMenu.selectAll": "全选", "desktopTopbar.editMenu.speech": "语音", "desktopTopbar.editMenu.speech.startSpeaking": "开始说话", "desktopTopbar.editMenu.speech.stopSpeaking": "停止说话", "desktopTopbar.editMenu.title": "编辑", "desktopTopbar.editMenu.undo": "撤消", "desktopTopbar.extensionsMenu.install": "安装扩展程序...", "desktopTopbar.extensionsMenu.manage": "管理扩展程序", "desktopTopbar.fileMenu.close": "关闭窗口", "desktopTopbar.fileMenu.closeTab": "关闭选项卡", "desktopTopbar.fileMenu.newNotionWindow": "Notion 新窗口", "desktopTopbar.fileMenu.newTab": "新选项卡", "desktopTopbar.fileMenu.newWindow": "新窗口", "desktopTopbar.fileMenu.print": "正在打印…", "desktopTopbar.fileMenu.quit": "退出", "desktopTopbar.fileMenu.quitWithoutSavingTabs": "退出且不保存选项卡", "desktopTopbar.fileMenu.reopenClosedTab": "重新打开上次关闭的选项卡", "desktopTopbar.fileMenu.title": "文件", "desktopTopbar.helpMenu.copyInstallId": "拷贝安装 ID", "desktopTopbar.helpMenu.disableAdvancedLogging": "禁用高级日志记录并重新启动", "desktopTopbar.helpMenu.disableDebugLogging": "禁用高级日志记录并重新启动", "desktopTopbar.helpMenu.disableHardwareAcceleration": "禁用硬件加速并重新启动", "desktopTopbar.helpMenu.enableAdvancedLogging": "启用高级日志记录并重新启动", "desktopTopbar.helpMenu.enableDebugLogging": "启用高级日志记录并重新启动", "desktopTopbar.helpMenu.enableHardwareAcceleration": "启用硬件加速并重新启动", "desktopTopbar.helpMenu.openActivityMonitor": "打开活动监视器", "desktopTopbar.helpMenu.openConsole": "打开控制台", "desktopTopbar.helpMenu.openHelpAndSupport": "打开帮助和文档", "desktopTopbar.helpMenu.recordingNetLog": "正在录制网络日志…", "desktopTopbar.helpMenu.recordNetLog": "录制网络日志…", "desktopTopbar.helpMenu.recordNetLogConfirmation": "网络日志现在正在录制到你的“下载”文件夹中。要停止录制，请点击“故障排除”菜单中的“停止录制网络日志”按钮或退出应用。", "desktopTopbar.helpMenu.recordNetLogFailed": "录制网络日志失败。请重试或检查日志以了解更多信息。", "desktopTopbar.helpMenu.recordNetLogFailedMessage": "请重试或检查日志以了解更多信息。发生错误：", "desktopTopbar.helpMenu.recordNetLogFailedTitle": "录制网络日志失败", "desktopTopbar.helpMenu.recordNetLogStop": "停止录制网络日志…", "desktopTopbar.helpMenu.recordPerformanceTrace": "记录性能跟踪", "desktopTopbar.helpMenu.recordPerformanceTraceConfirm": "你想记录未来 30 秒的性能跟踪吗？完成后，它将被放置在你的“下载”文件夹中。", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmCancel": "取消", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmOk": "记录性能跟踪", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmTitle": "记录性能跟踪？", "desktopTopbar.helpMenu.resetAndEraseAllLocalData": "重置并删除所有本地数据", "desktopTopbar.helpMenu.showLogsInExplorer": "在资源管理器中显示日志", "desktopTopbar.helpMenu.showLogsInFinder": "在 Finder 中显示日志", "desktopTopbar.helpMenu.title": "帮助", "desktopTopbar.historyMenu.historyBack": "返回", "desktopTopbar.historyMenu.historyForward": "前进", "desktopTopbar.historyMenu.title": "历史", "desktopTopbar.toggleDevTools": "切换开发者工具", "desktopTopbar.toggleWindowDevTools": "切换Windows开发者工具", "desktopTopbar.troubleshootingMenu.title": "故障排除", "desktopTopbar.viewMenu.actualSize": "实际大小", "desktopTopbar.viewMenu.forceReload": "强制重新加载", "desktopTopbar.viewMenu.forceReloadDialog.cancel": "取消", "desktopTopbar.viewMenu.forceReloadDialog.message": "你目前处于离线状态。强制重新加载此页面将导致你无法访问，直到你重新上线。", "desktopTopbar.viewMenu.forceReloadDialog.ok": "仍然重新加载", "desktopTopbar.viewMenu.forceReloadDialog.title": "确定要强制重新加载吗？", "desktopTopbar.viewMenu.reload": "重新加载", "desktopTopbar.viewMenu.showHideSidebar": "显示/隐藏侧边栏", "desktopTopbar.viewMenu.showHideTabSpaceButton": "显示/隐藏选项卡群组", "desktopTopbar.viewMenu.title": "视图", "desktopTopbar.viewMenu.togglefullscreen": "切换全屏", "desktopTopbar.viewMenu.zoomIn": "放大", "desktopTopbar.viewMenu.zoomOut": "缩小", "desktopTopbar.whatsNewMac.title": "打开 Notion 的最新版本（macOS 版）", "desktopTopbar.whatsNewWindows.title": "打开 Notion 的最新版本（Windows 版）", "desktopTopbar.windowMenu.close": "关闭", "desktopTopbar.windowMenu.front": "前面", "desktopTopbar.windowMenu.maximize": "最大化", "desktopTopbar.windowMenu.minimize": "最小化", "desktopTopbar.windowMenu.showNextTab": "显示下一个选项卡", "desktopTopbar.windowMenu.showPreviousTab": "显示上一个选项卡", "desktopTopbar.windowMenu.title": "窗口", "desktopTopbar.windowMenu.zoom": "Zoom", "desktopTroubleshooting.resetData.cancel": "取消", "desktopTroubleshooting.resetData.closingWindows": "关闭 Notion 窗口", "desktopTroubleshooting.resetData.deletingFiles": "正在删除文件", "desktopTroubleshooting.resetData.done": "完成", "desktopTroubleshooting.resetData.doneMessage": "应用已重置。", "desktopTroubleshooting.resetData.failed": "我们的恢复流程未能删除某些文件。 很抱歉给您带来不便，请访问 https://www.notion.so/help 寻求帮助。如需手动强制完全重置应用，请将 Notion 彻底关闭。然后，请删除以下路径：{userDataPath}", "desktopTroubleshooting.resetData.message": "此操作会删除所有本地和内部数据，包括缓存和本地设置，将 Notion 应用还原到新安装的状态。执行后，你将从 Notion 退出登录。你的页面和其他应用内内容将保持不变。是否要继续？", "desktopTroubleshooting.resetData.reset": "重置所有本地数据", "desktopTroubleshooting.resetData.restart": "重启", "desktopTroubleshooting.resetData.title": "正在重置和清除所有本地数据", "desktopTroubleshooting.showLogs.error.message.mac": "Notion 尝试在 Finder 中显示日志时遇到错误：", "desktopTroubleshooting.showLogs.error.message.windows": "Notion 尝试在资源管理器中显示日志时遇到错误：", "desktopTroubleshooting.showLogs.error.title": "显示日志失败", "desktopTroubleshooting.startRecordingNetLog": "开始录制网络日志", "desktopTroubleshooting.stopRecordingNetLog": "停止录制网络日志", "menuBarIcon.menu.changeCommandSearchAndNotionAiShortcut": "编辑快捷键", "menuBarIcon.menu.changeCommandSearchShortcut": "更改命令搜索快捷键", "menuBarIcon.menu.enableQuickSearch": "启用快速搜索", "menuBarIcon.menu.keepInBackground": "保持后端运行", "menuBarIcon.menu.launchPreferences": "启动偏好设置", "menuBarIcon.menu.openOnLogin": "登录电脑时打开 Notion", "menuBarIcon.menu.quitNotion": "退出 Notion", "menuBarIcon.menu.showImmediately": "立即显示", "menuBarIcon.menu.showNotionInMenuBar": "在菜单栏中显示 Notion", "menuBarIcon.menu.toggleCommandSearch": "折叠命令搜索", "menuBarIcon.menu.toggleNotionAi": "折叠 Notion AI", "openAtLogin.dialog.detail": "{operatingSystem}阻止 Notion 配置“登录时打开”设置。这通常是因为 Notion 的启动已在系统设置中配置，或者你的权限不足。不过，你仍然可以在系统设置中手动配置这项设置。", "openAtLogin.dialog.title": "登录时打开", "tabSpaces.deleteDialog.cancelButton": "取消", "tabSpaces.deleteDialog.deleteButton": "删除", "tabSpaces.deleteDialog.detail": "此选项卡组中的所有选项卡都将取消分组。", "tabSpaces.deleteDialog.title": "要删除你的“{title}”选项卡组吗？", "tabSpaces.snackbar.switchedToTabGroup": "已切换到{title}", "tabSpaces.snackbar.switchedToUngroupedTabs": "已切换到未分组的选项卡", "tabSpaces.snackbar.tabGroupPlaceholder": "选项卡组", "updatePrompt.detail": "你想现在安装吗？我们将为你重新打开窗口和选项卡。", "updatePrompt.installAndRelaunch": "安装并重新启动", "updatePrompt.message": "Notion 新版本现已上线！", "updatePrompt.remindMeLater": "稍后提醒我", "window.closeDialog.cancelButton": "取消", "window.closeDialog.confirmButton": "关闭", "window.closeDialog.title.app": "要关闭 Notion 吗?", "window.closeDialog.title.tab": "要关闭 Notion 选项卡吗？", "window.closeDialog.title.window": "要关闭 Notion 窗口吗？", "window.loadingError.message": "加载 Notion 时出了错，请连接到互联网开始使用。", "window.loadingError.reload": "重新加载", "window.movedTabSnackbarMessage": "已将“{tabTitle}”移动至“{tabSpaceTitle}”", "window.tabLoadingError.cancel": "取消", "window.tabMenu.closeOtherTabs": "关闭其他选项卡", "window.tabMenu.closeTab": "关闭选项卡", "window.tabMenu.closeTabsToLeft": "关闭左侧的选项卡", "window.tabMenu.closeTabsToRight": "关闭右侧的选项卡", "window.tabMenu.copyLink": "拷贝链接", "window.tabMenu.duplicateTab": "创建选项卡副本", "window.tabMenu.moveTo": "移动到", "window.tabMenu.moveToNewWindow": "将选项卡移动到新窗口", "window.tabMenu.moveToSubmenuNewWindow": "新窗口", "window.tabMenu.pinTab": "固定选项卡", "window.tabMenu.refresh": "刷新选项卡", "window.tabMenu.reopenClosedTab": "重新打开上次关闭的选项卡", "window.tabMenu.replacePinnedTabUrl": "替换固定 URL 为当前 URL", "window.tabMenu.returnToPinnedTabUrl": "返回固定URL", "window.tabMenu.ungroupTab": "取消选项卡组", "window.tabMenu.unpinTab": "取消固定选项卡", "window.tabTitlePlaceholder": "选项卡", "window.ungroupedTabSnackbarMessage": "已取消“{tabTitle}”分组"}