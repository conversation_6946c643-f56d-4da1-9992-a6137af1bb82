{"activityMonitor.copyDiagnosticInformation": "คัดลอกข้อมูลการวินิจฉัย", "activityMonitor.copyExecutablePath": "คัดลอกเส้นทางที่เปิดได้", "activityMonitor.copyUrl": "คัดลอก URL", "activityMonitor.forceKillProcess": "บังคับยุติกระบวนการ", "activityMonitor.inspectActivityMonitor": "ตรวจสอบการติดตามกิจกรรม", "activityMonitor.killProcess": "ยุติกระบวนการ", "activityMonitor.openDevTools": "เปิด DevTools", "activityMonitor.reload": "โหลดอีกครั้ง", "clientPlaceholder.placeholderDescription": "คลิกเพื่อแสดงในหน้าต่างนี้", "clientPlaceholder.placeholderTitle": "กำลังดูเนื้อหาในหน้าต่างอื่นอยู่", "commandSearch.window.title": "Notion - Command Search", "crashWatchdog.dialog.abnormalExit": "การออกที่ผิดปกติ: มีการออกจากหน้าด้วยรหัสออกที่ไม่ใช่ศูนย์", "crashWatchdog.dialog.buttonCloseTab": "ปิดแท็บ", "crashWatchdog.dialog.buttonCloseWindow": "ปิดหน้าต่าง", "crashWatchdog.dialog.buttonRestartApp": "รีสตาร์ท Notion", "crashWatchdog.dialog.crashed": "หยุดทำงาน: หน้าหยุดทำงานโดยไม่ทราบสาเหตุ", "crashWatchdog.dialog.details": "URL ของหน้า: {url} เหตุผล: {reason} รหัสออก: {exitCode}", "crashWatchdog.dialog.integrityFailure": "ความถูกต้องสมบูรณ์: หน้าไม่ผ่านการตรวจสอบความถูกต้องสมบูรณ์ของรหัส", "crashWatchdog.dialog.killed": "บังคับปิด: หน้านี้ถูกบังคับปิดโดยกระบวนการภายนอก", "crashWatchdog.dialog.launchFailed": "เปิดใช้งานไม่สำเร็จ: เปิดใช้งานกระบวนการไม่สำเร็จ", "crashWatchdog.dialog.message": "มีบางอย่างผิดพลาดขณะแสดงแท็บนี้ และเรากู้คืนโดยอัตโนมัติไม่ได้", "crashWatchdog.dialog.oom": "OOM: หน่วยความจำในหน้าหมดและหยุดทำงาน", "crashWatchdog.dialog.title": "มีบางอย่างผิดพลาด", "crashWatchdog.dialog.urlUnknown": "ไม่ทราบ", "desktop.activityMonitor.all": "ทั้งหมด", "desktop.activityMonitor.cpuPercent": "% CPU", "desktop.activityMonitor.cpuPercentDescription": "สัดส่วนของเวลาประมวลผลของ CPU ที่กระบวนการใช้งาน", "desktop.activityMonitor.cpuTime": "เวลา CPU", "desktop.activityMonitor.cpuTimeDescription": "จำนวนวินาทีทั้งหมดของเวลา CPU ที่ใช้ตั้งแต่เริ่มกระบวนการ", "desktop.activityMonitor.creationTime": "สร้างแล้ว", "desktop.activityMonitor.creationTimeDescription": "ระยะเวลาตั้งแต่สร้างกระบวนการ", "desktop.activityMonitor.frames": "เฟรม", "desktop.activityMonitor.framesDescription": "จำนวนเฟรมที่กระบวนการจัดการอยู่ กระบวนการเรนเดอร์หลายกระบวนการจะรับผิดชอบหลายเฟรม", "desktop.activityMonitor.hidden": "ซ่อนอยู่", "desktop.activityMonitor.hideColumns": "ซ่อนคอลัมน์", "desktop.activityMonitor.hideFilters": "ซ่อนตัวกรอง", "desktop.activityMonitor.idleWakeupsPerSecond": "การปลุกเครื่องในขณะว่าง", "desktop.activityMonitor.idleWakeupsPerSecondDescription": "จำนวนครั้งที่กระบวนการปลุกการทำงานของ CPU นับตั้งแต่การอัพเดทตัวตรวจสอบกิจกรรมครั้งล่าสุด", "desktop.activityMonitor.loading": "กำลังโหลดข้อมูลกิจกรรม", "desktop.activityMonitor.memCurrent": "หน่วยความจำ (ปัจจุบัน)", "desktop.activityMonitor.memCurrentDescription": "ขนาด “ชุดการทำงาน” ของกระบวนการ ซึ่งหมายถึงชุดของหน้าที่ยังคงอยู่ในหน่วยความจำ (RAM) ในปัจจุบัน ตัวเลขนี้ไม่นับการบีบอัดหน่วยความจำโดยระบบปฏิบัติการ การจัดการหน้าที่ไม่ใช้งานและหน้าที่แคชไว้ หรือเทคนิคการจัดการหน่วยความจำอื่นๆ ปริมาณหน่วยความจำจริงที่กระบวนการใช้งานจึงมีแนวโน้มว่าจะน้อยกว่านี้มาก", "desktop.activityMonitor.memPeak": "หน่วยความจำ (สูงสุด)", "desktop.activityMonitor.memPeakDescription": "ขนาด \"ชุดการทำงาน\" สูงสุดของกระบวนการ ซึ่งหมายถึงปริมาณหน่วยความจำจริงสูงสุดที่กระบวนการเคยใช้ตั้งแต่เริ่มทำงาน ขนาด “ชุดการทำงาน” ของกระบวนการ ซึ่งหมายถึงชุดของหน้าที่ยังคงอยู่ในหน่วยความจำ (RAM) ในปัจจุบัน ตัวเลขนี้ไม่นับการบีบอัดหน่วยความจำโดยระบบปฏิบัติการ การจัดการหน้าที่ไม่ใช้งานและหน้าที่แคชไว้ หรือเทคนิคการจัดการหน่วยความจำอื่นๆ ปริมาณหน่วยความจำจริงที่กระบวนการใช้งานจึงมีแนวโน้มว่าจะน้อยกว่านี้มาก", "desktop.activityMonitor.memPrivate": "หน่วยความจำ (ส่วนตัว)", "desktop.activityMonitor.memPrivateDescription": "ปริมาณหน่วยความจำจริงที่จัดสรรโดยกระบวนการที่ไม่ได้ใช้ร่วมกับกระบวนการอื่นเช่น JS heap หรือเนื้อหา HTML", "desktop.activityMonitor.memShared": "หน่วยความจำ (ใช้ร่วมกัน)", "desktop.activityMonitor.memSharedDescription": "ปริมาณหน่วยความจำจริงที่จัดสรรโดยกระบวนการที่ใช้ร่วมกับกระบวนการอื่น เช่น ไลบรารีที่ใช้ร่วมกันหรือไฟล์ที่แมป", "desktop.activityMonitor.mixed": "ผสม", "desktop.activityMonitor.parentWindowId": "รหัสหน้าต่างหลัก", "desktop.activityMonitor.parentWindowIdDescription": "ตัวระบุที่ไม่ซ้ำกันของหน้าต่างที่มีแท็บนี้", "desktop.activityMonitor.pid": "PID", "desktop.activityMonitor.pidDescription": "รหัสกระบวนการของกระบวนการตามที่ระบบปฏิบัติการใช้", "desktop.activityMonitor.processName": "ชื่อกระบวนการ", "desktop.activityMonitor.showColumns": "แสดงคอลัมน์", "desktop.activityMonitor.showFilters": "แสดงตัวกรอง", "desktop.activityMonitor.tabId": "รหัสแท็บ", "desktop.activityMonitor.tabIdDescription": "ตัวระบุที่ไม่ซ้ำกันของแท็บในแอพ Notion", "desktop.activityMonitor.type": "ประเภท", "desktop.activityMonitor.url": "URL", "desktop.activityMonitor.urlDescription": "URL ของกระบวนการ กระบวนการเรนเดอร์หลายกระบวนการจะรับผิดชอบหลายเฟรม ดูข้อมูลเพิ่มเติมได้ที่คอลัมน์เฟรม", "desktop.activityMonitor.visibilityState": "การมองเห็น", "desktop.activityMonitor.visibilityStateDescription": "สถานะการมองเห็นของกระบวนการ หากกระบวนการนั้นเป็นกระบวนการเรนเดอร์ สถานะการมองเห็นนี้จะอิงตามเฟรมหลักของกระบวนการนั้น", "desktop.activityMonitor.visible": "มองเห็นได้", "desktop.tabBar.backButtonLabel": "ย้อนกลับ", "desktop.tabBar.closeSidebarLabel": "ปิดแถบด้านข้าง", "desktop.tabBar.closeTabLabel": "ปิดแท็บ {tabTitle}", "desktop.tabBar.forwardButtonLabel": "ไปข้างหน้า", "desktop.tabBar.newTabButtonLabel": "แท็บใหม่", "desktop.tabBar.openSidebarLabel": "เปิดแถบด้านข้าง", "desktop.tabBar.tabSpacesLabel": "พื้นที่แท็บ", "desktopExtensions.install.failed.title": "ติดตั้งส่วนขยายไม่สำเร็จ", "desktopExtensions.manage.cancel": "ยกเลิก", "desktopExtensions.manage.disable": "ปิดใช้งาน", "desktopExtensions.manage.enable": "เปิดใช้งาน", "desktopExtensions.manage.message": "คุณต้องการทำอะไรกับ {extensionTitle} {extensionVersion}?", "desktopExtensions.manage.title": "จัดการส่วนขยาย", "desktopExtensions.manage.uninstall": "ถอนการติดตั้ง", "desktopExtensions.openFailed.noPopupMessage": "ส่วนขยายนี้ไม่ได้ระบุป๊อปอัป (action.default_popup)", "desktopExtensions.openFailed.noPopupTitle": "เปิดส่วนขยายไม่สำเร็จ", "desktopExtensions.unzip.failed.badFileRead": "อ่านไฟล์ CRX ไม่สำเร็จ: {error}", "desktopExtensions.unzip.failed.badFileWrite": "เขียนไฟล์ {filePath} ไม่สำเร็จ: {error}", "desktopExtensions.unzip.failed.badFolderCreate": "สร้างโฟลเดอร์ส่วนขยายที่ {extensionPath} ไม่สำเร็จ: {error}", "desktopExtensions.unzip.failed.badManifest": "แยกวิเคราะห์ไฟล์ manifest.json ในส่วนขยายนี้ไม่ได้ ส่วนขยายนี้ถูกต้องหรือไม่? ข้อผิดพลาดที่พบคือ {error}", "desktopExtensions.unzip.failed.badManifestNoName": "ไม่พบชื่อใน manifest.json", "desktopExtensions.unzip.failed.error": "แตกไฟล์ CRX ไม่สำเร็จ: {error}", "desktopExtensions.unzip.failed.noManifest": "ไม่พบไฟล์ manifest.json ใน CRX ส่วนขยายนี้ถูกต้องหรือไม่?", "desktopInstaller.failedToMove.detail": "ระบบย้ายแอพไปยังโฟลเดอร์แอปพลิเคชันของคุณไม่สำเร็จ โปรดย้ายด้วยตนเอง", "desktopInstaller.failedToMove.title": "ย้ายแอพไม่สำเร็จ", "desktopInstaller.invalidInstallDialog.cancelButton.label": "ยกเลิก", "desktopInstaller.invalidInstallDialog.confirmMove": "แอปพลิเคชัน Notion ของคุณไม่ได้ติดตั้งอย่างถูกต้อง ให้ระบบย้ายแอพ Notion ไปยังโฟลเดอร์แอพพลิเคชันได้หรือไม่?", "desktopInstaller.invalidInstallDialog.okButton.label": "ตกลง", "desktopInstaller.invalidInstallDialog.title": "ติดตั้งไม่ถูกต้อง", "desktopTopbar.appMenu.about": "เกี่ยวกับ Notion", "desktopTopbar.appMenu.checkForUpdate": "ตรวจสอบการอัพเดท...", "desktopTopbar.appMenu.checkForUpdate.noUpdateAvailable": "คุณใช้ Notion เวอร์ชันล่าสุด!", "desktopTopbar.appMenu.checkForUpdate.title": "ตรวจสอบการอัพเดท", "desktopTopbar.appMenu.checkForUpdate.updateAvailable": "Notion เวอร์ชันใหม่พร้อมใช้งานและกำลังดาวน์โหลดอยู่ในพื้นหลัง ขอขอบคุณที่ใช้เวอร์ชันล่าสุด!", "desktopTopbar.appMenu.checkForUpdate.updateCheckFailed": "Notion เชื่อมต่อกับเซิร์ฟเวอร์อัพเดทไม่สำเร็จ อาจเกิดจากปัญหาการเชื่อมต่ออินเทอร์เน็ตหรือเซิร์ฟเวอร์อัพเดทเอง โปรดลองอีกครั้งในภายหลัง", "desktopTopbar.appMenu.downloadingUpdate": "กำลังดาวน์โหลดอัพเดท ({percentage}%)", "desktopTopbar.appMenu.hide": "ซ่อน Notion", "desktopTopbar.appMenu.hideOthers": "ซ่อนอื่นๆ", "desktopTopbar.appMenu.preferences": "ความชอบ...", "desktopTopbar.appMenu.quit": "ออก", "desktopTopbar.appMenu.quitWithoutSavingTabs": "ออกโดยไม่บันทึกแท็บ", "desktopTopbar.appMenu.restartToApplyUpdate": "รีสตาร์ทเพื่อปรับใช้การอัพเดท", "desktopTopbar.appMenu.services": "บริการ", "desktopTopbar.appMenu.unhide": "แสดงทั้งหมด", "desktopTopbar.editMenu.copy": "คัดลอก", "desktopTopbar.editMenu.copyLinkToCurrentPage": "คัดลอกลิงค์ไปยังหน้าปัจจุบัน", "desktopTopbar.editMenu.copyLinkToCurrentPageBlockTitle": "คัดลอกชื่อของหน้าปัจจุบัน", "desktopTopbar.editMenu.cut": "ตัด", "desktopTopbar.editMenu.paste": "วาง", "desktopTopbar.editMenu.pasteAndMatchStyle": "วางและจับคู่สไตล์", "desktopTopbar.editMenu.redo": "ทำใหม่", "desktopTopbar.editMenu.selectAll": "เลือกทั้งหมด", "desktopTopbar.editMenu.speech": "การพูด", "desktopTopbar.editMenu.speech.startSpeaking": "เริ่มพูด", "desktopTopbar.editMenu.speech.stopSpeaking": "หยุดพูด", "desktopTopbar.editMenu.title": "แก้ไข", "desktopTopbar.editMenu.undo": "เลิกทำ", "desktopTopbar.extensionsMenu.install": "ติดตั้งส่วนขยาย...", "desktopTopbar.extensionsMenu.manage": "จัดการส่วนขยาย", "desktopTopbar.fileMenu.close": "ปิดหน้าต่าง", "desktopTopbar.fileMenu.closeTab": "ปิดแท็บ", "desktopTopbar.fileMenu.newNotionWindow": "หน้าต่าง Notion ใหม่", "desktopTopbar.fileMenu.newTab": "แท็บใหม่", "desktopTopbar.fileMenu.newWindow": "หน้าต่างใหม่", "desktopTopbar.fileMenu.print": "พิมพ์…", "desktopTopbar.fileMenu.quit": "ออก", "desktopTopbar.fileMenu.quitWithoutSavingTabs": "ออกโดยไม่บันทึกแท็บ", "desktopTopbar.fileMenu.reopenClosedTab": "เปิดแท็บที่ปิดล่าสุดอีกครั้ง", "desktopTopbar.fileMenu.title": "ไฟล์", "desktopTopbar.helpMenu.copyInstallId": "คัดลอกรหัสการติดตั้ง", "desktopTopbar.helpMenu.disableAdvancedLogging": "ปิดการบันทึกขั้นสูงและรีสตาร์ท", "desktopTopbar.helpMenu.disableDebugLogging": "ปิดใช้งานการบันทึกขั้นสูงและรีสตาร์ท", "desktopTopbar.helpMenu.disableHardwareAcceleration": "ปิดใช้งานการเร่งความเร็วฮาร์ดแวร์และรีสตาร์ท", "desktopTopbar.helpMenu.enableAdvancedLogging": "เปิดการบันทึกขั้นสูงและรีสตาร์ท", "desktopTopbar.helpMenu.enableDebugLogging": "เปิดใช้งานการบันทึกขั้นสูงและรีสตาร์ท", "desktopTopbar.helpMenu.enableHardwareAcceleration": "เปิดใช้งานการเร่งความเร็วฮาร์ดแวร์และรีสตาร์ท", "desktopTopbar.helpMenu.openActivityMonitor": "เปิดตัวติดตามกิจกรรม", "desktopTopbar.helpMenu.openConsole": "เปิดคอนโซล", "desktopTopbar.helpMenu.openHelpAndSupport": "เปิดความช่วยเหลือและเอกสาร", "desktopTopbar.helpMenu.recordingNetLog": "กำลังบันทึกบันทึกเครือข่าย…", "desktopTopbar.helpMenu.recordNetLog": "บันทึกบันทึกเครือข่าย…", "desktopTopbar.helpMenu.recordNetLogConfirmation": "ขณะนี้ระบบกำลังบันทึกข้อมูลเครือข่ายไปยังโฟลเดอร์ดาวน์โหลด หากต้องการหยุดการบันทึก ให้คลิกปุ่ม ‘หยุดการบันทึกข้อมูลเครือข่าย’ ในเมนูการแก้ไขปัญหาหรือออกจากแอพ", "desktopTopbar.helpMenu.recordNetLogFailed": "การบันทึกบันทึกเครือข่ายล้มเหลว โปรดลองอีกครั้งหรือตรวจสอบบันทึกเพื่อดูข้อมูลเพิ่มเติม", "desktopTopbar.helpMenu.recordNetLogFailedMessage": "โปรดลองอีกครั้งหรือตรวจสอบบันทึกเพื่อดูข้อมูลเพิ่มเติม ข้อผิดพลาดคือ:", "desktopTopbar.helpMenu.recordNetLogFailedTitle": "การบันทึกบันทึกเครือข่ายไม่สำเร็จ", "desktopTopbar.helpMenu.recordNetLogStop": "หยุดการบันทึกบันทึกเครือข่าย…", "desktopTopbar.helpMenu.recordPerformanceTrace": "บันทึกการติดตามประสิทธิภาพ...", "desktopTopbar.helpMenu.recordPerformanceTraceConfirm": "ต้องการบันทึกการติดตามประสิทธิภาพเป็นเวลา 30 วินาทีจากนี้หรือไม่? เมื่อเสร็จแล้ว ระบบจะจัดเก็บการบันทึกไว้ในโฟลเดอร์ Downloads", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmCancel": "ยกเลิก", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmOk": "บันทึกการติดตามประสิทธิภาพ", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmTitle": "ต้องการบันทึกการติดตามประสิทธิภาพหรือไม่?", "desktopTopbar.helpMenu.resetAndEraseAllLocalData": "รีเซ็ตและลบข้อมูลในเครื่องทั้งหมด", "desktopTopbar.helpMenu.showLogsInExplorer": "แสดงบันทึกใน Explorer", "desktopTopbar.helpMenu.showLogsInFinder": "แสดงบันทึกใน Finder", "desktopTopbar.helpMenu.title": "ช่วยเหลือ", "desktopTopbar.historyMenu.historyBack": "ย้อนกลับ", "desktopTopbar.historyMenu.historyForward": "ไปข้างหน้า", "desktopTopbar.historyMenu.title": "ประวัติ", "desktopTopbar.toggleDevTools": "เปิด/ปิดเครื่องมือสำหรับนักพัฒนา", "desktopTopbar.toggleWindowDevTools": "เปิด/ปิดเครื่องมือสำหรับนักพัฒนาของหน้าต่าง", "desktopTopbar.troubleshootingMenu.title": "การแก้ไขปัญหา", "desktopTopbar.viewMenu.actualSize": "ขนาดจริง", "desktopTopbar.viewMenu.forceReload": "บังคับโหลดอีกครั้ง", "desktopTopbar.viewMenu.forceReloadDialog.cancel": "ยกเลิก", "desktopTopbar.viewMenu.forceReloadDialog.message": "คุณออฟไลน์อยู่ในขณะนี้ การบังคับโหลดหน้านี้อีกครั้งจะทำให้คุณจะเสียสิทธิเข้าถึงหน้านี้จนกว่าจะกลับมาออนไลน์", "desktopTopbar.viewMenu.forceReloadDialog.ok": "โหลดใหม่เลย", "desktopTopbar.viewMenu.forceReloadDialog.title": "แน่ใจไหมว่าต้องการบังคับโหลดอีกครั้ง?", "desktopTopbar.viewMenu.reload": "โหลดใหม่", "desktopTopbar.viewMenu.showHideSidebar": "แสดง/ซ่อนแถบด้านข้าง", "desktopTopbar.viewMenu.showHideTabSpaceButton": "แสดง/ซ่อนกลุ่มแท็บ", "desktopTopbar.viewMenu.title": "ดู", "desktopTopbar.viewMenu.togglefullscreen": "เปิด/ปิดเต็มหน้าจอ", "desktopTopbar.viewMenu.zoomIn": "ซูมเข้า", "desktopTopbar.viewMenu.zoomOut": "ซูมออก", "desktopTopbar.whatsNewMac.title": "เปิด มีอะไรใหม่ ใน Notion สำหรับ macOS", "desktopTopbar.whatsNewWindows.title": "เปิด มีอะไรใหม่ ใน Notion สำหรับ Windows", "desktopTopbar.windowMenu.close": "ปิด", "desktopTopbar.windowMenu.front": "ด้านหน้า", "desktopTopbar.windowMenu.maximize": "ขยายใหญ่", "desktopTopbar.windowMenu.minimize": "ย่อเล็ก", "desktopTopbar.windowMenu.showNextTab": "แสดงแท็บถัดไป", "desktopTopbar.windowMenu.showPreviousTab": "แสดงแท็บก่อนหน้า", "desktopTopbar.windowMenu.title": "หน้าต่าง", "desktopTopbar.windowMenu.zoom": "Zoom", "desktopTroubleshooting.resetData.cancel": "ยกเลิก", "desktopTroubleshooting.resetData.closingWindows": "กำลังปิดหน้าต่าง Notion", "desktopTroubleshooting.resetData.deletingFiles": "กำลังลบไฟล์", "desktopTroubleshooting.resetData.done": "เรียบร้อย", "desktopTroubleshooting.resetData.doneMessage": "รีเซ็ตแอพแล้ว", "desktopTroubleshooting.resetData.failed": "กระบวนการกู้คืนลบไฟล์บางไฟล์ไม่สำเร็จ ขออภัยในความไม่สะดวก - โปรดไปที่ https://www.notion.so/help เพื่อขอความช่วยเหลือ หากต้องการรีเซ็ตแอพทั้งหมดโดยบังคับด้วยตนเอง โปรดปิดแอพ Notion ทั้งหมด จากนั้นลบเส้นทางต่อไปนี้: {userDataPath}", "desktopTroubleshooting.resetData.message": "การดำเนินการนี้จะลบข้อมูลในเครื่องและภายในทั้งหมดรวมถึงแคชและการตั้งค่าในเครื่องเพื่อกู้คืนแอพ Notion ให้อยู่ในสถานะติดตั้งใหม่ นอกจากนี้ยังจะนำคุณออกจากระบบ Notion หน้าเว็บและเนื้อหาอื่นๆ ในแอพของคุณจะไม่ถูกแตะต้อง ต้องการดำเนินการต่อไหม?", "desktopTroubleshooting.resetData.reset": "รีเซ็ตข้อมูลในเครื่องทั้งหมด", "desktopTroubleshooting.resetData.restart": "รีสตาร์ท", "desktopTroubleshooting.resetData.title": "กำลังรีเซ็ตและลบข้อมูลในเครื่องทั้งหมด", "desktopTroubleshooting.showLogs.error.message.mac": "Notion พบข้อผิดพลาดขณะพยายามแสดงบันทึกใน Finder:", "desktopTroubleshooting.showLogs.error.message.windows": "Notion พบข้อผิดพลาดขณะพยายามแสดงบันทึกใน Explorer:", "desktopTroubleshooting.showLogs.error.title": "แสดงบันทึกไม่สำเร็จ", "desktopTroubleshooting.startRecordingNetLog": "เริ่มการบันทึกบันทึกเครือข่าย", "desktopTroubleshooting.stopRecordingNetLog": "หยุดการบันทึกบันทึกเครือข่าย", "menuBarIcon.menu.changeCommandSearchAndNotionAiShortcut": "แก้ไขทางลัด", "menuBarIcon.menu.changeCommandSearchShortcut": "เปลี่ยนทางลัดการค้นหาคำสั่ง", "menuBarIcon.menu.enableQuickSearch": "เปิดใช้งานการค้นหาด่วน", "menuBarIcon.menu.keepInBackground": "เก็บไว้ในพื้นหลัง", "menuBarIcon.menu.launchPreferences": "การตั้งค่าการเปิดใช้", "menuBarIcon.menu.openOnLogin": "เปิด Notion เมื่อเข้าสู่ระบบ", "menuBarIcon.menu.quitNotion": "ออกจาก Notion", "menuBarIcon.menu.showImmediately": "แสดงทันที", "menuBarIcon.menu.showNotionInMenuBar": "แสดง Notion ในแถบเมนู", "menuBarIcon.menu.toggleCommandSearch": "เปิด/ปิดการค้นหาคำสั่ง", "menuBarIcon.menu.toggleNotionAi": "เปิด/ปิด Notion AI", "openAtLogin.dialog.detail": "{operatingSystem} ป้องกันไม่ให้ Notion กำหนดการตั้งค่า ‘เปิดเมื่อเข้าสู่ระบบ’ ซึ่งมักจะเกิดขึ้นเมื่อมีการกำหนดค่าการเริ่มต้นใช้งาน Notion ในการตั้งค่าระบบ หรือหากคุณมีสิทธิไม่เพียงพอ คุณยังกำหนดการตั้งค่านี้ด้วยตนเองได้ในการตั้งค่าระบบ", "openAtLogin.dialog.title": "เปิดเมื่อเข้าสู่ระบบ", "tabSpaces.deleteDialog.cancelButton": "ยกเลิก", "tabSpaces.deleteDialog.deleteButton": "ลบ", "tabSpaces.deleteDialog.detail": "แท็บทั้งหมดในกลุ่มแท็บนี้จะถูกยกเลิกการจัดกลุ่ม", "tabSpaces.deleteDialog.title": "ต้องการลบกลุ่มแท็บ ‘{title}’ ใช่ไหม?", "tabSpaces.snackbar.switchedToTabGroup": "เปลี่ยนเป็น {title}", "tabSpaces.snackbar.switchedToUngroupedTabs": "เปลี่ยนเป็นแท็บที่ไม่ได้จัดกลุ่ม", "tabSpaces.snackbar.tabGroupPlaceholder": "กลุ่มแท็บ", "updatePrompt.detail": "คุณต้องการติดตั้งตอนนี้หรือไม่? เราจะเปิดหน้าต่างและแท็บให้คุณอีกครั้ง", "updatePrompt.installAndRelaunch": "ติดตั้งและเปิดใหม่", "updatePrompt.message": "Notion เวอร์ชันใหม่พร้อมใช้งานแล้ว!", "updatePrompt.remindMeLater": "เตือนฉันภายหลัง", "window.closeDialog.cancelButton": "ยกเลิก", "window.closeDialog.confirmButton": "ปิด", "window.closeDialog.title.app": "ปิด Notion ใช่ไหม?", "window.closeDialog.title.tab": "ปิดแท็บ Notion ใช่ไหม?", "window.closeDialog.title.window": "ปิดหน้าต่าง Notion ใช่ไหม?", "window.loadingError.message": "เกิดข้อผิดพลาดในการโหลด Notion เชื่อมต่ออินเทอร์เน็ตเพื่อเริ่มต้น", "window.loadingError.reload": "โหลดใหม่", "window.movedTabSnackbarMessage": "ย้าย {tabTitle} ไปยัง {tabSpaceTitle} แล้ว", "window.tabLoadingError.cancel": "ยกเลิก", "window.tabMenu.closeOtherTabs": "ปิดแท็บอื่น", "window.tabMenu.closeTab": "ปิดแท็บ", "window.tabMenu.closeTabsToLeft": "ปิดแท็บทางซ้าย", "window.tabMenu.closeTabsToRight": "ปิดแท็บทางขวา", "window.tabMenu.copyLink": "คัดลอกลิงค์", "window.tabMenu.duplicateTab": "ทำสำเนาแท็บ", "window.tabMenu.moveTo": "ย้ายไปยัง", "window.tabMenu.moveToNewWindow": "ย้ายแท็บไปยังหน้าต่างใหม่", "window.tabMenu.moveToSubmenuNewWindow": "หน้าต่างใหม่", "window.tabMenu.pinTab": "ปักหมุดแท็บ", "window.tabMenu.refresh": "รีเฟรชแท็บ", "window.tabMenu.reopenClosedTab": "เปิดแท็บที่ปิดล่าสุดอีกครั้ง", "window.tabMenu.replacePinnedTabUrl": "แทนที่ URL ที่ปักหมุดด้วย URL ปัจจุบัน", "window.tabMenu.returnToPinnedTabUrl": "กลับไปที่ URL ที่ปักหมุดไว้", "window.tabMenu.ungroupTab": "ยกเลิกการจัดกลุ่มแท็บ", "window.tabMenu.unpinTab": "เลิกปักหมุดแท็บ", "window.tabTitlePlaceholder": "แท็บ", "window.ungroupedTabSnackbarMessage": "ยกเลิกการจัดกลุ่ม {tabTitle} แล้ว"}