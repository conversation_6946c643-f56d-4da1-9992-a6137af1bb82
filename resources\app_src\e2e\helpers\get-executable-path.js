"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getExecutablePath = getExecutablePath;
const path_1 = __importDefault(require("path"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const root_dir_1 = require("./root-dir");
function getExecutablePath() {
    const possiblePaths = [
        path_1.default.join(root_dir_1.ROOT_DIR, "build/desktop/desktop/out"),
        path_1.default.join(__dirname, "../../out"),
    ];
    const executablePath = possiblePaths.map(findAppInDir).find(Boolean);
    if (!executablePath) {
        throw new Error(`Failed to find app. Looked in:\n${possiblePaths.join("\n")}`);
    }
    return executablePath;
}
function findAppInDir(outDir) {
    if (!fs_extra_1.default.existsSync(outDir)) {
        return null;
    }
    const candidateDirs = fs_extra_1.default.readdirSync(outDir).filter(entry => {
        const stat = fs_extra_1.default.statSync(path_1.default.join(outDir, entry));
        const isDirectory = stat.isDirectory();
        const ignoredNames = ["make"];
        return isDirectory && !ignoredNames.includes(entry);
    });
    if (candidateDirs.length === 0) {
        return null;
    }
    else if (candidateDirs.length > 1) {
        throw new Error(`Multiple apps found in out dir (${outDir}): ${candidateDirs.join(", ")}`);
    }
    const appName = getAppName(candidateDirs[0]);
    const appDir = path_1.default.join(outDir, candidateDirs[0]);
    if (!appName) {
        return null;
    }
    return getBinaryPath(appDir, appName);
}
function getAppName(appPath) {
    return appPath.split("-").at(0);
}
function getBinaryPath(appPath, appName) {
    let result;
    if (process.platform === "darwin") {
        result = path_1.default.join(appPath, `${appName}.app`, "Contents", "MacOS", appName);
    }
    else {
        throw new Error("Unsupported platform");
    }
    if (!fs_extra_1.default.existsSync(result)) {
        throw new Error(`Possible app found at ${appPath} but no binary found at ${result}`);
    }
    return result;
}
