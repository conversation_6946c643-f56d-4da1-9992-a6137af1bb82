{"activityMonitor.copyDiagnosticInformation": "진단 정보 복사", "activityMonitor.copyExecutablePath": "실행 파일 경로 복사", "activityMonitor.copyUrl": "URL 복사", "activityMonitor.forceKillProcess": "프로세스 강제 종료", "activityMonitor.inspectActivityMonitor": "활동 모니터 검사", "activityMonitor.killProcess": "프로세스 종료", "activityMonitor.openDevTools": "개발자 도구 열기", "activityMonitor.reload": "다시 로드", "clientPlaceholder.placeholderDescription": "이 창에서 표시하려면 클릭하세요.", "clientPlaceholder.placeholderTitle": "현재 다른 창에서 콘텐츠를 보는 중", "commandSearch.window.title": "Notion - 명령어 검색", "crashWatchdog.dialog.abnormalExit": "비정상 종료: 페이지가 0이 아닌 종료 코드로 종료되었습니다.", "crashWatchdog.dialog.buttonCloseTab": "탭 닫기", "crashWatchdog.dialog.buttonCloseWindow": "창 닫기", "crashWatchdog.dialog.buttonRestartApp": "Notion 다시 시작", "crashWatchdog.dialog.crashed": "충돌: 알 수 없는 이유로 페이지가 충돌했습니다.", "crashWatchdog.dialog.details": "페이지 URL: {url} 이유: {reason} 종료 코드: {exitCode}", "crashWatchdog.dialog.integrityFailure": "무결성 오류: 페이지가 코드 무결성 검사에 실패했습니다.", "crashWatchdog.dialog.killed": "강제 종료: 페이지가 외부 프로세스에 의해 강제 종료되었습니다.", "crashWatchdog.dialog.launchFailed": "실행 실패: 프로세스를 실행하지 못했습니다.", "crashWatchdog.dialog.message": "이 탭을 표시하는 동안 문제가 발생했으며 자동으로 복구할 수 없습니다.", "crashWatchdog.dialog.oom": "OOM: 메모리 부족으로 페이지가 충돌했습니다.", "crashWatchdog.dialog.title": "문제가 발생했습니다", "crashWatchdog.dialog.urlUnknown": "알 수 없음", "desktop.activityMonitor.all": "전체", "desktop.activityMonitor.cpuPercent": "% CPU", "desktop.activityMonitor.cpuPercentDescription": "프로세스에서 사용할 수 있는 CPU 시간의 일부입니다.", "desktop.activityMonitor.cpuTime": "CPU 시간", "desktop.activityMonitor.cpuTimeDescription": "프로세스 시작 이후 사용된 CPU 시간의 총 초 수입니다.", "desktop.activityMonitor.creationTime": "생성일", "desktop.activityMonitor.creationTimeDescription": "프로세스가 생성된 이후 경과된 시간입니다.", "desktop.activityMonitor.frames": "프레임", "desktop.activityMonitor.framesDescription": "프로세스에서 관리하는 프레임 수입니다. 많은 렌더러 프로세스가 여러 프레임을 처리합니다.", "desktop.activityMonitor.hidden": "숨김", "desktop.activityMonitor.hideColumns": "열 숨기기", "desktop.activityMonitor.hideFilters": "필터 숨기기", "desktop.activityMonitor.idleWakeupsPerSecond": "유휴 상태에서의 웨이크업 횟수", "desktop.activityMonitor.idleWakeupsPerSecondDescription": "프로세스가 마지막 활동 모니터 업데이트 이후 CPU를 깨운 횟수입니다.", "desktop.activityMonitor.loading": "활동 데이터 로딩 중", "desktop.activityMonitor.memCurrent": "메모리(현재)", "desktop.activityMonitor.memCurrentDescription": "프로세스의 '작업 집합' 크기로, 현재 RAM에 상주하고 있는 페이지들의 집합입니다. 이 숫자는 운영 체제의 메모리 압축, 비활성화된 페이지 및 캐시 처리된 페이지 관리, 또는 기타 메모리 관리 기술을 고려하지 않습니다. 프로세스에서 사용하는 물리적 메모리의 크기는 이보다 훨씬 작을 가능성이 높습니다.", "desktop.activityMonitor.memPeak": "메모리(최대)", "desktop.activityMonitor.memPeakDescription": "프로세스의 최대 작업 집합 크기로, 프로세스가 시작된 이후 프로세스에서 사용된 물리적 메모리의 최대 양입니다. 프로세스의 '작업 집합' 크기로, 현재 RAM에 상주하고 있는 페이지들의 집합입니다. 이 숫자는 운영 체제의 메모리 압축, 비활성화된 페이지 및 캐시 처리된 페이지 관리, 또는 기타 메모리 관리 기술을 고려하지 않습니다. 프로세스에서 사용하는 물리적 메모리의 크기는 이보다 훨씬 작을 가능성이 높습니다.", "desktop.activityMonitor.memPrivate": "메모리(개인)", "desktop.activityMonitor.memPrivateDescription": "프로세스에서 할당한 물리적 메모리 중 JavaScript 힙이나 HTML 콘텐츠처럼 다른 프로세스와 공유되지 않는 메모리의 양입니다.", "desktop.activityMonitor.memShared": "메모리(공유)", "desktop.activityMonitor.memSharedDescription": "프로세스에서 할당한 물리적 메모리 중 공유 라이브러리나 매핑된 파일처럼 다른 프로세스와 공유되는 메모리의 양입니다.", "desktop.activityMonitor.mixed": "혼합", "desktop.activityMonitor.parentWindowId": "상위 창 ID", "desktop.activityMonitor.parentWindowIdDescription": "이 탭이 포함된 창의 고유 식별자입니다.", "desktop.activityMonitor.pid": "PID", "desktop.activityMonitor.pidDescription": "운영 체제에서 사용하는 프로세스의 프로세스 ID입니다.", "desktop.activityMonitor.processName": "프로세스 이름", "desktop.activityMonitor.showColumns": "열 표시하기", "desktop.activityMonitor.showFilters": "필터 표시하기", "desktop.activityMonitor.tabId": "탭 ID", "desktop.activityMonitor.tabIdDescription": "Notion 앱 탭의 고유 식별자입니다.", "desktop.activityMonitor.type": "유형", "desktop.activityMonitor.url": "URL", "desktop.activityMonitor.urlDescription": "프로세스의 URL입니다. 많은 렌더러 프로세스가 여러 프레임을 처리합니다. 자세한 내용은 \"프레임\" 열을 참고하세요.", "desktop.activityMonitor.visibilityState": "표시 여부", "desktop.activityMonitor.visibilityStateDescription": "프로세스의 가시성 상태입니다. 프로세스가 렌더러 프로세스인 경우, 이는 메인 프레임의 가시성 상태를 나타냅니다.", "desktop.activityMonitor.visible": "표시", "desktop.tabBar.backButtonLabel": "뒤로가기", "desktop.tabBar.closeSidebarLabel": "사이드바 닫기", "desktop.tabBar.closeTabLabel": "{tabTitle} 탭 닫기", "desktop.tabBar.forwardButtonLabel": "앞으로 가기", "desktop.tabBar.newTabButtonLabel": "새 탭", "desktop.tabBar.openSidebarLabel": "사이드바 열기", "desktop.tabBar.tabSpacesLabel": "탭 스페이스", "desktopExtensions.install.failed.title": "확장 프로그램을 설치하지 못했습니다.", "desktopExtensions.manage.cancel": "취소", "desktopExtensions.manage.disable": "비활성화", "desktopExtensions.manage.enable": "활성화", "desktopExtensions.manage.message": "{extensionTitle} {extensionVersion} 버전으로 어떤 작업을 하시겠습니까?", "desktopExtensions.manage.title": "확장 프로그램 관리", "desktopExtensions.manage.uninstall": "제거", "desktopExtensions.manage.unload": "언로드", "desktopExtensions.openFailed.noPopupMessage": "이 확장 프로그램은 팝업을 지정하지 않았습니다(action.default_popup).", "desktopExtensions.openFailed.noPopupTitle": "확장 프로그램을 열지 못했습니다.", "desktopExtensions.unzip.failed.badFileRead": "다음 오류로 CRX 파일을 읽지 못했습니다. {error}", "desktopExtensions.unzip.failed.badFileWrite": "다음 오류로 {filePath}에 파일을 쓰지 못했습니다. {error}", "desktopExtensions.unzip.failed.badFolderCreate": "다음 오류로 {extensionPath}에 확장 프로그램 폴더를 만들지 못했습니다. {error}", "desktopExtensions.unzip.failed.badManifest": "이 확장 프로그램의 manifest.json을 구문 분석할 수 없습니다. 유효한 확장 프로그램입니까? 발생한 오류는 다음과 같습니다. {error}", "desktopExtensions.unzip.failed.badManifestNoName": "manifest.json에서 이름을 찾을 수 없습니다.", "desktopExtensions.unzip.failed.error": "다음 오류로 인해 CRX 파일의 압축을 풀지 못했습니다. {error}", "desktopExtensions.unzip.failed.noManifest": "CRX에서 manifest.json을 찾을 수 없습니다. 유효한 확장 프로그램입니까?", "desktopInstaller.failedToMove.detail": "앱을 Applications 폴더로 이동시키지 못했습니다. 수동으로 이동시켜 주세요.", "desktopInstaller.failedToMove.title": "앱 이동 실패", "desktopInstaller.invalidInstallDialog.cancelButton.label": "취소", "desktopInstaller.invalidInstallDialog.confirmMove": "Notion 애플리케이션이 정상적으로 설치되지 않았습니다. Notion 앱을 Applications 폴더로 이동시켜도 될까요?", "desktopInstaller.invalidInstallDialog.okButton.label": "확인", "desktopInstaller.invalidInstallDialog.title": "잘못된 설치", "desktopTopbar.appMenu.about": "Notion 소개", "desktopTopbar.appMenu.checkForUpdate": "업데이트 확인", "desktopTopbar.appMenu.checkForUpdate.noUpdateAvailable": "최신 버전의 Notion을 사용 중입니다!", "desktopTopbar.appMenu.checkForUpdate.title": "업데이트 확인", "desktopTopbar.appMenu.checkForUpdate.updateAvailable": "새 버전의 Notion을 사용할 수 있으며 현재 백그라운드에서 다운로드 중입니다. 최신 상태를 유지해 주셔서 감사합니다!", "desktopTopbar.appMenu.checkForUpdate.updateCheckFailed": "인터넷 연결 또는 업데이트 서버 자체에 문제가 있어 업데이트 서버에 연결하지 못했습니다. 나중에 다시 시도하세요.", "desktopTopbar.appMenu.downloadingUpdate": "업데이트 다운로드 중({percentage}%)", "desktopTopbar.appMenu.hide": "Notion 숨기기", "desktopTopbar.appMenu.hideOthers": "다른 항목 숨기기", "desktopTopbar.appMenu.preferences": "환경설정…", "desktopTopbar.appMenu.quit": "끝내기", "desktopTopbar.appMenu.quitWithoutSavingTabs": "탭 저장 없이 종료", "desktopTopbar.appMenu.restartToApplyUpdate": "업데이트 적용을 위해 다시 시작", "desktopTopbar.appMenu.services": "서비스", "desktopTopbar.appMenu.unhide": "모두 표시하기", "desktopTopbar.editMenu.copy": "복사", "desktopTopbar.editMenu.copyLinkToCurrentPage": "현재 페이지 링크 복사", "desktopTopbar.editMenu.copyLinkToCurrentPageBlockTitle": "현재 페이지의 이름 복사", "desktopTopbar.editMenu.cut": "잘라내기", "desktopTopbar.editMenu.paste": "붙여넣기", "desktopTopbar.editMenu.pasteAndMatchStyle": "서식 유지 붙여넣기", "desktopTopbar.editMenu.redo": "다시 실행", "desktopTopbar.editMenu.selectAll": "모두 선택", "desktopTopbar.editMenu.speech": "말하기", "desktopTopbar.editMenu.speech.startSpeaking": "말하기 시작", "desktopTopbar.editMenu.speech.stopSpeaking": "말하기 중지", "desktopTopbar.editMenu.title": "편집", "desktopTopbar.editMenu.undo": "실행 취소", "desktopTopbar.extensionsMenu.install": "확장 프로그램 설치", "desktopTopbar.extensionsMenu.manage": "확장 프로그램 관리", "desktopTopbar.fileMenu.close": "창 닫기", "desktopTopbar.fileMenu.closeTab": "탭 닫기", "desktopTopbar.fileMenu.newNotionWindow": "새 Notion 창", "desktopTopbar.fileMenu.newTab": "새로운 탭", "desktopTopbar.fileMenu.newWindow": "새 창", "desktopTopbar.fileMenu.print": "인쇄", "desktopTopbar.fileMenu.quit": "나가기", "desktopTopbar.fileMenu.quitWithoutSavingTabs": "탭 저장 없이 종료", "desktopTopbar.fileMenu.reopenClosedTab": "마지막으로 닫은 탭 다시 열기", "desktopTopbar.fileMenu.title": "파일", "desktopTopbar.helpMenu.copyInstallId": "설치 ID 복사", "desktopTopbar.helpMenu.disableAdvancedLogging": "고급 로깅 비활성화 후 다시 시작하기", "desktopTopbar.helpMenu.disableDebugLogging": "고급 로깅 비활성화 후 다시 시작", "desktopTopbar.helpMenu.disableHardwareAcceleration": "하드웨어 가속 비활성화 후 다시 시작", "desktopTopbar.helpMenu.enableAdvancedLogging": "고급 로깅 활성화 후 다시 시작하기", "desktopTopbar.helpMenu.enableDebugLogging": "고급 로깅 활성화 후 다시 시작", "desktopTopbar.helpMenu.enableHardwareAcceleration": "하드웨어 가속 활성화 후 다시 시작", "desktopTopbar.helpMenu.openActivityMonitor": "활동 모니터 열기", "desktopTopbar.helpMenu.openConsole": "콘솔 열기", "desktopTopbar.helpMenu.openHelpAndSupport": "도움말과 설명서 열기", "desktopTopbar.helpMenu.recordingNetLog": "네트워크 로그 기록 중…", "desktopTopbar.helpMenu.recordNetLog": "네트워크 로그 기록하기…", "desktopTopbar.helpMenu.recordNetLogConfirmation": "이제 다운로드 폴더에 네트워크 로그가 기록되고 있습니다. 기록을 중지하려면 문제 해결 메뉴에서 '네트워크 로그 기록 중지' 버튼을 클릭하거나 앱을 종료하세요.", "desktopTopbar.helpMenu.recordNetLogFailed": "네트워크 로그를 기록하지 못했습니다. 다시 시도하거나 로그를 검사하여 자세한 내용을 확인하세요.", "desktopTopbar.helpMenu.recordNetLogFailedMessage": "다시 시도하거나 로그를 검사하여 자세한 내용을 확인하세요. 오류는 다음과 같습니다.", "desktopTopbar.helpMenu.recordNetLogFailedTitle": "네트워크 로그 기록 실패", "desktopTopbar.helpMenu.recordNetLogStop": "네트워크 로그 기록 중지하기…", "desktopTopbar.helpMenu.recordPerformanceTrace": "성능 트레이스 기록", "desktopTopbar.helpMenu.recordPerformanceTraceConfirm": "다음 30초 동안 성능 트레이스를 기록하시겠습니까? 완료되면 다운로드 폴더에 저장됩니다.", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmCancel": "취소", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmOk": "성능 트레이스 기록", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmTitle": "성능 트레이스를 기록하시겠습니까?", "desktopTopbar.helpMenu.resetAndEraseAllLocalData": "앱을 초기화하고 로컬 데이터 모두 삭제하기", "desktopTopbar.helpMenu.showLogsInExplorer": "탐색기에서 로그 표시", "desktopTopbar.helpMenu.showLogsInFinder": "Finder에서 로그 표시", "desktopTopbar.helpMenu.title": "도움말", "desktopTopbar.historyMenu.historyBack": "뒤로", "desktopTopbar.historyMenu.historyForward": "앞으로", "desktopTopbar.historyMenu.title": "기록", "desktopTopbar.toggleDevTools": "개발자 도구 토글", "desktopTopbar.toggleWindowDevTools": "Windows 개발자 도구 토글", "desktopTopbar.troubleshootingMenu.title": "트러블슈팅", "desktopTopbar.viewMenu.actualSize": "실제 크기", "desktopTopbar.viewMenu.forceReload": "강제 새로고침", "desktopTopbar.viewMenu.forceReloadDialog.cancel": "취소", "desktopTopbar.viewMenu.forceReloadDialog.message": "현재 오프라인 상태입니다. 이 페이지를 강제로 다시 불러오면 다시 온라인 상태가 될 때까지 페이지에 접근할 수 없게 됩니다.", "desktopTopbar.viewMenu.forceReloadDialog.ok": "그래도 진행하기", "desktopTopbar.viewMenu.forceReloadDialog.title": "정말 강제로 다시 불러오시겠습니까?", "desktopTopbar.viewMenu.reload": "다시 불러오기", "desktopTopbar.viewMenu.showHideSidebar": "사이드바 표시/숨기기", "desktopTopbar.viewMenu.showHideTabSpaceButton": "탭 그룹 표시/숨기기", "desktopTopbar.viewMenu.title": "보기", "desktopTopbar.viewMenu.togglefullscreen": "전체 화면 토글", "desktopTopbar.viewMenu.zoomIn": "확대", "desktopTopbar.viewMenu.zoomOut": "축소", "desktopTopbar.whatsNewMac.title": "MacOS용 Notion 업데이트 사항 확인", "desktopTopbar.whatsNewWindows.title": "Windows용 Notion 업데이트 사항 확인", "desktopTopbar.windowMenu.close": "닫기", "desktopTopbar.windowMenu.front": "앞면", "desktopTopbar.windowMenu.maximize": "최대화", "desktopTopbar.windowMenu.minimize": "최소화", "desktopTopbar.windowMenu.showNextTab": "다음 탭 표시", "desktopTopbar.windowMenu.showPreviousTab": "이전 탭 표시", "desktopTopbar.windowMenu.title": "창", "desktopTopbar.windowMenu.zoom": "확대/축소", "desktopTroubleshooting.resetData.cancel": "취소", "desktopTroubleshooting.resetData.closingWindows": "Notion 창 닫기", "desktopTroubleshooting.resetData.deletingFiles": "파일 삭제 중", "desktopTroubleshooting.resetData.done": "완료", "desktopTroubleshooting.resetData.doneMessage": "앱이 재설정되었습니다.", "desktopTroubleshooting.resetData.failed": "복구 프로세스에서 일부 파일을 삭제하지 못했습니다. 불편을 드려 죄송합니다. 도움이 필요하시면 https://www.notion.so/help를 방문하세요. 앱을 수동으로 완전히 재설정하려면 Notion을 완전히 닫으세요. 그런 다음, 경로({userDataPath})를 삭제하세요.", "desktopTroubleshooting.resetData.message": "이렇게 진행하면 캐시 및 로컬 설정을 포함한 모든 로컬 및 내부 데이터가 삭제되어 Notion 앱을 새로 설치한 상태로 복원되며, Notion에서도 로그아웃됩니다. 페이지 및 기타 앱 내 콘텐츠는 그대로 유지됩니다. 계속하시겠어요?", "desktopTroubleshooting.resetData.reset": "모든 로컬 데이터 재설정", "desktopTroubleshooting.resetData.restart": "다시 시작", "desktopTroubleshooting.resetData.title": "모든 로컬 데이터 재설정 및 삭제", "desktopTroubleshooting.showLogs.error.message.mac": "Finder에서 로그를 표시하는 동안 Notion에서 오류가 발생했습니다.", "desktopTroubleshooting.showLogs.error.message.windows": "Explorer에서 로그를 표시하는 동안 Notion에서 오류가 발생했습니다.", "desktopTroubleshooting.showLogs.error.title": "로그 표시에 실패했습니다.", "desktopTroubleshooting.startRecordingNetLog": "네트워크 로그 기록 시작", "desktopTroubleshooting.stopRecordingNetLog": "네트워크 로그 기록 중지", "menuBarIcon.menu.changeCommandSearchAndNotionAiShortcut": "단축키 편집", "menuBarIcon.menu.changeCommandSearchShortcut": "명령어 검색 단축키 변경", "menuBarIcon.menu.enableQuickSearch": "빠른 검색 활성화", "menuBarIcon.menu.keepInBackground": "백그라운드에서 계속 실행", "menuBarIcon.menu.launchPreferences": "시작 설정", "menuBarIcon.menu.openOnLogin": "로그인 시 Notion 열기", "menuBarIcon.menu.quitNotion": "Notion 종료", "menuBarIcon.menu.showImmediately": "즉시 표시", "menuBarIcon.menu.showNotionInMenuBar": "메뉴 모음에 Notion 표시", "menuBarIcon.menu.toggleCommandSearch": "명령어 검색 토글", "menuBarIcon.menu.toggleNotionAi": "Notion AI 토글", "openAtLogin.dialog.detail": "{operatingSystem}에서 Notion의 '로그인 시 열기' 설정 구성을 금지했습니다. 일반적으로 시스템 설정에서 Notion 시작을 구성했거나 권한이 충분하지 않은 경우 이러한 문제가 발생합니다. 시스템 설정에서 해당 설정을 수동으로 구성할 수 있습니다.", "openAtLogin.dialog.title": "로그인 시 열기", "tabSpaces.deleteDialog.cancelButton": "취소하기", "tabSpaces.deleteDialog.deleteButton": "삭제하기", "tabSpaces.deleteDialog.detail": "이 탭 그룹의 모든 탭이 그룹 해제됩니다.", "tabSpaces.deleteDialog.title": "'{title}' 탭 그룹을 삭제하시겠습니까?", "tabSpaces.snackbar.switchedToTabGroup": "{title}(으)로 전환됨", "tabSpaces.snackbar.switchedToUngroupedTabs": "그룹화되지 않은 탭으로 전환됨", "tabSpaces.snackbar.tabGroupPlaceholder": "탭 그룹", "updatePrompt.detail": "지금 설치하시겠습니까? 창과 탭이 다시 열립니다.", "updatePrompt.installAndRelaunch": "설치 후 다시 시작", "updatePrompt.message": "Notion의 새로운 버전이 출시되었습니다!", "updatePrompt.remindMeLater": "나중에 다시 알림", "window.closeDialog.cancelButton": "취소", "window.closeDialog.confirmButton": "닫기", "window.closeDialog.title.app": "Notion을 닫으시겠습니까?", "window.closeDialog.title.tab": "Notion 탭을 닫으시겠습니까?", "window.closeDialog.title.window": "Notion 창을 닫으시겠습니까?", "window.loadingError.message": "Notion을 로드하는 동안 오류가 발생했습니다. 시작하려면 인터넷에 연결하세요.", "window.loadingError.reload": "다시 불러오기", "window.movedTabSnackbarMessage": "{tabTitle}(을)를 {tabSpaceTitle}(으)로 이동함", "window.tabLoadingError.cancel": "취소", "window.tabMenu.closeOtherTabs": "다른 탭 닫기", "window.tabMenu.closeTab": "탭 닫기", "window.tabMenu.closeTabsToLeft": "왼쪽 탭 닫기", "window.tabMenu.closeTabsToRight": "오른쪽 탭 닫기", "window.tabMenu.copyLink": "링크 복사", "window.tabMenu.duplicateTab": "탭 복제", "window.tabMenu.moveTo": "다음으로 옮기기:", "window.tabMenu.moveToNewWindow": "탭을 새 창에서 열기", "window.tabMenu.moveToSubmenuNewWindow": "새 창", "window.tabMenu.pinTab": "탭 고정", "window.tabMenu.refresh": "탭 새로고침", "window.tabMenu.reopenClosedTab": "마지막으로 닫은 탭 다시 열기", "window.tabMenu.replacePinnedTabUrl": "현재 URL을 고정된 URL로 바꾸기", "window.tabMenu.returnToPinnedTabUrl": "고정된 URL로 돌아가기", "window.tabMenu.ungroupTab": "탭 그룹 해제", "window.tabMenu.unpinTab": "탭 고정 해제", "window.tabTitlePlaceholder": "탭", "window.ungroupedTabSnackbarMessage": "{tabTitle} 그룹 해제됨"}