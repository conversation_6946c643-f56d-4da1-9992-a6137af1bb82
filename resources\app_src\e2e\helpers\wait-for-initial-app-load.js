"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.waitForInitialAppLoad = waitForInitialAppLoad;
exports.waitForInitialWebContents = waitForInitialWebContents;
exports.waitForInitialPageRender = waitForInitialPageRender;
exports.waitForMainAppBoot = waitForMainAppBoot;
const get_identified_pages_1 = require("./get-identified-pages");
const wait_for_function_1 = require("./wait-for-function");
const wait_for_performance_entry_1 = require("./wait-for-performance-entry");
async function waitForInitialAppLoad(app, timeout = 15000) {
    const timeoutStartTime = Date.now();
    let identifiedPages;
    let timeLeft = timeout;
    await app.firstWindow();
    timeLeft = timeLeft - (Date.now() - timeoutStartTime);
    identifiedPages = await waitForInitialWebContents(app, timeLeft);
    const firstTab = identifiedPages[get_identified_pages_1.PageType.tab][0];
    if (!firstTab) {
        throw new Error("No tabs found");
    }
    try {
        timeLeft = timeLeft - (Date.now() - timeoutStartTime);
        await waitForInitialPageRender(firstTab, timeLeft);
    }
    catch (error) {
        throw new Error(`Timed out waiting for initial app.js JavaScript to load.`);
    }
    identifiedPages = (0, get_identified_pages_1.getIdentifiedPages)(app);
    return identifiedPages;
}
async function waitForInitialWebContents(app, timeout = 30000) {
    await app.firstWindow();
    try {
        return await (0, wait_for_function_1.waitForFunction)(() => {
            const identifiedPages = (0, get_identified_pages_1.getIdentifiedPages)(app);
            const oneTab = identifiedPages[get_identified_pages_1.PageType.tab].length > 0;
            const oneTabPreview = identifiedPages[get_identified_pages_1.PageType.tabPreview].length > 0;
            const oneTabBar = identifiedPages[get_identified_pages_1.PageType.tabBar].length > 0;
            const oneQuickSearch = identifiedPages[get_identified_pages_1.PageType.quickSearch].length > 0;
            if (oneTab && oneTabPreview && oneTabBar && oneQuickSearch) {
                return identifiedPages;
            }
        }, timeout);
    }
    catch (error) {
        const identifiedPages = (0, get_identified_pages_1.getIdentifiedPages)(app);
        const stringifiedIdentifiedPages = JSON.stringify(identifiedPages, null, 2);
        throw new Error(`Timed out waiting for initial web contents to exist. Found the following web contents: ${stringifiedIdentifiedPages}`);
    }
}
async function waitForInitialPageRender(page, timeout = 10000) {
    return (0, wait_for_performance_entry_1.waitForPerformanceEntry)(page, "initial_page_render", timeout);
}
async function waitForMainAppBoot(page, timeout = 10000) {
    return (0, wait_for_performance_entry_1.waitForPerformanceEntry)(page, "main_80_app_completed", timeout);
}
