"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.waitForPerformanceEntry = waitForPerformanceEntry;
async function waitForPerformanceEntry(page, entryName, timeout = 10000) {
    return page.evaluate(async ({ timeout, entryName }) => {
        let delay = 100;
        const maxDelay = timeout;
        const multiplier = 1.5;
        const startTime = Date.now();
        while (true) {
            if (Date.now() - startTime > timeout) {
                throw new Error(`Timed out after ${timeout}ms waiting for function to return value`);
            }
            const result = performance.getEntriesByName(entryName);
            console.log("result", result);
            if (result.length > 0) {
                return result;
            }
            await new Promise(resolve => setTimeout(resolve, delay));
            delay = Math.min(delay * multiplier, maxDelay);
        }
    }, { timeout, entryName });
}
