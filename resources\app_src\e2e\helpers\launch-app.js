"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.launchApp = launchApp;
const test_1 = require("@playwright/test");
const get_executable_path_1 = require("./get-executable-path");
const log_1 = require("./log");
async function launchApp() {
    (0, log_1.log)("Looking for Notion");
    let executablePath;
    try {
        executablePath = (0, get_executable_path_1.getExecutablePath)();
    }
    catch (error) {
        (0, log_1.warn)("No Notion build was found. Please run 'notion desktop build' first.");
        throw error;
    }
    (0, log_1.log)(`Found Notion at ${executablePath}`);
    const app = await test_1._electron.launch({
        executablePath,
        env: {
            ...process.env,
            IS_PLAYWRIGHT_TEST: "true",
        },
    });
    (0, log_1.log)("App launched");
    await app.firstWindow();
    (0, log_1.log)("Window opened");
    return app;
}
