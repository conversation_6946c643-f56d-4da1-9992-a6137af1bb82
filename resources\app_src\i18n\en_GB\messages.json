{"activityMonitor.copyDiagnosticInformation": "Copy Diagnostic Information", "activityMonitor.copyExecutablePath": "Copy Executable Path", "activityMonitor.copyUrl": "Copy URL", "activityMonitor.forceKillProcess": "Force Kill Process", "activityMonitor.inspectActivityMonitor": "Inspect Activity Monitor", "activityMonitor.killProcess": "Kill Process", "activityMonitor.openDevTools": "Open DevTools", "activityMonitor.reload": "Reload", "clientPlaceholder.placeholderDescription": "Click to display in this window", "clientPlaceholder.placeholderTitle": "Currently viewing content in another window", "commandSearch.window.title": "Notion – Command search", "crashWatchdog.dialog.abnormalExit": "Abnormal Exit: The page exited with a non-zero exit code.", "crashWatchdog.dialog.buttonCloseTab": "Close Tab", "crashWatchdog.dialog.buttonCloseWindow": "Close Window", "crashWatchdog.dialog.buttonRestartApp": "Restart Notion", "crashWatchdog.dialog.crashed": "Crashed: The page crashed due to an unknown reason.", "crashWatchdog.dialog.details": "Page URL: {url} Reason: {reason} Exit code: {exitCode}", "crashWatchdog.dialog.integrityFailure": "Integrity Failure: The page failed code integrity checks.", "crashWatchdog.dialog.killed": "Killed: The page was killed by an external process.", "crashWatchdog.dialog.launchFailed": "Launch Failed: The process failed to launch.", "crashWatchdog.dialog.message": "Something went wrong while displaying this tab and we could not automatically recover it.", "crashWatchdog.dialog.oom": "OOM: The page ran out of memory and crashed.", "crashWatchdog.dialog.title": "Something went wrong", "crashWatchdog.dialog.urlUnknown": "Unknown", "desktop.activityMonitor.all": "All", "desktop.activityMonitor.cpuPercent": "% CPU", "desktop.activityMonitor.cpuPercentDescription": "The fraction of available CPU time used by the process.", "desktop.activityMonitor.cpuTime": "CPU Time", "desktop.activityMonitor.cpuTimeDescription": "The total seconds of CPU time used since process start-up.", "desktop.activityMonitor.creationTime": "Created", "desktop.activityMonitor.creationTimeDescription": "The amount of time since the process was created.", "desktop.activityMonitor.frames": "Frames", "desktop.activityMonitor.framesDescription": "The number of frames managed by the process. Many renderer processes are responsible for multiple frames.", "desktop.activityMonitor.hidden": "Hidden", "desktop.activityMonitor.hideColumns": "Hide Columns", "desktop.activityMonitor.hideFilters": "Hide Filters", "desktop.activityMonitor.idleWakeupsPerSecond": "Idle wakeups", "desktop.activityMonitor.idleWakeupsPerSecondDescription": "The number of times the process has woken up the CPU since the last activity monitor update.", "desktop.activityMonitor.loading": "Loading activity data", "desktop.activityMonitor.memCurrent": "Memory (current)", "desktop.activityMonitor.memCurrentDescription": "The “working set” size of the process, which is the set of pages currently resident in RAM. This number does not account for the operating system’s memory compression, management of inactive and cached pages or other memory management techniques. The amount of physical memory used by the process is likely much smaller.", "desktop.activityMonitor.memPeak": "Memory (peak)", "desktop.activityMonitor.memPeakDescription": "The peak working set size of the process, which is the maximum amount of physical memory used by the process since it was launched. The “working set” size of the process, which is the set of pages currently resident in RAM. This number does not account for the operating system’s memory compression, management of inactive and cached pages or other memory management techniques. The amount of physical memory used by the process is likely much smaller.", "desktop.activityMonitor.memPrivate": "Memory (private)", "desktop.activityMonitor.memPrivateDescription": "The amount of physical memory allocated by the process that is not shared with other processes, such as JS heap or HTML content.", "desktop.activityMonitor.memShared": "Memory (shared)", "desktop.activityMonitor.memSharedDescription": "The amount of physical memory allocated by the process that is shared with other processes, such as shared libraries or mapped files.", "desktop.activityMonitor.mixed": "Mixed", "desktop.activityMonitor.parentWindowId": "Parent Window ID", "desktop.activityMonitor.parentWindowIdDescription": "The unique identifier of the window containing this tab.", "desktop.activityMonitor.pid": "PID", "desktop.activityMonitor.pidDescription": "The process ID of the process as used by the operating system.", "desktop.activityMonitor.processName": "Process name", "desktop.activityMonitor.showColumns": "Show Columns", "desktop.activityMonitor.showFilters": "Show Filters", "desktop.activityMonitor.tabId": "Tab ID", "desktop.activityMonitor.tabIdDescription": "The unique identifier of the tab in the Notion app.", "desktop.activityMonitor.type": "Type", "desktop.activityMonitor.url": "URL", "desktop.activityMonitor.urlDescription": "The URL of the process. Many renderer processes are responsible for multiple frames, please see the Frames column for more information.", "desktop.activityMonitor.visibilityState": "Visibility", "desktop.activityMonitor.visibilityStateDescription": "The visibility state of the process. If the process is a renderer process, this will be the visibility state of the main frame.", "desktop.activityMonitor.visible": "Visible", "desktop.tabBar.backButtonLabel": "Back", "desktop.tabBar.closeSidebarLabel": "Close sidebar", "desktop.tabBar.closeTabLabel": "Close tab, {tabTitle}", "desktop.tabBar.forwardButtonLabel": "Forward", "desktop.tabBar.newTabButtonLabel": "New tab", "desktop.tabBar.openSidebarLabel": "Open sidebar", "desktop.tabBar.tabSpacesLabel": "Tab Spaces", "desktopExtensions.install.failed.title": "Failed to install extension", "desktopExtensions.manage.cancel": "Cancel", "desktopExtensions.manage.disable": "Disable", "desktopExtensions.manage.enable": "Enable", "desktopExtensions.manage.message": "What would you like to do with {extensionTitle} {extensionVersion}?", "desktopExtensions.manage.title": "Manage extension", "desktopExtensions.manage.uninstall": "Uninstall", "desktopExtensions.openFailed.noPopupMessage": "This extension did not specify a popup (action.default_popup)", "desktopExtensions.openFailed.noPopupTitle": "Failed to open extension", "desktopExtensions.unzip.failed.badFileRead": "Failed to read CRX file: {error}", "desktopExtensions.unzip.failed.badFileWrite": "Failed to write file {filePath}: {error}", "desktopExtensions.unzip.failed.badFolderCreate": "Failed to create extensions folder at {extensionPath}: {error}", "desktopExtensions.unzip.failed.badManifest": "The manifest.json in this extension could not be parsed. Is this a valid extension? The error was: {error}", "desktopExtensions.unzip.failed.badManifestNoName": "No name found in manifest.json", "desktopExtensions.unzip.failed.error": "Failed to unzip CRX file: {error}", "desktopExtensions.unzip.failed.noManifest": "No manifest.json found in CRX. Is this a valid extension?", "desktopInstaller.failedToMove.detail": "We failed to move the app to your Applications folder. Please move it manually.", "desktopInstaller.failedToMove.title": "Failed to move app", "desktopInstaller.invalidInstallDialog.cancelButton.label": "Cancel", "desktopInstaller.invalidInstallDialog.confirmMove": "Your Notion application is not installed properly. Can we move your Notion app into your Applications folder?", "desktopInstaller.invalidInstallDialog.okButton.label": "OK", "desktopInstaller.invalidInstallDialog.title": "Invalid install", "desktopTopbar.appMenu.about": "About Notion", "desktopTopbar.appMenu.checkForUpdate": "Check for Updates…", "desktopTopbar.appMenu.checkForUpdate.noUpdateAvailable": "You’re on the latest version of Notion!", "desktopTopbar.appMenu.checkForUpdate.title": "Check for updates", "desktopTopbar.appMenu.checkForUpdate.updateAvailable": "A new version of Notion is available and currently being downloaded in the background. Thanks for staying up to date!", "desktopTopbar.appMenu.checkForUpdate.updateCheckFailed": "Notion failed to make a connection with the update server, either because of a problem with your Internet connection or the update server itself. Please try again later.", "desktopTopbar.appMenu.downloadingUpdate": "Downloading update ({percentage}%)", "desktopTopbar.appMenu.hide": "Hide Notion", "desktopTopbar.appMenu.hideOthers": "Hide others", "desktopTopbar.appMenu.preferences": "Preferences…", "desktopTopbar.appMenu.quit": "Quit", "desktopTopbar.appMenu.quitWithoutSavingTabs": "Quit without saving tabs", "desktopTopbar.appMenu.restartToApplyUpdate": "Restart to apply update", "desktopTopbar.appMenu.services": "Services", "desktopTopbar.appMenu.unhide": "Show All", "desktopTopbar.editMenu.copy": "Copy", "desktopTopbar.editMenu.copyLinkToCurrentPage": "Copy link to current page", "desktopTopbar.editMenu.copyLinkToCurrentPageBlockTitle": "Copy name of current page", "desktopTopbar.editMenu.cut": "Cut", "desktopTopbar.editMenu.paste": "Paste", "desktopTopbar.editMenu.pasteAndMatchStyle": "Paste and Match Style", "desktopTopbar.editMenu.redo": "Redo", "desktopTopbar.editMenu.selectAll": "Select all", "desktopTopbar.editMenu.speech": "Speech", "desktopTopbar.editMenu.speech.startSpeaking": "Start speaking", "desktopTopbar.editMenu.speech.stopSpeaking": "Stop speaking", "desktopTopbar.editMenu.title": "Edit", "desktopTopbar.editMenu.undo": "Undo", "desktopTopbar.extensionsMenu.install": "Install extension...", "desktopTopbar.extensionsMenu.manage": "Manage extensions", "desktopTopbar.fileMenu.close": "Close window", "desktopTopbar.fileMenu.closeTab": "Close tab", "desktopTopbar.fileMenu.newNotionWindow": "New Notion window", "desktopTopbar.fileMenu.newTab": "New tab", "desktopTopbar.fileMenu.newWindow": "New window", "desktopTopbar.fileMenu.print": "Print…", "desktopTopbar.fileMenu.quit": "Exit", "desktopTopbar.fileMenu.quitWithoutSavingTabs": "Exit without saving tabs", "desktopTopbar.fileMenu.reopenClosedTab": "Reopen last closed tab", "desktopTopbar.fileMenu.title": "File", "desktopTopbar.helpMenu.copyInstallId": "Copy Install ID", "desktopTopbar.helpMenu.disableAdvancedLogging": "Disable advanced logging and restart", "desktopTopbar.helpMenu.disableDebugLogging": "Disable advanced logging and restart", "desktopTopbar.helpMenu.disableHardwareAcceleration": "Disable hardware acceleration and restart", "desktopTopbar.helpMenu.enableAdvancedLogging": "Enable advanced logging and restart", "desktopTopbar.helpMenu.enableDebugLogging": "Enable advanced logging and restart", "desktopTopbar.helpMenu.enableHardwareAcceleration": "Enable hardware acceleration and restart", "desktopTopbar.helpMenu.openActivityMonitor": "Open activity monitor", "desktopTopbar.helpMenu.openConsole": "Open console", "desktopTopbar.helpMenu.openHelpAndSupport": "Open help and documentation", "desktopTopbar.helpMenu.recordingNetLog": "Recording network log…", "desktopTopbar.helpMenu.recordNetLog": "Record Network Log…", "desktopTopbar.helpMenu.recordNetLogConfirmation": "A net log is now being recorded to your Downloads folder. To stop the recording, click the ‘Stop Recording Network Log’ button in the Troubleshooting menu or exit the app.", "desktopTopbar.helpMenu.recordNetLogFailed": "Network log recording failed. Please try again or inspect the logs for more information.", "desktopTopbar.helpMenu.recordNetLogFailedMessage": "Please try again or inspect the logs for more information. The error was:", "desktopTopbar.helpMenu.recordNetLogFailedTitle": "Network log recording failed", "desktopTopbar.helpMenu.recordNetLogStop": "Stop Recording Network Log…", "desktopTopbar.helpMenu.recordPerformanceTrace": "Record performance trace…", "desktopTopbar.helpMenu.recordPerformanceTraceConfirm": "Do you want to record a performance trace for the next 30 seconds? Once done, it will be placed in your Downloads folder.", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmCancel": "Cancel", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmOk": "Record performance trace", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmTitle": "Record a performance trace?", "desktopTopbar.helpMenu.resetAndEraseAllLocalData": "Reset and erase all local data", "desktopTopbar.helpMenu.showLogsInExplorer": "Show logs in Explorer", "desktopTopbar.helpMenu.showLogsInFinder": "Show logs in Finder", "desktopTopbar.helpMenu.title": "Help", "desktopTopbar.historyMenu.historyBack": "Back", "desktopTopbar.historyMenu.historyForward": "Forward", "desktopTopbar.historyMenu.title": "History", "desktopTopbar.toggleDevTools": "Toggle Developer Tools", "desktopTopbar.toggleWindowDevTools": "Toggle Window developer tools", "desktopTopbar.troubleshootingMenu.title": "Troubleshooting", "desktopTopbar.viewMenu.actualSize": "Actual size", "desktopTopbar.viewMenu.forceReload": "Force reload", "desktopTopbar.viewMenu.forceReloadDialog.cancel": "Cancel", "desktopTopbar.viewMenu.forceReloadDialog.message": "You are currently offline. Force reloading this page will cause you to lose access to it until you’re back online.", "desktopTopbar.viewMenu.forceReloadDialog.ok": "Reload anyway", "desktopTopbar.viewMenu.forceReloadDialog.title": "Are you sure you want to force reload?", "desktopTopbar.viewMenu.reload": "Reload", "desktopTopbar.viewMenu.showHideSidebar": "Show/hide sidebar", "desktopTopbar.viewMenu.showHideTabSpaceButton": "Show/hide tab groups", "desktopTopbar.viewMenu.title": "View", "desktopTopbar.viewMenu.togglefullscreen": "Toggle Full Screen", "desktopTopbar.viewMenu.zoomIn": "Zoom In", "desktopTopbar.viewMenu.zoomOut": "Zoom Out", "desktopTopbar.whatsNewMac.title": "Open What’s New in Notion for macOS", "desktopTopbar.whatsNewWindows.title": "Open What’s New in Notion for Windows", "desktopTopbar.windowMenu.close": "Close", "desktopTopbar.windowMenu.front": "Front", "desktopTopbar.windowMenu.maximize": "<PERSON><PERSON>", "desktopTopbar.windowMenu.minimize": "Minimise", "desktopTopbar.windowMenu.showNextTab": "Show next tab", "desktopTopbar.windowMenu.showPreviousTab": "Show previous tab", "desktopTopbar.windowMenu.title": "Window", "desktopTopbar.windowMenu.zoom": "Zoom", "desktopTroubleshooting.resetData.cancel": "Cancel", "desktopTroubleshooting.resetData.closingWindows": "Closing Notion windows", "desktopTroubleshooting.resetData.deletingFiles": "Deleting files", "desktopTroubleshooting.resetData.done": "Done", "desktopTroubleshooting.resetData.doneMessage": "The app has been reset.", "desktopTroubleshooting.resetData.failed": "Our recovery process failed to delete some files. We apologise for the trouble – please visit https://www.notion.so/help for assistance. To force a full reset of the app manually, please close Notion entirely. Then, delete the following path: {userDataPath}", "desktopTroubleshooting.resetData.message": "This will delete all local and internal data, including the cache and local settings, restoring the Notion app to a freshly installed state. It will also log you out of Notion. Your pages and other in-app content will be left untouched. Do you want to continue?", "desktopTroubleshooting.resetData.reset": "Reset all local data", "desktopTroubleshooting.resetData.restart": "<PERSON><PERSON>", "desktopTroubleshooting.resetData.title": "Resetting and erasing all local data", "desktopTroubleshooting.showLogs.error.message.mac": "<PERSON><PERSON> encountered an error while trying to show the logs in <PERSON><PERSON>:", "desktopTroubleshooting.showLogs.error.message.windows": "<PERSON><PERSON> encountered an error while trying to show the logs in Explorer:", "desktopTroubleshooting.showLogs.error.title": "Showing the logs failed", "desktopTroubleshooting.startRecordingNetLog": "Start Recording Network Log", "desktopTroubleshooting.stopRecordingNetLog": "Stop Recording Network Log", "menuBarIcon.menu.changeCommandSearchAndNotionAiShortcut": "Edit shortcuts", "menuBarIcon.menu.changeCommandSearchShortcut": "Change command search shortcuts", "menuBarIcon.menu.enableQuickSearch": "Enable quick search", "menuBarIcon.menu.keepInBackground": "Keep in background", "menuBarIcon.menu.launchPreferences": "Launch preferences", "menuBarIcon.menu.openOnLogin": "Open Notion at login", "menuBarIcon.menu.quitNotion": "Quit Notion", "menuBarIcon.menu.showImmediately": "Show immediately", "menuBarIcon.menu.showNotionInMenuBar": "Show Notion in menu bar", "menuBarIcon.menu.toggleCommandSearch": "Toggle command search", "menuBarIcon.menu.toggleNotionAi": "Toggle Notion AI", "openAtLogin.dialog.detail": "{operatingSystem} prevented <PERSON><PERSON> from configuring the ‘Open at Login’ setting. This usually happens when Notion’s startup has been configured in the system settings or if you have insufficient permissions. You can still configure this setting manually in the system settings.", "openAtLogin.dialog.title": "Open at Login", "tabSpaces.deleteDialog.cancelButton": "Cancel", "tabSpaces.deleteDialog.deleteButton": "Delete", "tabSpaces.deleteDialog.detail": "All tabs in this tab group will be ungrouped.", "tabSpaces.deleteDialog.title": "Delete your ‘{title}’ tab group?", "tabSpaces.snackbar.switchedToTabGroup": "Switched to {title}", "tabSpaces.snackbar.switchedToUngroupedTabs": "Switched to ungrouped tabs", "tabSpaces.snackbar.tabGroupPlaceholder": "Tab Group", "updatePrompt.detail": "Would you like to install it now? We’ll reopen your windows and tabs for you.", "updatePrompt.installAndRelaunch": "Install and relaunch", "updatePrompt.message": "A new version of Notion is available!", "updatePrompt.remindMeLater": "Remind me later", "window.closeDialog.cancelButton": "Cancel", "window.closeDialog.confirmButton": "Close", "window.closeDialog.title.app": "Close Notion?", "window.closeDialog.title.tab": "Close Notion tab?", "window.closeDialog.title.window": "Close Notion window?", "window.loadingError.message": "Error loading Notion, connect to the internet to get started.", "window.loadingError.reload": "Reload", "window.movedTabSnackbarMessage": "Moved {tabTitle} to {tabSpaceTitle}", "window.tabLoadingError.cancel": "Cancel", "window.tabMenu.closeOtherTabs": "Close other tabs", "window.tabMenu.closeTab": "Close Tab", "window.tabMenu.closeTabsToLeft": "Close tabs to the left", "window.tabMenu.closeTabsToRight": "Close tabs to the right", "window.tabMenu.copyLink": "Copy link", "window.tabMenu.duplicateTab": "Duplicate tab", "window.tabMenu.moveTo": "Move to", "window.tabMenu.moveToNewWindow": "Move tab to new window", "window.tabMenu.moveToSubmenuNewWindow": "New window", "window.tabMenu.pinTab": "Pin tab", "window.tabMenu.refresh": "Refresh tab", "window.tabMenu.reopenClosedTab": "Reopen last closed tab", "window.tabMenu.replacePinnedTabUrl": "Replace pinned URL with current", "window.tabMenu.returnToPinnedTabUrl": "Return to pinned URL", "window.tabMenu.ungroupTab": "Ungroup tab", "window.tabMenu.unpinTab": "Unpin tab", "window.tabTitlePlaceholder": "Tab", "window.ungroupedTabSnackbarMessage": "Ungrouped {tabTitle}"}