{"activityMonitor.copyDiagnosticInformation": "Copiar información de diagnóstico", "activityMonitor.copyExecutablePath": "Copiar ruta ejecutable", "activityMonitor.copyUrl": "Copiar URL", "activityMonitor.forceKillProcess": "<PERSON>zar proceso kill", "activityMonitor.inspectActivityMonitor": "Inspeccionar el monitor de actividad", "activityMonitor.killProcess": "Proceso kill", "activityMonitor.openDevTools": "<PERSON><PERSON><PERSON> DevTools", "activityMonitor.reload": "Actualizar", "clientPlaceholder.placeholderDescription": "Haz clic para mostrar en esta ventana", "clientPlaceholder.placeholderTitle": "Actualmente se muestra el contenido en otra ventana", "commandSearch.window.title": "Notion - Atajo de búsqueda", "crashWatchdog.dialog.abnormalExit": "Salida anormal: la página salió con un código de salida distinto de cero.", "crashWatchdog.dialog.buttonCloseTab": "<PERSON><PERSON>r pesta<PERSON>", "crashWatchdog.dialog.buttonCloseWindow": "<PERSON><PERSON><PERSON> ventana", "crashWatchdog.dialog.buttonRestartApp": "Reiniciar Notion", "crashWatchdog.dialog.crashed": "Bloqueo: la página se bloqueó debido a una razón desconocida.", "crashWatchdog.dialog.details": "URL de la página: {url} Motivo: {reason} Código de <PERSON>lida: {exitCode}", "crashWatchdog.dialog.integrityFailure": "Error de integridad: la página falló en las comprobaciones de integridad del código.", "crashWatchdog.dialog.killed": "Cierre: la página se cerró debido a un proceso externo.", "crashWatchdog.dialog.launchFailed": "Error de inicio: el proceso no se pudo iniciar.", "crashWatchdog.dialog.message": "Se produjo un error al mostrar esta pestaña y no pudimos recuperarla automáticamente.", "crashWatchdog.dialog.oom": "OOM: la página se quedó sin memoria y se bloqueó.", "crashWatchdog.dialog.title": "Algo salió mal", "crashWatchdog.dialog.urlUnknown": "Desconocido", "desktop.activityMonitor.all": "Todo", "desktop.activityMonitor.cpuPercent": "% CPU", "desktop.activityMonitor.cpuPercentDescription": "La fracción de tiempo de CPU disponible utilizada por el proceso.", "desktop.activityMonitor.cpuTime": "Tiempo de CPU", "desktop.activityMonitor.cpuTimeDescription": "El total de segundos de tiempo de CPU utilizado desde el inicio del proceso.", "desktop.activityMonitor.creationTime": "Hora de creación", "desktop.activityMonitor.creationTimeDescription": "La cantidad de tiempo desde que se creó el proceso.", "desktop.activityMonitor.frames": "<PERSON>", "desktop.activityMonitor.framesDescription": "El número de marcos administrados por el proceso. Muchos procesos de renderización son responsables de múltiples marcos.", "desktop.activityMonitor.hidden": "Oculto", "desktop.activityMonitor.hideColumns": "Ocultar columnas", "desktop.activityMonitor.hideFilters": "Ocultar filtros", "desktop.activityMonitor.idleWakeupsPerSecond": "Despertares inactivos", "desktop.activityMonitor.idleWakeupsPerSecondDescription": "El número de veces que el proceso reactivó la CPU desde la última actualización del monitor de actividad.", "desktop.activityMonitor.loading": "Cargando datos de actividad", "desktop.activityMonitor.memCurrent": "Memoria (actual)", "desktop.activityMonitor.memCurrentDescription": "El tamaño del “conjunto de trabajo” del proceso, que corresponde al conjunto de páginas que residen actualmente en la memoria RAM. Este número no tiene en cuenta la compresión de memoria del sistema operativo, la gestión de páginas inactivas y en caché u otras técnicas de gestión de memoria. Es probable que la cantidad de memoria física utilizada por el proceso sea mucho menor.", "desktop.activityMonitor.memPeak": "Memoria (pico)", "desktop.activityMonitor.memPeakDescription": "El tamaño máximo del conjunto de trabajo del proceso, que corresponde a la cantidad máxima de memoria física utilizada por el proceso desde que se inició. El tamaño del “conjunto de trabajo” del proceso, que corresponde al conjunto de páginas que residen actualmente en la memoria RAM. Este número no tiene en cuenta la compresión de memoria del sistema operativo, la gestión de páginas inactivas y en caché u otras técnicas de gestión de memoria. Es probable que la cantidad de memoria física utilizada por el proceso sea mucho menor.", "desktop.activityMonitor.memPrivate": "Memoria (privada)", "desktop.activityMonitor.memPrivateDescription": "La cantidad de memoria física asignada por el proceso que no se comparte con otros procesos, como el montón de JavaScript o contenido HTML.", "desktop.activityMonitor.memShared": "Memoria (compartida)", "desktop.activityMonitor.memSharedDescription": "La cantidad de memoria física asignada por el proceso que se comparte con otros procesos, como bibliotecas compartidas o archivos mapeados.", "desktop.activityMonitor.mixed": "Mixto", "desktop.activityMonitor.parentWindowId": "ID de la ventana principal", "desktop.activityMonitor.parentWindowIdDescription": "El identificador único de la ventana que contiene esta pestaña.", "desktop.activityMonitor.pid": "PID", "desktop.activityMonitor.pidDescription": "El ID de proceso utilizado por el sistema operativo.", "desktop.activityMonitor.processName": "Nombre del proceso", "desktop.activityMonitor.showColumns": "Mostrar columnas", "desktop.activityMonitor.showFilters": "Mostrar filtros", "desktop.activityMonitor.tabId": "ID de la pestaña", "desktop.activityMonitor.tabIdDescription": "El identificador único de la pestaña en la app de Notion.", "desktop.activityMonitor.type": "Tipo", "desktop.activityMonitor.url": "URL", "desktop.activityMonitor.urlDescription": "La URL del proceso. Muchos procesos de renderización son responsables de múltiples marcos. Consulta la columna Marcos para obtener más información.", "desktop.activityMonitor.visibilityState": "Visibilidad", "desktop.activityMonitor.visibilityStateDescription": "El estado de visibilidad del proceso. Si el proceso es de renderización, este será el estado de visibilidad del marco principal.", "desktop.activityMonitor.visible": "Visible", "desktop.tabBar.backButtonLabel": "Atrás", "desktop.tabBar.closeSidebarLabel": "Ce<PERSON>r barra lateral", "desktop.tabBar.closeTabLabel": "Cerrar la pestaña {tabTitle}", "desktop.tabBar.forwardButtonLabel": "Adelante", "desktop.tabBar.newTabButtonLabel": "Nueva pestaña", "desktop.tabBar.openSidebarLabel": "Abrir la barra lateral", "desktop.tabBar.tabSpacesLabel": "Espacios de pestañas", "desktopExtensions.install.failed.title": "Se produjo un error al instalar la extensión", "desktopExtensions.manage.cancel": "<PERSON><PERSON><PERSON>", "desktopExtensions.manage.disable": "Desactivar", "desktopExtensions.manage.enable": "Activar", "desktopExtensions.manage.message": "¿Qué te gustaría hacer con {extensionTitle} {extensionVersion}?", "desktopExtensions.manage.title": "Gestionar extensión", "desktopExtensions.manage.uninstall": "<PERSON><PERSON><PERSON><PERSON>", "desktopExtensions.manage.unload": "<PERSON><PERSON><PERSON>", "desktopExtensions.openFailed.noPopupMessage": "Esta extensión no especificó una ventana emergente (action.default_popup)", "desktopExtensions.openFailed.noPopupTitle": "No se pudo abrir la extensión", "desktopExtensions.unzip.failed.badFileRead": "Se produjo un error al leer el archivo CRX: {error}", "desktopExtensions.unzip.failed.badFileWrite": "Se produjo un error al escribir el archivo {filePath}: {error}", "desktopExtensions.unzip.failed.badFolderCreate": "Se produjo un error al crear la carpeta de extensiones en {extensionPath}: {error}", "desktopExtensions.unzip.failed.badManifest": "Se produjo un error al analizar el archivo manifest.json en esta extensión. ¿Es la extensión válida? El error fue: {error}", "desktopExtensions.unzip.failed.badManifestNoName": "No se encontró ningún nombre en el archivo manifest.json", "desktopExtensions.unzip.failed.error": "Se produjo un error al descomprimir el archivo CRX: {error}", "desktopExtensions.unzip.failed.noManifest": "No se encontró el archivo manifest.json en CRX. ¿Es la extensión válida?", "desktopInstaller.failedToMove.detail": "No pudimos mover la app a la carpeta Aplicaciones. Deberás hacerlo de forma manual.", "desktopInstaller.failedToMove.title": "No se pudo mover la app", "desktopInstaller.invalidInstallDialog.cancelButton.label": "<PERSON><PERSON><PERSON>", "desktopInstaller.invalidInstallDialog.confirmMove": "La aplicación de Notion no se instaló correctamente. ¿Nos das permiso para mover la app de Notion a la carpeta Aplicaciones?", "desktopInstaller.invalidInstallDialog.okButton.label": "Aceptar", "desktopInstaller.invalidInstallDialog.title": "Instalación no válida", "desktopTopbar.appMenu.about": "Acerca de Notion", "desktopTopbar.appMenu.checkForUpdate": "Buscar actualizaciones…", "desktopTopbar.appMenu.checkForUpdate.noUpdateAvailable": "¡Tienes la última versión de Notion!", "desktopTopbar.appMenu.checkForUpdate.title": "Buscar actualizaciones", "desktopTopbar.appMenu.checkForUpdate.updateAvailable": "Hay una nueva versión de Notion disponible y se está descargando en segundo plano. ¡Gracias por estar al día!", "desktopTopbar.appMenu.checkForUpdate.updateCheckFailed": "Notion no pudo conectarse con el servidor de actualización debido a un problema de la conexión a Internet o del propio servidor. Inténtalo de nuevo más tarde.", "desktopTopbar.appMenu.downloadingUpdate": "Descargando actualización ({percentage} %)", "desktopTopbar.appMenu.hide": "Ocultar Notion", "desktopTopbar.appMenu.hideOthers": "Ocultar otros", "desktopTopbar.appMenu.preferences": "Preferencias…", "desktopTopbar.appMenu.quit": "Salir", "desktopTopbar.appMenu.quitWithoutSavingTabs": "Ce<PERSON>r sin guardar las pestañas", "desktopTopbar.appMenu.restartToApplyUpdate": "Reinicia para aplicar la actualización", "desktopTopbar.appMenu.services": "<PERSON><PERSON><PERSON>", "desktopTopbar.appMenu.unhide": "<PERSON><PERSON> todo", "desktopTopbar.editMenu.copy": "Copiar", "desktopTopbar.editMenu.copyLinkToCurrentPage": "Copiar el enlace a la página actual", "desktopTopbar.editMenu.copyLinkToCurrentPageBlockTitle": "Copiar el nombre de la página actual", "desktopTopbar.editMenu.cut": "Cortar", "desktopTopbar.editMenu.paste": "<PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.pasteAndMatchStyle": "Pegar y combinar formato", "desktopTopbar.editMenu.redo": "<PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "desktopTopbar.editMenu.speech": "Voz", "desktopTopbar.editMenu.speech.startSpeaking": "Empezar lo<PERSON>", "desktopTopbar.editMenu.speech.stopSpeaking": "Detener locución", "desktopTopbar.editMenu.title": "<PERSON><PERSON>", "desktopTopbar.editMenu.undo": "<PERSON><PERSON><PERSON>", "desktopTopbar.extensionsMenu.install": "Instalar extensión", "desktopTopbar.extensionsMenu.manage": "Gestionar extensiones", "desktopTopbar.fileMenu.close": "<PERSON><PERSON><PERSON> ventana", "desktopTopbar.fileMenu.closeTab": "<PERSON><PERSON>r pesta<PERSON>", "desktopTopbar.fileMenu.newNotionWindow": "Nueva ventana de Notion", "desktopTopbar.fileMenu.newTab": "Nueva pestaña", "desktopTopbar.fileMenu.newWindow": "Nueva ventana", "desktopTopbar.fileMenu.print": "Imprimir...", "desktopTopbar.fileMenu.quit": "Salir", "desktopTopbar.fileMenu.quitWithoutSavingTabs": "Salir sin guardar las pestañas", "desktopTopbar.fileMenu.reopenClosedTab": "Volver a abrir la última pestaña cerrada", "desktopTopbar.fileMenu.title": "Archivo", "desktopTopbar.helpMenu.copyInstallId": "Copiar ID de instalación", "desktopTopbar.helpMenu.disableAdvancedLogging": "Desactivar el registro avanzado y reiniciar", "desktopTopbar.helpMenu.disableDebugLogging": "Desactivar el inicio de sesión acelerado y reiniciar", "desktopTopbar.helpMenu.disableHardwareAcceleration": "Desactivar aceleración por hardware y reiniciar", "desktopTopbar.helpMenu.enableAdvancedLogging": "Activar el registro avanzado y reiniciar", "desktopTopbar.helpMenu.enableDebugLogging": "Activar el inicio de sesión acelerado y reiniciar", "desktopTopbar.helpMenu.enableHardwareAcceleration": "Activar aceleración por hardware y reiniciar", "desktopTopbar.helpMenu.openActivityMonitor": "Abrir el monitor de actividad", "desktopTopbar.helpMenu.openConsole": "<PERSON><PERSON><PERSON> consola", "desktopTopbar.helpMenu.openHelpAndSupport": "Abrir Ayuda y documentación", "desktopTopbar.helpMenu.recordingNetLog": "Grabando registro de red...", "desktopTopbar.helpMenu.recordNetLog": "Grabar registro de red...", "desktopTopbar.helpMenu.recordNetLogConfirmation": "Se está grabando un registro de red en tu carpeta Descargas. Para detener el proceso, haz clic en el botón “Detener grabación del registro de red” en el menú de Solución de problemas o sal de la aplicación.", "desktopTopbar.helpMenu.recordNetLogFailed": "Se produjo un error al grabar un registro de red. Vuelve a intentarlo o revisa los registros para obtener más información.", "desktopTopbar.helpMenu.recordNetLogFailedMessage": "Vuelve a intentarlo o revisa los registros para obtener más información. Error:", "desktopTopbar.helpMenu.recordNetLogFailedTitle": "Se produjo un error al grabar un registro de red", "desktopTopbar.helpMenu.recordNetLogStop": "Detener la grabación del registro de red...", "desktopTopbar.helpMenu.recordPerformanceTrace": "Grabar registro de rendimiento…", "desktopTopbar.helpMenu.recordPerformanceTraceConfirm": "¿Quieres grabar un registro del rendimiento de los próximos 30 segundos? Una vez que esté listo, se guardará en tu carpeta de Descargas.", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmCancel": "<PERSON><PERSON><PERSON>", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmOk": "Grabar registro de rendimiento", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmTitle": "¿Quieres grabar un registro de rendimiento?", "desktopTopbar.helpMenu.resetAndEraseAllLocalData": "Restablecer y borrar todos los datos locales", "desktopTopbar.helpMenu.showLogsInExplorer": "Mostrar registros en Explorer", "desktopTopbar.helpMenu.showLogsInFinder": "Mostrar registros en Finder", "desktopTopbar.helpMenu.title": "<PERSON><PERSON><PERSON>", "desktopTopbar.historyMenu.historyBack": "Atrás", "desktopTopbar.historyMenu.historyForward": "Adelante", "desktopTopbar.historyMenu.title": "Historial", "desktopTopbar.toggleDevTools": "Activar/desactivar herramientas de desarrollo", "desktopTopbar.toggleWindowDevTools": "Mostrar/Ocultar herramientas de desarrollo de Windows", "desktopTopbar.troubleshootingMenu.title": "Solución de problemas", "desktopTopbar.viewMenu.actualSize": "Tamaño original", "desktopTopbar.viewMenu.forceReload": "<PERSON><PERSON> recarga", "desktopTopbar.viewMenu.forceReloadDialog.cancel": "<PERSON><PERSON><PERSON>", "desktopTopbar.viewMenu.forceReloadDialog.message": "Actualmente, no estás en línea. Si fuerzas la recarga de esta página, perderás el acceso a ella hasta que vuelvas a conectarte.", "desktopTopbar.viewMenu.forceReloadDialog.ok": "<PERSON><PERSON><PERSON> de todos modos", "desktopTopbar.viewMenu.forceReloadDialog.title": "¿Confirmas que quieres forzar la recarga?", "desktopTopbar.viewMenu.reload": "Recargar", "desktopTopbar.viewMenu.showHideSidebar": "Mostrar/Ocultar la barra lateral", "desktopTopbar.viewMenu.showHideTabSpaceButton": "Mostrar/ocultar grupos de pestañas", "desktopTopbar.viewMenu.title": "<PERSON>er", "desktopTopbar.viewMenu.togglefullscreen": "Mostrar/ocultar pantalla completa", "desktopTopbar.viewMenu.zoomIn": "Acercar", "desktopTopbar.viewMenu.zoomOut": "<PERSON><PERSON><PERSON>", "desktopTopbar.whatsNewMac.title": "Descubre las novedades de Notion para macOS", "desktopTopbar.whatsNewWindows.title": "Descubre las novedades de Notion para Windows", "desktopTopbar.windowMenu.close": "<PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.front": "Primer plano", "desktopTopbar.windowMenu.maximize": "Maximizar", "desktopTopbar.windowMenu.minimize": "<PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.showNextTab": "Mostrar pestaña siguiente", "desktopTopbar.windowMenu.showPreviousTab": "Mostrar pestaña anterior", "desktopTopbar.windowMenu.title": "Ventana", "desktopTopbar.windowMenu.zoom": "Zoom", "desktopTroubleshooting.resetData.cancel": "<PERSON><PERSON><PERSON>", "desktopTroubleshooting.resetData.closingWindows": "Cerrando las ventanas de Notion", "desktopTroubleshooting.resetData.deletingFiles": "Eliminando archivos", "desktopTroubleshooting.resetData.done": "Listo", "desktopTroubleshooting.resetData.doneMessage": "Se restableció la aplicación.", "desktopTroubleshooting.resetData.failed": "El proceso de recuperación no pudo eliminar algunos archivos. Sentimos las molestias. Dirígete a https://www.notion.so/help para obtener ayuda. Para forzar el restablecimiento de la aplicación manualmente, cierra Notion por completo. A continuación, elimina la siguiente ruta: {userDataPath}", "desktopTroubleshooting.resetData.message": "Esta acción eliminará todos los datos locales e internos, incluidas la caché y la configuración local, y restaurará la aplicación de Notion a su estado inicial. También forzará el cierre de sesión. Tus páginas y el resto del contenido de la aplicación no se verán afectados. ¿Quieres continuar?", "desktopTroubleshooting.resetData.reset": "Restablecer todos los datos locales", "desktopTroubleshooting.resetData.restart": "Reiniciar", "desktopTroubleshooting.resetData.title": "Restablecer y borrar todos los datos locales", "desktopTroubleshooting.showLogs.error.message.mac": "Notion encontró un error al intentar mostrar los registros en Finder:", "desktopTroubleshooting.showLogs.error.message.windows": "Notion encontró un error al intentar mostrar los registros en Explorer:", "desktopTroubleshooting.showLogs.error.title": "Se produjo un error al intentar mostrar los registros", "desktopTroubleshooting.startRecordingNetLog": "Iniciar la grabación del registro de red", "desktopTroubleshooting.stopRecordingNetLog": "Detener la grabación del registro de red", "menuBarIcon.menu.changeCommandSearchAndNotionAiShortcut": "<PERSON><PERSON>", "menuBarIcon.menu.changeCommandSearchShortcut": "Cambiar atajo del Atajo de búsqueda", "menuBarIcon.menu.enableQuickSearch": "Activar b<PERSON>que<PERSON> rá<PERSON>a", "menuBarIcon.menu.keepInBackground": "Dejar en segundo plano", "menuBarIcon.menu.launchPreferences": "Preferencias de inicio", "menuBarIcon.menu.openOnLogin": "Abrir Notion al iniciar sesión", "menuBarIcon.menu.quitNotion": "Salir de Notion", "menuBarIcon.menu.showImmediately": "Mostrar de inmediato", "menuBarIcon.menu.showNotionInMenuBar": "Mostrar Notion en la barra de menú", "menuBarIcon.menu.toggleCommandSearch": "Activar/desactivar atajo de búsqueda", "menuBarIcon.menu.toggleNotionAi": "Activar o desactivar la IA de Notion", "openAtLogin.dialog.detail": "{operatingSystem} impidió que Notion configurara la opción “Abrir al iniciar sesión”. Esto puede ocurrir cuando el inicio de Notion ya está configurado en los ajustes del sistema o si no cuentas con los permisos necesarios. Puedes modificar esta opción de forma manual desde la configuración del sistema.", "openAtLogin.dialog.title": "Abrir al iniciar sesión", "tabSpaces.deleteDialog.cancelButton": "<PERSON><PERSON><PERSON>", "tabSpaces.deleteDialog.deleteButton": "Eliminar", "tabSpaces.deleteDialog.detail": "Se desagruparán todas las pestañas de este grupo de pestañas.", "tabSpaces.deleteDialog.title": "¿Quieres eliminar el grupo de pestañas \"{title}\"?", "tabSpaces.snackbar.switchedToTabGroup": "Se cambió a {title}", "tabSpaces.snackbar.switchedToUngroupedTabs": "Se cambió a pestañas no agrupadas", "tabSpaces.snackbar.tabGroupPlaceholder": "Grupo de pestañas", "updatePrompt.detail": "¿Te gustaría instalar la actualización ahora? Restauraremos las ventanas y pestañas que tenías abiertas.", "updatePrompt.installAndRelaunch": "Instalar y reabrir", "updatePrompt.message": "¡Hay una nueva versión de Notion disponible!", "updatePrompt.remindMeLater": "Recordármelo más tarde", "window.closeDialog.cancelButton": "<PERSON><PERSON><PERSON>", "window.closeDialog.confirmButton": "<PERSON><PERSON><PERSON>", "window.closeDialog.title.app": "¿Quieres cerrar <PERSON>ion?", "window.closeDialog.title.tab": "¿Quieres cerrar la pestaña de Notion?", "window.closeDialog.title.window": "¿Quieres cerrar la ventana de Notion?", "window.loadingError.message": "<PERSON>rror al cargar Notion; conéctate a Internet para empezar.", "window.loadingError.reload": "Recargar", "window.movedTabSnackbarMessage": "Se movió {tabTitle} a {tabSpaceTitle}", "window.tabLoadingError.cancel": "<PERSON><PERSON><PERSON>", "window.tabMenu.closeOtherTabs": "Cerrar las demás pestañas", "window.tabMenu.closeTab": "<PERSON><PERSON>r pesta<PERSON>", "window.tabMenu.closeTabsToLeft": "Cerrar pestañas a la izquierda", "window.tabMenu.closeTabsToRight": "Cerrar pestañas a la derecha", "window.tabMenu.copyLink": "<PERSON><PERSON><PERSON> enlace", "window.tabMenu.duplicateTab": "Duplicar pestaña", "window.tabMenu.moveTo": "Mover a", "window.tabMenu.moveToNewWindow": "Mover la pestaña a una nueva ventana", "window.tabMenu.moveToSubmenuNewWindow": "Nueva ventana", "window.tabMenu.pinTab": "<PERSON>jar pesta<PERSON>", "window.tabMenu.refresh": "Actualizar pestaña", "window.tabMenu.reopenClosedTab": "Reabrir la última pestaña cerrada", "window.tabMenu.replacePinnedTabUrl": "Reemplazar la URL fijada por la actual", "window.tabMenu.returnToPinnedTabUrl": "Volver a la URL fijada", "window.tabMenu.ungroupTab": "Desagrupar pestaña", "window.tabMenu.unpinTab": "<PERSON><PERSON><PERSON> pesta<PERSON>", "window.tabTitlePlaceholder": "Pestaña", "window.ungroupedTabSnackbarMessage": "{tabTitle} sin agrupar"}