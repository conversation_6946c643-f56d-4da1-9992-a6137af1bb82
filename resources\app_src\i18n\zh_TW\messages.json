{"activityMonitor.copyDiagnosticInformation": "複製診斷資訊", "activityMonitor.copyExecutablePath": "複製可執行路徑", "activityMonitor.copyUrl": "複製網址", "activityMonitor.forceKillProcess": "強制終止程序", "activityMonitor.inspectActivityMonitor": "檢查活動監視器", "activityMonitor.killProcess": "關閉程序", "activityMonitor.openDevTools": "開啟 DevTools", "activityMonitor.reload": "重新載入", "clientPlaceholder.placeholderDescription": "點擊以在此視窗中顯示", "clientPlaceholder.placeholderTitle": "目前正在另一個視窗中檢視內容", "commandSearch.window.title": "Notion - 快速搜尋", "crashWatchdog.dialog.abnormalExit": "異常結束：此頁面以非零結束代碼關閉。", "crashWatchdog.dialog.buttonCloseTab": "關閉分頁", "crashWatchdog.dialog.buttonCloseWindow": "關閉視窗", "crashWatchdog.dialog.buttonRestartApp": "重新啟動 Notion", "crashWatchdog.dialog.crashed": "當機：此頁面因不明原因當機。", "crashWatchdog.dialog.details": "頁面網址：{url}。原因：{reason}。結束代碼：{exitCode}", "crashWatchdog.dialog.integrityFailure": "完整性錯誤：此頁面未通過代碼完整性檢查。", "crashWatchdog.dialog.killed": "已終止：外部程序終止了此頁面。", "crashWatchdog.dialog.launchFailed": "啟動失敗：程序啟動失敗。", "crashWatchdog.dialog.message": "顯示此分頁時發生問題，而且我們無法自動復原。", "crashWatchdog.dialog.oom": "OOM：此頁面因記憶體不足而當機。", "crashWatchdog.dialog.title": "發生問題", "crashWatchdog.dialog.urlUnknown": "未知", "desktop.activityMonitor.all": "全部", "desktop.activityMonitor.cpuPercent": "% CPU", "desktop.activityMonitor.cpuPercentDescription": "程序在 CPU 中所占用時間的比例。", "desktop.activityMonitor.cpuTime": "CPU 時間", "desktop.activityMonitor.cpuTimeDescription": "程序啟動後使用 CPU 時間的總秒數。", "desktop.activityMonitor.creationTime": "建立時間", "desktop.activityMonitor.creationTimeDescription": "程序建立後所經過的時間。", "desktop.activityMonitor.frames": "框架", "desktop.activityMonitor.framesDescription": "此程序管理的框架。許多轉譯器程序處理多個框架。", "desktop.activityMonitor.hidden": "已隱藏", "desktop.activityMonitor.hideColumns": "隱藏欄", "desktop.activityMonitor.hideFilters": "隱藏篩選條件", "desktop.activityMonitor.idleWakeupsPerSecond": "閒置喚醒", "desktop.activityMonitor.idleWakeupsPerSecondDescription": "自上次活動監視器更新後，程序喚醒 CPU 的次數。", "desktop.activityMonitor.loading": "正在載入活動資料", "desktop.activityMonitor.memCurrent": "記憶體（目前）", "desktop.activityMonitor.memCurrentDescription": "程序的「工作集」大小是目前駐留在 RAM 中的頁面集。這個數字不包含作業系統的記憶體壓縮、未啟用和快取頁面的管理，或其他記憶體管理技術。程序使用的實體記憶體可能小得多。", "desktop.activityMonitor.memPeak": "記憶體（尖峰值）", "desktop.activityMonitor.memPeakDescription": "程序的尖峰工作集大小是啟動後使用的最大實體記憶體量。程序的「工作集」大小是目前駐留在 RAM 中的頁面集。這個數字不包含作業系統的記憶體壓縮、未啟用和快取頁面的管理，或其他記憶體管理技術。程序使用的實體記憶體可能小得多。", "desktop.activityMonitor.memPrivate": "記憶體（私人）", "desktop.activityMonitor.memPrivateDescription": "此程序分配到的實體記憶體，且未與其他程序共用，例如 JS 堆積或 HTML 內容。", "desktop.activityMonitor.memShared": "記憶體（共用）", "desktop.activityMonitor.memSharedDescription": "此程序分配到的實體記憶體，且與其他程序共用，例如共用程式庫或對應檔。", "desktop.activityMonitor.mixed": "混合", "desktop.activityMonitor.parentWindowId": "上層視窗 ID", "desktop.activityMonitor.parentWindowIdDescription": "包含此分頁的視窗專屬識別碼。", "desktop.activityMonitor.pid": "PID", "desktop.activityMonitor.pidDescription": "作業系統使用的程序 ID。", "desktop.activityMonitor.processName": "程序名稱", "desktop.activityMonitor.showColumns": "顯示欄", "desktop.activityMonitor.showFilters": "顯示篩選條件", "desktop.activityMonitor.tabId": "分頁 ID", "desktop.activityMonitor.tabIdDescription": "Notion 應用程式中此分頁的專屬識別碼。", "desktop.activityMonitor.type": "類型", "desktop.activityMonitor.url": "URL", "desktop.activityMonitor.urlDescription": "程序網址。許多轉譯器程序處理多個框架，請參閱框架欄了解詳情。", "desktop.activityMonitor.visibilityState": "可見度", "desktop.activityMonitor.visibilityStateDescription": "程序的可見度狀態。如果是轉譯器程序，這將是主畫面的可見度狀態。", "desktop.activityMonitor.visible": "可見", "desktop.tabBar.backButtonLabel": "返回", "desktop.tabBar.closeSidebarLabel": "關閉側邊欄", "desktop.tabBar.closeTabLabel": "關閉標籤頁 {tabTitle}", "desktop.tabBar.forwardButtonLabel": "前進", "desktop.tabBar.newTabButtonLabel": "新標籤頁", "desktop.tabBar.openSidebarLabel": "開啟側邊欄", "desktop.tabBar.tabSpacesLabel": "分頁空間", "desktopExtensions.install.failed.title": "安裝擴充功能失敗", "desktopExtensions.manage.cancel": "取消", "desktopExtensions.manage.disable": "停用", "desktopExtensions.manage.enable": "啟用", "desktopExtensions.manage.message": "你使用 {extensionTitle} {extensionVersion} 的用途是？", "desktopExtensions.manage.title": "管理擴充功能", "desktopExtensions.manage.uninstall": "解除安裝", "desktopExtensions.manage.unload": "解除安裝", "desktopExtensions.openFailed.noPopupMessage": "此擴充功能未指定彈出式視窗（actiondefault_popup）", "desktopExtensions.openFailed.noPopupTitle": "無法打開對話框功能", "desktopExtensions.unzip.failed.badFileRead": "讀取 CRX 檔案失敗：{error}", "desktopExtensions.unzip.failed.badFileWrite": "無法寫入檔案 {filePath}：{error}", "desktopExtensions.unzip.failed.badFolderCreate": "無法在 {extensionPath} 建立擴充資料夾：{error}", "desktopExtensions.unzip.failed.badManifest": "無法解析此擴充功能中的 manifest.json。這是否是有效的擴充功能？錯誤為：{error}", "desktopExtensions.unzip.failed.badManifestNoName": "在 manifest.json 中找不到名稱", "desktopExtensions.unzip.failed.error": "無法解壓縮 CRX 檔案：{error}", "desktopExtensions.unzip.failed.noManifest": "在 CRX 中找不到 manifest.json。這是否是有效的擴充功能？", "desktopInstaller.failedToMove.detail": "將應用程式移至你的應用程式資料夾失敗。請手動將其移動。", "desktopInstaller.failedToMove.title": "無法移動應用程式", "desktopInstaller.invalidInstallDialog.cancelButton.label": "取消", "desktopInstaller.invalidInstallDialog.confirmMove": "Notion 應用程式並未正確安裝。我們可以將你的 Notion 移入應用程式資料夾內嗎？", "desktopInstaller.invalidInstallDialog.okButton.label": "好的", "desktopInstaller.invalidInstallDialog.title": "安裝無效", "desktopTopbar.appMenu.about": "關於 Notion", "desktopTopbar.appMenu.checkForUpdate": "檢查更新……", "desktopTopbar.appMenu.checkForUpdate.noUpdateAvailable": "Notion 已為最新版本！", "desktopTopbar.appMenu.checkForUpdate.title": "檢查更新", "desktopTopbar.appMenu.checkForUpdate.updateAvailable": "新版 Notion 現已推出，目前正在背景下載中。感謝你使用最新版本！", "desktopTopbar.appMenu.checkForUpdate.updateCheckFailed": "Notion 無法與更新的伺服器連接，原因在於你的網路連接，或者更新使用的伺服器本身出現問題。請稍後再試一次。", "desktopTopbar.appMenu.downloadingUpdate": "正在下載更新 （{percentage}%）", "desktopTopbar.appMenu.hide": "隱藏 Notion", "desktopTopbar.appMenu.hideOthers": "隱藏其他", "desktopTopbar.appMenu.preferences": "偏好設定", "desktopTopbar.appMenu.quit": "離開", "desktopTopbar.appMenu.quitWithoutSavingTabs": "退出但不儲存標籤頁", "desktopTopbar.appMenu.restartToApplyUpdate": "重新啟動以套用更新", "desktopTopbar.appMenu.services": "服務", "desktopTopbar.appMenu.unhide": "顯示全部", "desktopTopbar.editMenu.copy": "複製", "desktopTopbar.editMenu.copyLinkToCurrentPage": "將連結複製到目前頁面", "desktopTopbar.editMenu.copyLinkToCurrentPageBlockTitle": "複製目前頁面的名稱", "desktopTopbar.editMenu.cut": "剪下", "desktopTopbar.editMenu.paste": "貼上", "desktopTopbar.editMenu.pasteAndMatchStyle": "貼上並符合風格", "desktopTopbar.editMenu.redo": "重做", "desktopTopbar.editMenu.selectAll": "全選", "desktopTopbar.editMenu.speech": "語音", "desktopTopbar.editMenu.speech.startSpeaking": "開始說話", "desktopTopbar.editMenu.speech.stopSpeaking": "停止說話", "desktopTopbar.editMenu.title": "編輯", "desktopTopbar.editMenu.undo": "還原", "desktopTopbar.extensionsMenu.install": "安裝擴充功能...", "desktopTopbar.extensionsMenu.manage": "管理擴充功能", "desktopTopbar.fileMenu.close": "關閉視窗", "desktopTopbar.fileMenu.closeTab": "關閉標籤頁", "desktopTopbar.fileMenu.newNotionWindow": "新 Notion 視窗", "desktopTopbar.fileMenu.newTab": "新標籤頁", "desktopTopbar.fileMenu.newWindow": "新視窗", "desktopTopbar.fileMenu.print": "列印…", "desktopTopbar.fileMenu.quit": "退出", "desktopTopbar.fileMenu.quitWithoutSavingTabs": "退出但不儲存標籤頁", "desktopTopbar.fileMenu.reopenClosedTab": "重新開啟最後關閉的標籤頁", "desktopTopbar.fileMenu.title": "檔案", "desktopTopbar.helpMenu.copyInstallId": "複製安裝 ID", "desktopTopbar.helpMenu.disableAdvancedLogging": "停用進階記錄檔並重新啟動", "desktopTopbar.helpMenu.disableDebugLogging": "停用進階登錄並重新啟動", "desktopTopbar.helpMenu.disableHardwareAcceleration": "停用硬體加速並重新啟動", "desktopTopbar.helpMenu.enableAdvancedLogging": "啟用進階記錄檔並重新啟動", "desktopTopbar.helpMenu.enableDebugLogging": "啟用進階登錄並重新啟動", "desktopTopbar.helpMenu.enableHardwareAcceleration": "啟用硬體加速並重新啟動", "desktopTopbar.helpMenu.openActivityMonitor": "開啟活動監視器", "desktopTopbar.helpMenu.openConsole": "開啟控制台", "desktopTopbar.helpMenu.openHelpAndSupport": "開啟幫助文件", "desktopTopbar.helpMenu.recordingNetLog": "正在記錄網路紀錄…", "desktopTopbar.helpMenu.recordNetLog": "記錄網路紀錄…", "desktopTopbar.helpMenu.recordNetLogConfirmation": "系統現在正記錄你的網路紀錄，並會儲存在「下載」資料夾。若要停止記錄，請在「疑難排解」選單點擊「停止記錄網路紀錄」按鈕，或退出應用程式。", "desktopTopbar.helpMenu.recordNetLogFailed": "記錄網路紀錄失敗。請重新嘗試，或檢視紀錄了解詳情。", "desktopTopbar.helpMenu.recordNetLogFailedMessage": "請重新嘗試，或檢視紀錄了解詳情。錯誤原因是：", "desktopTopbar.helpMenu.recordNetLogFailedTitle": "記錄網路紀錄失敗", "desktopTopbar.helpMenu.recordNetLogStop": "停止記錄網路紀錄…", "desktopTopbar.helpMenu.recordPerformanceTrace": "記錄性能追蹤", "desktopTopbar.helpMenu.recordPerformanceTraceConfirm": "是否要記錄接下來 30 秒的性能追蹤？完成後，該記錄即會保存在你的「下載」資料夾。", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmCancel": "取消", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmOk": "記錄性能追蹤", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmTitle": "是否記錄性能追蹤？", "desktopTopbar.helpMenu.resetAndEraseAllLocalData": "重設並清除所有本機資料", "desktopTopbar.helpMenu.showLogsInExplorer": "在 Explorer 中顯示日誌檔", "desktopTopbar.helpMenu.showLogsInFinder": "在 Finder 中顯示日誌檔", "desktopTopbar.helpMenu.title": "說明", "desktopTopbar.historyMenu.historyBack": "返回", "desktopTopbar.historyMenu.historyForward": "前進", "desktopTopbar.historyMenu.title": "歷史", "desktopTopbar.toggleDevTools": "切換開發者工具", "desktopTopbar.toggleWindowDevTools": "切換 Windows 開發人員工具", "desktopTopbar.troubleshootingMenu.title": "疑難排解", "desktopTopbar.viewMenu.actualSize": "實際大小", "desktopTopbar.viewMenu.forceReload": "強制重新載入", "desktopTopbar.viewMenu.forceReloadDialog.cancel": "取消", "desktopTopbar.viewMenu.forceReloadDialog.message": "你目前處於離線狀態。若強制重新載入此頁面，在你重新上線前都將無法存取頁面。", "desktopTopbar.viewMenu.forceReloadDialog.ok": "仍要重新載入", "desktopTopbar.viewMenu.forceReloadDialog.title": "確定要強制重新載入嗎？", "desktopTopbar.viewMenu.reload": "重新載入", "desktopTopbar.viewMenu.showHideSidebar": "顯示/隱藏側邊欄", "desktopTopbar.viewMenu.showHideTabSpaceButton": "顯示/隱藏分頁群組", "desktopTopbar.viewMenu.title": "瀏覽模式", "desktopTopbar.viewMenu.togglefullscreen": "切換全螢幕", "desktopTopbar.viewMenu.zoomIn": "放大", "desktopTopbar.viewMenu.zoomOut": "縮小", "desktopTopbar.whatsNewMac.title": "在 macOS 版 Notion 中打開最新消息", "desktopTopbar.whatsNewWindows.title": "在 Windows 版 Notion 中打開最新消息", "desktopTopbar.windowMenu.close": "關閉", "desktopTopbar.windowMenu.front": "前面", "desktopTopbar.windowMenu.maximize": "最大化", "desktopTopbar.windowMenu.minimize": "最小化", "desktopTopbar.windowMenu.showNextTab": "顯示下一個標籤頁", "desktopTopbar.windowMenu.showPreviousTab": "顯示上一個標籤頁", "desktopTopbar.windowMenu.title": "視窗", "desktopTopbar.windowMenu.zoom": "Zoom", "desktopTroubleshooting.resetData.cancel": "取消", "desktopTroubleshooting.resetData.closingWindows": "正在關閉 Notion 視窗", "desktopTroubleshooting.resetData.deletingFiles": "正在刪除檔案", "desktopTroubleshooting.resetData.done": "完成", "desktopTroubleshooting.resetData.doneMessage": "此應用程式已重設。", "desktopTroubleshooting.resetData.failed": "我們的復原流程無法刪除部分檔案。很抱歉造成困擾，請造訪 https://www.notion.so/help尋求協助。若要手動強制完整重設應用程式，請完全關閉 Notion。然後刪除以下路徑：{userDataPath}", "desktopTroubleshooting.resetData.message": "此操作將刪除所有本機和內部資料，包含快取和本機設定，將 Notion 應用程式還原至全新安裝的狀態。此操作也會將你從 Notion 登出。你的頁面和其他應用程式中的內容將不受影響。確定要繼續嗎？", "desktopTroubleshooting.resetData.reset": "重設所有本機資料", "desktopTroubleshooting.resetData.restart": "重新啟動", "desktopTroubleshooting.resetData.title": "重設並清除所有本機資料", "desktopTroubleshooting.showLogs.error.message.mac": "Notion 嘗試在 Finder 中顯示日誌檔時發生錯誤：", "desktopTroubleshooting.showLogs.error.message.windows": "Notion 嘗試在 Explorer 中顯示日誌檔時發生錯誤：", "desktopTroubleshooting.showLogs.error.title": "顯示日誌檔失敗", "desktopTroubleshooting.startRecordingNetLog": "開始記錄網路紀錄", "desktopTroubleshooting.stopRecordingNetLog": "停止記錄網路紀錄", "menuBarIcon.menu.changeCommandSearchAndNotionAiShortcut": "編輯快速鍵", "menuBarIcon.menu.changeCommandSearchShortcut": "更改搜尋快速鍵", "menuBarIcon.menu.enableQuickSearch": "啟用快速搜尋", "menuBarIcon.menu.keepInBackground": "保持在背景", "menuBarIcon.menu.launchPreferences": "啟動偏好設定", "menuBarIcon.menu.openOnLogin": "登入時啟動 Notion", "menuBarIcon.menu.quitNotion": "退出 Notion", "menuBarIcon.menu.showImmediately": "立即顯示", "menuBarIcon.menu.showNotionInMenuBar": "在選單列中顯示 Notion", "menuBarIcon.menu.toggleCommandSearch": "切換快速搜尋", "menuBarIcon.menu.toggleNotionAi": "切換 Notion AI", "openAtLogin.dialog.detail": "{operatingSystem} 不允許 Notion 變更「登入時啟動」設定。這種情況通常發生在系統設定中已有 Notion 啟動相關設定，或是你沒有足夠權限。你仍可在系統設定中手動變更這項設定。", "openAtLogin.dialog.title": "登入時啟動", "tabSpaces.deleteDialog.cancelButton": "取消", "tabSpaces.deleteDialog.deleteButton": "刪除", "tabSpaces.deleteDialog.detail": "此分頁群組中的所有分頁將取消分組。", "tabSpaces.deleteDialog.title": "要刪除「{title}」分頁群組嗎？", "tabSpaces.snackbar.switchedToTabGroup": "已切換至{title}", "tabSpaces.snackbar.switchedToUngroupedTabs": "已切換至未分組的分頁", "tabSpaces.snackbar.tabGroupPlaceholder": "分頁群組", "updatePrompt.detail": "想要現在安裝嗎？系統將重新開啟視窗與分頁。", "updatePrompt.installAndRelaunch": "安裝並重新啟動", "updatePrompt.message": "Notion 推出新版本了！", "updatePrompt.remindMeLater": "稍後提醒我", "window.closeDialog.cancelButton": "取消", "window.closeDialog.confirmButton": "關閉", "window.closeDialog.title.app": "關閉 Notion？", "window.closeDialog.title.tab": "關閉 Notion 分頁？", "window.closeDialog.title.window": "關閉 Notion 視窗？", "window.loadingError.message": "載入 Notion 時出現錯誤，連接到網路即可使用。", "window.loadingError.reload": "重新載入", "window.movedTabSnackbarMessage": "已將{tabTitle}移至{tabSpaceTitle}", "window.tabLoadingError.cancel": "取消", "window.tabMenu.closeOtherTabs": "關閉其他標籤頁", "window.tabMenu.closeTab": "關閉標籤頁", "window.tabMenu.closeTabsToLeft": "關閉左側標籤頁", "window.tabMenu.closeTabsToRight": "關閉右側標籤頁", "window.tabMenu.copyLink": "複製連結", "window.tabMenu.duplicateTab": "複製標籤頁", "window.tabMenu.moveTo": "移動到", "window.tabMenu.moveToNewWindow": "將標籤頁移至新視窗", "window.tabMenu.moveToSubmenuNewWindow": "新視窗", "window.tabMenu.pinTab": "釘選標籤頁", "window.tabMenu.refresh": "重新整理標籤頁", "window.tabMenu.reopenClosedTab": "重新開啟最後關閉的標籤頁", "window.tabMenu.replacePinnedTabUrl": "使用目前的 URL 取代置頂 URL", "window.tabMenu.returnToPinnedTabUrl": "回到釘選網址", "window.tabMenu.ungroupTab": "取消分組分頁", "window.tabMenu.unpinTab": "取消釘選標籤頁", "window.tabTitlePlaceholder": "分頁", "window.ungroupedTabSnackbarMessage": "已取消分組{tabTitle}"}