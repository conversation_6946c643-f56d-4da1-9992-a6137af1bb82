{"activityMonitor.copyDiagnosticInformation": "Copier les informations de diagnostic", "activityMonitor.copyExecutablePath": "<PERSON><PERSON><PERSON> le chemin d’accès à l’exécutable", "activityMonitor.copyUrl": "Copier l’URL", "activityMonitor.forceKillProcess": "Forcer l’arrêt du processus", "activityMonitor.inspectActivityMonitor": "Inspecter le moniteur d’activité", "activityMonitor.killProcess": "<PERSON><PERSON><PERSON><PERSON>", "activityMonitor.openDevTools": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activityMonitor.reload": "Recharger", "clientPlaceholder.placeholderDescription": "Cliquez pour afficher dans cette fenêtre", "clientPlaceholder.placeholderTitle": "Affichage actuel du contenu dans une autre fenêtre", "commandSearch.window.title": "Notion - Recherche rapide", "crashWatchdog.dialog.abnormalExit": "Fermeture anormale : la page s’est fermée avec un code de sortie non nul.", "crashWatchdog.dialog.buttonCloseTab": "<PERSON><PERSON><PERSON> l<PERSON>on<PERSON>t", "crashWatchdog.dialog.buttonCloseWindow": "<PERSON><PERSON><PERSON> la fenêtre", "crashWatchdog.dialog.buttonRestartApp": "Redémarrer Notion", "crashWatchdog.dialog.crashed": "Crash : la page a planté pour une raison inconnue.", "crashWatchdog.dialog.details": "URL de la page : {url} Motif : {reason} Code de sortie : {exitCode}", "crashWatchdog.dialog.integrityFailure": "Échec de l’intégrité : la page n’a pas pu vérifier l’intégrité du code.", "crashWatchdog.dialog.killed": "Interruption : la page a été interrompue par un processus externe.", "crashWatchdog.dialog.launchFailed": "Échec du lancement : le processus ne s’est pas lancé.", "crashWatchdog.dialog.message": "Un problème est survenu lors de l’affichage de cet onglet et nous n’avons pas pu le récupérer automatiquement.", "crashWatchdog.dialog.oom": "Manque de mémoire : la page a manqué de mémoire et a planté.", "crashWatchdog.dialog.title": "Une erreur s’est produite", "crashWatchdog.dialog.urlUnknown": "Inconnue", "desktop.activityMonitor.all": "Tous", "desktop.activityMonitor.cpuPercent": "% du processeur", "desktop.activityMonitor.cpuPercentDescription": "La fraction du temps du processeur disponible utilisée par le processus.", "desktop.activityMonitor.cpuTime": "Temps du processeur", "desktop.activityMonitor.cpuTimeDescription": "Le nombre total de secondes de temps du processeur utilisées depuis le démarrage du processus.", "desktop.activityMonitor.creationTime": "Date de création", "desktop.activityMonitor.creationTimeDescription": "Le temps écoulé depuis la création du processus.", "desktop.activityMonitor.frames": "Cadres", "desktop.activityMonitor.framesDescription": "Le nombre de cadres gérés par le processus. De nombreux processus de rendu sont responsables de plusieurs cadres.", "desktop.activityMonitor.hidden": "<PERSON><PERSON><PERSON><PERSON>", "desktop.activityMonitor.hideColumns": "Masquer les colonnes", "desktop.activityMonitor.hideFilters": "Masquer les filtres", "desktop.activityMonitor.idleWakeupsPerSecond": "Réveils du processeur en mode veille", "desktop.activityMonitor.idleWakeupsPerSecondDescription": "Le nombre de fois que le processus a réveillé le processeur depuis la dernière mise à jour du moniteur d’activité.", "desktop.activityMonitor.loading": "Chargement des données d’activité", "desktop.activityMonitor.memCurrent": "Mé<PERSON><PERSON> (actuelle)", "desktop.activityMonitor.memCurrentDescription": "La taille de l’« ensemble de travail » du processus, qui est l’ensemble des pages résidant actuellement dans la mémoire vive. Ce nombre ne tient pas compte de la compression de la mémoire du système d’exploitation, de la gestion des pages inactives et mises en cache ou d’autres techniques de gestion de la mémoire. La quantité de mémoire physique utilisée par le processus est probablement beaucoup plus petite.", "desktop.activityMonitor.memPeak": "Mé<PERSON><PERSON> (pointe)", "desktop.activityMonitor.memPeakDescription": "La taille maximale de l’ensemble de travail du processus, qui est la quantité maximale de mémoire physique utilisée par le processus depuis son lancement. La taille de l’« ensemble de travail » du processus, qui est l’ensemble des pages résidant actuellement dans la mémoire vive. Ce nombre ne tient pas compte de la compression de la mémoire du système d’exploitation, de la gestion des pages inactives et mises en cache ou d’autres techniques de gestion de la mémoire. La quantité de mémoire physique utilisée par le processus est probablement beaucoup plus petite.", "desktop.activityMonitor.memPrivate": "Mémoire (privée)", "desktop.activityMonitor.memPrivateDescription": "La quantité de mémoire physique allouée par le processus qui n’est pas partagée avec d’autres processus, tels que le tas JS ou le contenu HTML.", "desktop.activityMonitor.memShared": "<PERSON><PERSON><PERSON><PERSON> (partagée)", "desktop.activityMonitor.memSharedDescription": "La quantité de mémoire physique allouée par le processus qui est partagée avec d’autres processus, tels que les bibliothèques partagées ou les fichiers mappés.", "desktop.activityMonitor.mixed": "Mixte", "desktop.activityMonitor.parentWindowId": "Identifiant de la fenêtre parent", "desktop.activityMonitor.parentWindowIdDescription": "L’identifiant unique de la fenêtre contenant cet onglet.", "desktop.activityMonitor.pid": "PID", "desktop.activityMonitor.pidDescription": "L’identifiant du processus tel qu’utilisé par le système d’exploitation.", "desktop.activityMonitor.processName": "Nom du processus", "desktop.activityMonitor.showColumns": "Afficher les colonnes", "desktop.activityMonitor.showFilters": "Afficher les filtres", "desktop.activityMonitor.tabId": "Identifiant de l’onglet", "desktop.activityMonitor.tabIdDescription": "L’identifiant unique de l’onglet dans l’application Notion.", "desktop.activityMonitor.type": "Type", "desktop.activityMonitor.url": "URL", "desktop.activityMonitor.urlDescription": "L’URL du processus. De nombreux processus de rendu sont responsables de plusieurs cadres ; consultez la colonne Cadres pour plus d’informations.", "desktop.activityMonitor.visibilityState": "Visibilité", "desktop.activityMonitor.visibilityStateDescription": "L’état de visibilité du processus. Si le processus est un processus de rendu, ce sera l’état de visibilité du cadre principal.", "desktop.activityMonitor.visible": "Visible", "desktop.tabBar.backButtonLabel": "Précédent", "desktop.tabBar.closeSidebarLabel": "<PERSON><PERSON><PERSON> la barre latérale", "desktop.tabBar.closeTabLabel": "<PERSON><PERSON><PERSON> l<PERSON>onglet {tabTitle}", "desktop.tabBar.forwardButtonLabel": "Suivant", "desktop.tabBar.newTabButtonLabel": "Nouvel onglet", "desktop.tabBar.openSidebarLabel": "<PERSON>u<PERSON><PERSON>r la barre latérale", "desktop.tabBar.tabSpacesLabel": "Espaces d’onglets", "desktopExtensions.install.failed.title": "Impossible d’installer l’extension", "desktopExtensions.manage.cancel": "Annuler", "desktopExtensions.manage.disable": "Désactiver", "desktopExtensions.manage.enable": "Activer", "desktopExtensions.manage.message": "Que voulez-vous faire avec {extensionTitle} {extensionVersion} ?", "desktopExtensions.manage.title": "Gérer une extension", "desktopExtensions.manage.uninstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopExtensions.manage.unload": "Annuler le chargement", "desktopExtensions.openFailed.noPopupMessage": "Cette extension n’a pas spécifié de fenêtre contextuelle (action.default_popup)", "desktopExtensions.openFailed.noPopupTitle": "Échec de l’ouverture de l’extension", "desktopExtensions.unzip.failed.badFileRead": "Impossible de lire le fichier CRX : {error}", "desktopExtensions.unzip.failed.badFileWrite": "Impossible d’écrire le fichier {filePath} : {error}", "desktopExtensions.unzip.failed.badFolderCreate": "La création du dossier d’extensions sous {extensionPath} a échoué : {error}", "desktopExtensions.unzip.failed.badManifest": "Nous n’avons pas pu analyser le fichier manifest.json de cette extension. Est-ce une extension valide ? L’erreur suivante a été générée : {error}", "desktopExtensions.unzip.failed.badManifestNoName": "Aucun nom trouvé dans manifest.json", "desktopExtensions.unzip.failed.error": "Impossible d’extraire le fichier CRX : {error}", "desktopExtensions.unzip.failed.noManifest": "Aucun fichier manifest.json dans CRX. Est-ce une extension valide ?", "desktopInstaller.failedToMove.detail": "Nous n’avons pas pu déplacer l’appli vers votre dossier « Applications ». Déplacez-la manuellement.", "desktopInstaller.failedToMove.title": "Le déplacement de l’appli a échoué", "desktopInstaller.invalidInstallDialog.cancelButton.label": "Annuler", "desktopInstaller.invalidInstallDialog.confirmMove": "Votre application Notion n’est pas installée correctement. Pouvons-nous la déplacer vers votre dossier « Applications » ?", "desktopInstaller.invalidInstallDialog.okButton.label": "OK", "desktopInstaller.invalidInstallDialog.title": "Installation non valide", "desktopTopbar.appMenu.about": "À propos de Notion", "desktopTopbar.appMenu.checkForUpdate": "Recherche de mise à jour…", "desktopTopbar.appMenu.checkForUpdate.noUpdateAvailable": "Vous disposez dé<PERSON><PERSON> de la dernière version de Notion !", "desktopTopbar.appMenu.checkForUpdate.title": "Recherche de mise à jour", "desktopTopbar.appMenu.checkForUpdate.updateAvailable": "Une nouvelle version de Notion est disponible et en cours de téléchargement en arrière-plan. Merci de rester à jour !", "desktopTopbar.appMenu.checkForUpdate.updateCheckFailed": "Notion n’a pas pu établir de connexion avec le serveur de mise à jour, en raison d’un problème lié à votre connexion Internet ou au serveur de mise à jour. Réessayez plus tard.", "desktopTopbar.appMenu.downloadingUpdate": "Téléchargement de la mise à jour ({percentage} %)", "desktopTopbar.appMenu.hide": "Masquer Notion", "desktopTopbar.appMenu.hideOthers": "Masquer les autres", "desktopTopbar.appMenu.preferences": "Préférences…", "desktopTopbar.appMenu.quit": "<PERSON><PERSON><PERSON>", "desktopTopbar.appMenu.quitWithoutSavingTabs": "<PERSON><PERSON><PERSON> sans enregistrer les onglets", "desktopTopbar.appMenu.restartToApplyUpdate": "Redémarrer pour appliquer la mise à jour", "desktopTopbar.appMenu.services": "Services", "desktopTopbar.appMenu.unhide": "<PERSON><PERSON> afficher", "desktopTopbar.editMenu.copy": "<PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.copyLinkToCurrentPage": "Copier le lien vers la page actuelle", "desktopTopbar.editMenu.copyLinkToCurrentPageBlockTitle": "<PERSON><PERSON>r le nom de la page actuelle", "desktopTopbar.editMenu.cut": "Couper", "desktopTopbar.editMenu.paste": "<PERSON><PERSON>", "desktopTopbar.editMenu.pasteAndMatchStyle": "Coller et harmoniser le style", "desktopTopbar.editMenu.redo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.selectAll": "<PERSON><PERSON>", "desktopTopbar.editMenu.speech": "Voix", "desktopTopbar.editMenu.speech.startSpeaking": "Commencer à parler", "desktopTopbar.editMenu.speech.stopSpeaking": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.title": "Modifier", "desktopTopbar.editMenu.undo": "Annuler", "desktopTopbar.extensionsMenu.install": "Installer une extension…", "desktopTopbar.extensionsMenu.manage": "<PERSON><PERSON><PERSON> les extensions", "desktopTopbar.fileMenu.close": "<PERSON><PERSON><PERSON> la fenêtre", "desktopTopbar.fileMenu.closeTab": "<PERSON><PERSON><PERSON> l<PERSON>on<PERSON>t", "desktopTopbar.fileMenu.newNotionWindow": "Nouvelle fenêtre Notion", "desktopTopbar.fileMenu.newTab": "Nouvel onglet", "desktopTopbar.fileMenu.newWindow": "Nouvelle fenêtre", "desktopTopbar.fileMenu.print": "<PERSON><PERSON><PERSON><PERSON>…", "desktopTopbar.fileMenu.quit": "<PERSON><PERSON><PERSON>", "desktopTopbar.fileMenu.quitWithoutSavingTabs": "<PERSON><PERSON><PERSON> sans enregistrer les onglets", "desktopTopbar.fileMenu.reopenClosedTab": "<PERSON><PERSON><PERSON><PERSON><PERSON> le dernier onglet fermé", "desktopTopbar.fileMenu.title": "<PERSON><PERSON><PERSON>", "desktopTopbar.helpMenu.copyInstallId": "Copier l’ID d’installation", "desktopTopbar.helpMenu.disableAdvancedLogging": "Désactiver la journalisation avancée et redémarrer", "desktopTopbar.helpMenu.disableDebugLogging": "Désactiver la journalisation avancée et redémarrer", "desktopTopbar.helpMenu.disableHardwareAcceleration": "Désactiver l’accélération matérielle et redémarrer", "desktopTopbar.helpMenu.enableAdvancedLogging": "Activer la journalisation avancée et redémarrer", "desktopTopbar.helpMenu.enableDebugLogging": "Activer la journalisation avancée et redémarrer", "desktopTopbar.helpMenu.enableHardwareAcceleration": "Activer l’accélération matérielle et redémarrer", "desktopTopbar.helpMenu.openActivityMonitor": "<PERSON><PERSON><PERSON><PERSON><PERSON> le moniteur d’activité", "desktopTopbar.helpMenu.openConsole": "<PERSON><PERSON><PERSON><PERSON><PERSON> la <PERSON>", "desktopTopbar.helpMenu.openHelpAndSupport": "Ou<PERSON><PERSON>r le site d’aide et la documentation", "desktopTopbar.helpMenu.recordingNetLog": "Enregistrement du journal réseau…", "desktopTopbar.helpMenu.recordNetLog": "Enregistrer le journal réseau…", "desktopTopbar.helpMenu.recordNetLogConfirmation": "Un journal réseau est en cours d’enregistrement dans votre dossier Téléchargements. Pour arrêter l’enregistrement, cliquez sur le bouton « Arrêter l’enregistrement du journal réseau » dans le menu Dépannage, ou quittez l’application.", "desktopTopbar.helpMenu.recordNetLogFailed": "L’enregistrement du journal réseau a échoué. Réessayez ou consultez les journaux pour plus d’informations.", "desktopTopbar.helpMenu.recordNetLogFailedMessage": "R<PERSON><PERSON>ez ou consultez les journaux pour plus d’informations. L’erreur était la suivante :", "desktopTopbar.helpMenu.recordNetLogFailedTitle": "L’enregistrement du journal réseau a échoué", "desktopTopbar.helpMenu.recordNetLogStop": "Arrêter l’enregistrement du journal réseau…", "desktopTopbar.helpMenu.recordPerformanceTrace": "Enregistrer une trace de performance…", "desktopTopbar.helpMenu.recordPerformanceTraceConfirm": "Voulez-vous enregistrer une trace de performance pour les 30 prochaines secondes ? Une fois l’enregistrement terminé, il sera placé dans votre dossier Téléchargements.", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmCancel": "Annuler", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmOk": "Enregistrer une trace de performance", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmTitle": "Enregistrer une trace de performance ?", "desktopTopbar.helpMenu.resetAndEraseAllLocalData": "Réinitialiser et effacer toutes les données locales", "desktopTopbar.helpMenu.showLogsInExplorer": "Afficher les entrées du registre dans l’explorateur", "desktopTopbar.helpMenu.showLogsInFinder": "Afficher les entrées du registre dans le Finder", "desktopTopbar.helpMenu.title": "Aide", "desktopTopbar.historyMenu.historyBack": "Précédent", "desktopTopbar.historyMenu.historyForward": "Suivant", "desktopTopbar.historyMenu.title": "Historique", "desktopTopbar.toggleDevTools": "Afficher les outils de développement", "desktopTopbar.toggleWindowDevTools": "Afficher la fenêtre des outils de développement", "desktopTopbar.troubleshootingMenu.title": "Résolution de problèmes", "desktopTopbar.viewMenu.actualSize": "<PERSON><PERSON> r<PERSON>", "desktopTopbar.viewMenu.forceReload": "Forcer l’actualisation", "desktopTopbar.viewMenu.forceReloadDialog.cancel": "Annuler", "desktopTopbar.viewMenu.forceReloadDialog.message": "Vous êtes actuellement hors ligne. Le rechargement forcé de cette page vous empêchera d’y accéder jusqu’à ce que vous soyez de nouveau en ligne.", "desktopTopbar.viewMenu.forceReloadDialog.ok": "Recharger quand même", "desktopTopbar.viewMenu.forceReloadDialog.title": "Souhaitez-vous vraiment forcer le rechargement ?", "desktopTopbar.viewMenu.reload": "Recharger", "desktopTopbar.viewMenu.showHideSidebar": "Afficher/masquer la barre latérale", "desktopTopbar.viewMenu.showHideTabSpaceButton": "Afficher/masquer les groupes d’onglets", "desktopTopbar.viewMenu.title": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.viewMenu.togglefullscreen": "Basculer en plein écran", "desktopTopbar.viewMenu.zoomIn": "<PERSON>mer", "desktopTopbar.viewMenu.zoomOut": "Dézoomer", "desktopTopbar.whatsNewMac.title": "Ou<PERSON><PERSON>r les nouveautés de Notion pour macOS", "desktopTopbar.whatsNewWindows.title": "<PERSON><PERSON><PERSON><PERSON>r les nouveautés de Notion sur Windows", "desktopTopbar.windowMenu.close": "<PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.front": "Premier plan", "desktopTopbar.windowMenu.maximize": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.minimize": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.showNextTab": "Afficher l’onglet suivant", "desktopTopbar.windowMenu.showPreviousTab": "Afficher l’onglet précédent", "desktopTopbar.windowMenu.title": "<PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.zoom": "Zoom", "desktopTroubleshooting.resetData.cancel": "Annuler", "desktopTroubleshooting.resetData.closingWindows": "Fermeture des fenêtres de Notion", "desktopTroubleshooting.resetData.deletingFiles": "Suppression des fichiers", "desktopTroubleshooting.resetData.done": "<PERSON><PERSON><PERSON><PERSON>", "desktopTroubleshooting.resetData.doneMessage": "L’application a été réinitialisée.", "desktopTroubleshooting.resetData.failed": "Notre processus de récupération n’est pas parvenu à supprimer certains fichiers. Nous regrettons que vous ayez rencontré ce problème. Merci de vous rendre sur https://www.notion.so/help pour obtenir de l’aide. Pour forcer une réinitialisation totale de l’application manuellement, fermez complètement Notion. Ensuite, supprimez le chemin suivant : {userDataPath}", "desktopTroubleshooting.resetData.message": "Cela supprimera toutes les données locales et internes, notamment le cache et les paramètres locaux, restaurant ainsi l’application Notion à un état d’installation récent. Cela vous déconnectera également de Notion. Vos pages et autres contenus de l’application resteront intacts. Souhaitez-vous continuer ?", "desktopTroubleshooting.resetData.reset": "Réinitialiser toutes les données locales", "desktopTroubleshooting.resetData.restart": "<PERSON><PERSON><PERSON><PERSON>", "desktopTroubleshooting.resetData.title": "Réinitialiser et effacer toutes les données locales", "desktopTroubleshooting.showLogs.error.message.mac": "Notion a rencontré une erreur lors de l’affichage du registre dans le Finder :", "desktopTroubleshooting.showLogs.error.message.windows": "Notion a rencontré une erreur lors de l’affichage du registre dans l’explorateur :", "desktopTroubleshooting.showLogs.error.title": "Impossible d’afficher les entrées du registre", "desktopTroubleshooting.startRecordingNetLog": "Démarrer l’enregistrement du journal réseau", "desktopTroubleshooting.stopRecordingNetLog": "Arrêter l’enregistrement du journal réseau", "menuBarIcon.menu.changeCommandSearchAndNotionAiShortcut": "Modifier les raccourcis", "menuBarIcon.menu.changeCommandSearchShortcut": "Modifier le raccourci de la recherche rapide", "menuBarIcon.menu.enableQuickSearch": "Activer la recherche rapide", "menuBarIcon.menu.keepInBackground": "Garder en arrière-plan", "menuBarIcon.menu.launchPreferences": "Lancer les préférences", "menuBarIcon.menu.openOnLogin": "Ouvrir Notion à l’ouverture de la session", "menuBarIcon.menu.quitNotion": "Q<PERSON>ter Notion", "menuBarIcon.menu.showImmediately": "Afficher immédiatement", "menuBarIcon.menu.showNotionInMenuBar": "Afficher Notion dans la barre de menu", "menuBarIcon.menu.toggleCommandSearch": "Afficher/masquer la recherche rapide", "menuBarIcon.menu.toggleNotionAi": "Activer/désactiver l’IA de Notion", "openAtLogin.dialog.detail": "{operatingSystem} a empêché Notion de configurer le paramètre « Ouvrir lors de la connexion ». Cela peut se produire lorsque le démarrage de Notion est configuré dans les paramètres système ou si vous ne disposez pas des autorisations d’accès requises. Vous pouvez cependant configurer cette option manuellement dans les paramètres système.", "openAtLogin.dialog.title": "Ouvrir à l’ouverture de la session", "tabSpaces.deleteDialog.cancelButton": "Annuler", "tabSpaces.deleteDialog.deleteButton": "<PERSON><PERSON><PERSON><PERSON>", "tabSpaces.deleteDialog.detail": "Tous les onglets de ce groupe seront dissociés.", "tabSpaces.deleteDialog.title": "Supprimer votre groupe d’onglets « {title} » ?", "tabSpaces.snackbar.switchedToTabGroup": "Bas<PERSON><PERSON> vers {title}", "tabSpaces.snackbar.switchedToUngroupedTabs": "Basculé vers les onglets non groupés", "tabSpaces.snackbar.tabGroupPlaceholder": "Groupe d’onglets", "updatePrompt.detail": "Voulez-vous l’installer maintenant ? Nous nous chargerons de rouvrir vos fenêtres et onglets.", "updatePrompt.installAndRelaunch": "Installer et relancer", "updatePrompt.message": "Une nouvelle version de Notion est disponible !", "updatePrompt.remindMeLater": "Me le rappeler plus tard", "window.closeDialog.cancelButton": "Annuler", "window.closeDialog.confirmButton": "<PERSON><PERSON><PERSON>", "window.closeDialog.title.app": "<PERSON><PERSON><PERSON> ?", "window.closeDialog.title.tab": "Fermer l’onglet Notion ?", "window.closeDialog.title.window": "Fermer la fenêtre Notion ?", "window.loadingError.message": "Erreur lors du chargement de Notion, connectez-vous à Internet pour démarrer.", "window.loadingError.reload": "Recharger", "window.movedTabSnackbarMessage": "{tabTitle} déplacé vers {tabSpaceTitle}", "window.tabLoadingError.cancel": "Annuler", "window.tabMenu.closeOtherTabs": "<PERSON><PERSON><PERSON> les autres onglets", "window.tabMenu.closeTab": "<PERSON><PERSON><PERSON> l<PERSON>on<PERSON>t", "window.tabMenu.closeTabsToLeft": "<PERSON><PERSON><PERSON> les onglets à gauche", "window.tabMenu.closeTabsToRight": "<PERSON><PERSON><PERSON> les onglets à droite", "window.tabMenu.copyLink": "Copier le lien", "window.tabMenu.duplicateTab": "<PERSON>p<PERSON><PERSON> l<PERSON>on<PERSON>t", "window.tabMenu.moveTo": "<PERSON><PERSON><PERSON><PERSON> vers", "window.tabMenu.moveToNewWindow": "<PERSON><PERSON><PERSON><PERSON> l’onglet vers une nouvelle fenêtre", "window.tabMenu.moveToSubmenuNewWindow": "Nouvelle fenêtre", "window.tabMenu.pinTab": "<PERSON><PERSON><PERSON> cet onglet", "window.tabMenu.refresh": "Actualiser l’onglet", "window.tabMenu.reopenClosedTab": "<PERSON><PERSON><PERSON><PERSON><PERSON> le dernier onglet fermé", "window.tabMenu.replacePinnedTabUrl": "Remplacer l’URL épinglée par l’URL actuelle", "window.tabMenu.returnToPinnedTabUrl": "Retour à l’URL épinglée", "window.tabMenu.ungroupTab": "Dissocier l’onglet", "window.tabMenu.unpinTab": "Retirer des onglets épinglés", "window.tabTitlePlaceholder": "Onglet", "window.ungroupedTabSnackbarMessage": "{tabTitle} non groupé"}