(()=>{"use strict";var e={4:(e,n)=>{function t(e){return null!==e}function r(e){return null!=e}Object.defineProperty(n,"__esModule",{value:!0}),n.Info=n.DeprecatedAPI=n.objectAssign=n.objectEntries=n.objectKeys=void 0,n.isNonEmptyArray=function(e){return e.length>0},n.isKeyInObject=function(e,n){return n in e},n.isKeyInMap=function(e,n){return e.has(n)},n.getKeyInMap=function(e,n){return e.get(n)},n.arrayIncludes=function(e,n){return e.includes(n)},n.setIncludes=function(e,n){return e.has(n)},n.isNotNull=t,n.isDefined=function(e){return void 0!==e},n.isNotNullish=r,n.isNullish=function(e){return!r(e)},n.nullableToUndefinable=function(e){return t(e)?e:void 0},n.unreachable=function(e,n){if(n)throw new o(n());let t="(unknown)";try{try{t=JSON.stringify(e)??"undefined"}catch(n){t=String(e);const r=n instanceof Error?n.message:void 0;r&&(t+=` (Not serializable: ${r})`)}}catch{}throw new o(`Expected value to never occur: ${t}`)},n.isObject=function(e){return"object"==typeof e&&null!==e},n.oneOf=function(e){return n=>function(e,n){return n.some(n=>n(e))}(n,e)},n.propertyOf=function(e){return e.toString()},n.Opaque=function(e,n){return e},n.stringStartsWith=function(e,n){return e.startsWith(n)},n.safeCast=function(e){return e},n.mapObject=function(e,t){const r={};for(const[o,i]of(0,n.objectEntries)(e))r[o]=t(i,o);return r},n.objectKeys=Object.keys,n.objectEntries=Object.entries,n.objectAssign=Object.assign;class o extends Error{}n.DeprecatedAPI=Symbol("deprecated api name"),Symbol("abstracted api name"),n.Info=Symbol("info message"),Symbol("warning message")},25:(e,n,t)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.preferences=void 0;const r=t(4),o=t(326);n.preferences={isProtocolRegistered:(0,o.invokerInMain)("notion:get-is-protocol-registered"),setTheme:(0,o.senderToMain)("notion:set-theme"),electronAppFeatures:{get:(0,o.invokerInMain)("notion:get-electron-app-features"),setPreference:(0,o.senderToMain)("notion:set-user-preference"),...(0,o.getSimpleEmitter)("notion:set-electron-app-features")},setGlobalShortcutsEnabled:(0,o.senderToMain)("notion:set-global-shortcuts-enabled"),onOpenSettings:(0,o.getSimpleEmitter)("notion:open-settings"),openSystemSettings:(0,r.safeCast)((0,o.senderToMain)("notion:open-system-settings"))}},239:(e,n,t)=>{Object.defineProperty(n,"__esModule",{value:!0});const r=t(272);n.default=r},272:e=>{e.exports=JSON.parse('{"env":"production","commit":"05c80f1","isLocalhost":false,"domainName":"www.notion.so","domainBaseUrl":"https://www.notion.so","protocol":"notion","desktopAppId":"notion.id","offline":true,"desktopS3Url":"https://desktop-release.notion-static.com","splunkConfig":{"host":"http-inputs-notion.splunkcloud.com","path":"services/collector/raw","token":"EA76605A-F565-4B17-A496-34435622A1EB","maxBatchCount":0,"port":443},"mail":{"domainBaseUrl":"https://mail.notion.so"},"syncSession":{"cookieName":"p_sync_session","domain":".notion.so"},"targetPlatform":"windows"}')},288:e=>{e.exports=require("electron")},306:function(e,n,t){var r,o=this&&this.__createBinding||(Object.create?function(e,n,t,r){void 0===r&&(r=t);var o=Object.getOwnPropertyDescriptor(n,t);o&&!("get"in o?!n.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return n[t]}}),Object.defineProperty(e,r,o)}:function(e,n,t,r){void 0===r&&(r=t),e[r]=n[t]}),i=this&&this.__setModuleDefault||(Object.create?function(e,n){Object.defineProperty(e,"default",{enumerable:!0,value:n})}:function(e,n){e.default=n}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var n=[];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},r(e)},function(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var t=r(e),s=0;s<t.length;s++)"default"!==t[s]&&o(n,e,t[s]);return i(n,e),n}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(n,"__esModule",{value:!0}),n.setupProcessListeners=function(){(0,u.handleMainToRendererRequest)("notion:get-process-heap-stats",()=>[process.getHeapStatistics()]),(0,u.handleMainToRendererRequest)("notion:get-blink-memory-info",()=>[process.getBlinkMemoryInfo()]),(0,u.handleMainToRendererRequest)("notion:get-argv",()=>[process.argv]),u.handleMainToRendererEvent.addListener("notion:crash",async()=>{if("production"===c.default.env){const{preferences:e}=await Promise.resolve().then(()=>s(t(25))),{preferences:n}=await e.electronAppFeatures.get(),r=n?.isDebugMenuEnabled;if(!r)return}console.warn("Inducing crash as requested "),process.crash()})};const c=a(t(239)),u=t(326)},326:function(e,n,t){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(n,"__esModule",{value:!0}),n.handleMainToRendererEvent=void 0,n.getSimpleEmitter=function(e){return{addListener:t=>n.handleMainToRendererEvent.addListener(e,t),removeListener(t){n.handleMainToRendererEvent.removeListener(e,t)},listeners:()=>n.handleMainToRendererEvent.listeners(e)}},n.invokeInMainAndReturnResult=i,n.invokerInMain=function(e){return(...n)=>i(e,...n)},n.sendToMain=s,n.senderToMain=function(e){return(...n)=>s(e,...n)},n.handleMainToRendererRequest=function(e,n){const t=async(t,...r)=>{const i=await n(...r);o.default.ipcRenderer.send(e,...i)};return o.default.ipcRenderer.addListener(e,t),()=>o.default.ipcRenderer.removeListener(e,t)};const o=r(t(288));function i(e,...n){return o.default.ipcRenderer.invoke(e,...n)}function s(e,...n){o.default.ipcRenderer.send(e,...n)}n.handleMainToRendererEvent={addListener(e,n){const t=n;return o.default.ipcRenderer.addListener(e,t),()=>o.default.ipcRenderer.removeListener(e,t)},removeListener(e,n){o.default.ipcRenderer.removeListener(e,n)},listeners:e=>o.default.ipcRenderer.listeners(e),once(e,n){o.default.ipcRenderer.once(e,n)}}}},n={};function t(r){var o=n[r];if(void 0!==o)return o.exports;var i=n[r]={exports:{}};return e[r].call(i.exports,i,i.exports,t),i.exports}void 0!==t&&(t.ab="/native_modules/"),(()=>{const e=t(288),n=t(306),r=new Promise(e=>{window.onload=e});e.ipcRenderer.on("client-placeholder:message-port",async e=>{await r,window.postMessage("client-placeholder:message-port","*",e.ports)}),(0,n.setupProcessListeners)()})()})();
//# sourceMappingURL=preload.js.map