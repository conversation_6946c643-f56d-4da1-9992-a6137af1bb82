(()=>{"use strict";var e={4:(e,n)=>{function t(e){return null!==e}function o(e){return null!=e}Object.defineProperty(n,"__esModule",{value:!0}),n.Info=n.DeprecatedAPI=n.objectAssign=n.objectEntries=n.objectKeys=void 0,n.isNonEmptyArray=function(e){return e.length>0},n.isKeyInObject=function(e,n){return n in e},n.isKeyInMap=function(e,n){return e.has(n)},n.getKeyInMap=function(e,n){return e.get(n)},n.arrayIncludes=function(e,n){return e.includes(n)},n.setIncludes=function(e,n){return e.has(n)},n.isNotNull=t,n.isDefined=function(e){return void 0!==e},n.isNotNullish=o,n.isNullish=function(e){return!o(e)},n.nullableToUndefinable=function(e){return t(e)?e:void 0},n.unreachable=function(e,n){if(n)throw new i(n());let t="(unknown)";try{try{t=JSON.stringify(e)??"undefined"}catch(n){t=String(e);const o=n instanceof Error?n.message:void 0;o&&(t+=` (Not serializable: ${o})`)}}catch{}throw new i(`Expected value to never occur: ${t}`)},n.isObject=function(e){return"object"==typeof e&&null!==e},n.oneOf=function(e){return n=>function(e,n){return n.some(n=>n(e))}(n,e)},n.propertyOf=function(e){return e.toString()},n.Opaque=function(e,n){return e},n.stringStartsWith=function(e,n){return e.startsWith(n)},n.safeCast=function(e){return e},n.mapObject=function(e,t){const o={};for(const[i,a]of(0,n.objectEntries)(e))o[i]=t(a,i);return o},n.objectKeys=Object.keys,n.objectEntries=Object.entries,n.objectAssign=Object.assign;class i extends Error{}n.DeprecatedAPI=Symbol("deprecated api name"),Symbol("abstracted api name"),n.Info=Symbol("info message"),Symbol("warning message")},17:(e,n,t)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.setBadge=function(e){let n=null;"win32"===process.platform&&Boolean(e)&&(n=function(e){const n=document.createElement("canvas");n.width=16*window.devicePixelRatio,n.height=16*window.devicePixelRatio;const t=n.getContext("2d");if(!t)return null;const o=.9,i=n.width/2/o,a=n.height/2/o,r=n.width/2*o*o;return t.beginPath(),t.arc(i,a,r,0,2*Math.PI,!1),t.fillStyle="rgba(247,94,79,0.95)",t.fill(),t.font=8.1*window.devicePixelRatio+"px sans-serif",t.fillStyle="white",t.textAlign="center",t.fillText(parseInt(e)>99?"99+":e,i,a+3.15*window.devicePixelRatio),n.toDataURL("image/png")}(e)),(0,o.sendToMain)("notion:set-badge",{badgeString:e,badgeImageDataUrl:n,devicePixelRatio:window.devicePixelRatio})};const o=t(326)},25:(e,n,t)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.preferences=void 0;const o=t(4),i=t(326);n.preferences={isProtocolRegistered:(0,i.invokerInMain)("notion:get-is-protocol-registered"),setTheme:(0,i.senderToMain)("notion:set-theme"),electronAppFeatures:{get:(0,i.invokerInMain)("notion:get-electron-app-features"),setPreference:(0,i.senderToMain)("notion:set-user-preference"),...(0,i.getSimpleEmitter)("notion:set-electron-app-features")},setGlobalShortcutsEnabled:(0,i.senderToMain)("notion:set-global-shortcuts-enabled"),onOpenSettings:(0,i.getSimpleEmitter)("notion:open-settings"),openSystemSettings:(0,o.safeCast)((0,i.senderToMain)("notion:open-system-settings"))}},239:(e,n,t)=>{Object.defineProperty(n,"__esModule",{value:!0});const o=t(272);n.default=o},272:e=>{e.exports=JSON.parse('{"env":"production","commit":"05c80f1","isLocalhost":false,"domainName":"www.notion.so","domainBaseUrl":"https://www.notion.so","protocol":"notion","desktopAppId":"notion.id","offline":true,"desktopS3Url":"https://desktop-release.notion-static.com","splunkConfig":{"host":"http-inputs-notion.splunkcloud.com","path":"services/collector/raw","token":"EA76605A-F565-4B17-A496-34435622A1EB","maxBatchCount":0,"port":443},"mail":{"domainBaseUrl":"https://mail.notion.so"},"syncSession":{"cookieName":"p_sync_session","domain":".notion.so"},"targetPlatform":"windows"}')},288:e=>{e.exports=require("electron")},306:function(e,n,t){var o,i=this&&this.__createBinding||(Object.create?function(e,n,t,o){void 0===o&&(o=t);var i=Object.getOwnPropertyDescriptor(n,t);i&&!("get"in i?!n.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return n[t]}}),Object.defineProperty(e,o,i)}:function(e,n,t,o){void 0===o&&(o=t),e[o]=n[t]}),a=this&&this.__setModuleDefault||(Object.create?function(e,n){Object.defineProperty(e,"default",{enumerable:!0,value:n})}:function(e,n){e.default=n}),r=this&&this.__importStar||(o=function(e){return o=Object.getOwnPropertyNames||function(e){var n=[];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},o(e)},function(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var t=o(e),r=0;r<t.length;r++)"default"!==t[r]&&i(n,e,t[r]);return a(n,e),n}),s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(n,"__esModule",{value:!0}),n.setupProcessListeners=function(){(0,l.handleMainToRendererRequest)("notion:get-process-heap-stats",()=>[process.getHeapStatistics()]),(0,l.handleMainToRendererRequest)("notion:get-blink-memory-info",()=>[process.getBlinkMemoryInfo()]),(0,l.handleMainToRendererRequest)("notion:get-argv",()=>[process.argv]),l.handleMainToRendererEvent.addListener("notion:crash",async()=>{if("production"===d.default.env){const{preferences:e}=await Promise.resolve().then(()=>r(t(25))),{preferences:n}=await e.electronAppFeatures.get(),o=n?.isDebugMenuEnabled;if(!o)return}console.warn("Inducing crash as requested "),process.crash()})};const d=s(t(239)),l=t(326)},326:function(e,n,t){var o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(n,"__esModule",{value:!0}),n.handleMainToRendererEvent=void 0,n.getSimpleEmitter=function(e){return{addListener:t=>n.handleMainToRendererEvent.addListener(e,t),removeListener(t){n.handleMainToRendererEvent.removeListener(e,t)},listeners:()=>n.handleMainToRendererEvent.listeners(e)}},n.invokeInMainAndReturnResult=a,n.invokerInMain=function(e){return(...n)=>a(e,...n)},n.sendToMain=r,n.senderToMain=function(e){return(...n)=>r(e,...n)},n.handleMainToRendererRequest=function(e,n){const t=async(t,...o)=>{const a=await n(...o);i.default.ipcRenderer.send(e,...a)};return i.default.ipcRenderer.addListener(e,t),()=>i.default.ipcRenderer.removeListener(e,t)};const i=o(t(288));function a(e,...n){return i.default.ipcRenderer.invoke(e,...n)}function r(e,...n){i.default.ipcRenderer.send(e,...n)}n.handleMainToRendererEvent={addListener(e,n){const t=n;return i.default.ipcRenderer.addListener(e,t),()=>i.default.ipcRenderer.removeListener(e,t)},removeListener(e,n){i.default.ipcRenderer.removeListener(e,n)},listeners:e=>i.default.ipcRenderer.listeners(e),once(e,n){i.default.ipcRenderer.once(e,n)}}},451:(e,n,t)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.setupMessagePort=function(){o.ipcRenderer.on("notion:new-message-port",async e=>{await i,window.postMessage("notion:new-message-port","*",e.ports)})};const o=t(288),i=new Promise(e=>{window.onload=e})},584:function(e,n,t){var o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(n,"__esModule",{value:!0}),n.electronApi=void 0;const i=o(t(852)),a=t(288),r=t(4),s=o(t(239)),d=t(326),l=t(451),c=t(25),p=t(306),u=t(17),g=t(788),m=t(739),M={readText:(0,d.invokerInMain)("notion:clipboard:read-text"),writeText:(0,d.invokerInMain)("notion:clipboard:write-text"),readHtml:(0,d.invokerInMain)("notion:clipboard:read-html"),writeHtml:(0,d.invokerInMain)("notion:clipboard:write-html"),readRtf:(0,d.invokerInMain)("notion:clipboard:read-rtf"),writeRtf:(0,d.invokerInMain)("notion:clipboard:write-rtf"),readImage:(0,d.invokerInMain)("notion:clipboard:read-image"),writeImage:(0,d.invokerInMain)("notion:clipboard:write-image"),readBookmark:(0,d.invokerInMain)("notion:clipboard:read-bookmark"),writeBookmark:(0,d.invokerInMain)("notion:clipboard:write-bookmark"),clear:(0,d.invokerInMain)("notion:clipboard:clear"),availableFormats:(0,d.invokerInMain)("notion:clipboard:available-formats"),has:(0,d.invokerInMain)("notion:clipboard:has"),read:(0,d.invokerInMain)("notion:clipboard:read"),write:(0,d.invokerInMain)("notion:clipboard:write"),readBuffer:(0,d.invokerInMain)("notion:clipboard:read-buffer"),writeBuffer:(0,d.invokerInMain)("notion:clipboard:write-buffer")},f={getSpellCheckerLanguages:(0,d.invokerInMain)("notion:get-spellchecker-languages"),getAvailableSpellCheckerLanguages:(0,d.invokerInMain)("notion:get-available-spellchecker-languages"),setSpellCheckerLanguages:(0,d.senderToMain)("notion:set-spellchecker-languages"),setSpellCheckerEnabled:(0,d.senderToMain)("notion:set-spellchecker-enabled"),addToDictionary:(0,d.senderToMain)("notion:add-to-dictionary"),getSubstitutions:(0,d.invokerInMain)("notion:get-substitutions")},b={checkForUpdates:(0,d.senderToMain)("notion:check-for-updates"),updateReady:(0,d.getSimpleEmitter)("notion:update-ready"),installUpdate:(0,d.senderToMain)("notion:install-update"),installAppJsUpdate:(0,d.senderToMain)("notion:install-appjs-update"),updateError:(0,d.getSimpleEmitter)("notion:update-error"),updateChecking:(0,d.getSimpleEmitter)("notion:checking-for-update"),updateAvailable:(0,d.getSimpleEmitter)("notion:update-available"),updateProgress:(0,d.getSimpleEmitter)("notion:update-progress"),updateNotAvailable:(0,d.getSimpleEmitter)("notion:update-not-available"),checkForAppUpdates:(0,d.senderToMain)("notion:check-for-app-updates"),appUpdateReady:(0,d.getSimpleEmitter)("notion:app-update-ready"),appUpdateError:(0,d.getSimpleEmitter)("notion:app-update-error"),appUpdateChecking:(0,d.getSimpleEmitter)("notion:checking-for-app-update"),appUpdateAvailable:(0,d.getSimpleEmitter)("notion:app-update-available"),appUpdateProgress:(0,d.getSimpleEmitter)("notion:app-update-progress"),appUpdateNotAvailable:(0,d.getSimpleEmitter)("notion:app-update-not-available"),appUpdateFinished:(0,d.getSimpleEmitter)("notion:app-update-finished"),appUpdateInstall:(0,d.getSimpleEmitter)("notion:app-update-install")},v={openSearchModalForNewTab:(0,d.getSimpleEmitter)("notion:open-search-for-new-tab"),closeQuickSearch:(0,d.senderToMain)("notion:quick-search-close"),openQuickSearchResult:(0,d.senderToMain)("notion:quick-search-open-result"),quickSearchReady:(0,d.senderToMain)("notion:quick-search-ready"),quickSearchNotReady:(0,d.senderToMain)("notion:quick-search-not-ready"),quickSearchRenderCompleted:(0,d.senderToMain)("notion:quick-search-render-completed"),quickSearchSetSearchAssistantMode:(0,d.senderToMain)("notion:quick-search-set-search-assistant-mode"),quickSearchRefresh:(0,d.senderToMain)("notion:quick-search-refresh"),openQuickSearchShortcutSetting:(0,d.senderToMain)("notion:quick-search-open-shortcut-settings"),quickSearchVisibilityState:{isVisible:(0,d.invokerInMain)("notion:is-quick-search-visible"),...(0,d.getSimpleEmitter)("quick-search:visibility-state-changed")},closeGlobalSearchModal:(0,d.senderToMain)("notion:close-global-search-modal"),openPageFromGlobalSearch:(0,d.senderToMain)("notion:open-page-from-global-search"),openSearchModalFromQuickSearch:(0,d.getSimpleEmitter)("quick-search:open-search-modal"),openNotionAiFromQuickSearch:(0,d.getSimpleEmitter)("quick-search:open-notion-ai")},h={contextMenu:(0,d.getSimpleEmitter)("notion:context-menu"),copy:(0,d.senderToMain)("notion:copy"),copyImage:e=>(0,g.copyImage)(e,M),copyText:e=>M.writeText(e),cut:(0,d.senderToMain)("notion:cut"),paste:(0,d.senderToMain)("notion:paste"),replaceMisspelling:(0,d.senderToMain)("notion:replace-misspelling")},k={clearAllCookies:(0,d.senderToMain)("notion:clear-all-cookies"),clearBrowserHistory:(0,d.senderToMain)("notion:clear-browser-history"),clearCookies:(0,d.senderToMain)("notion:clear-cookies"),downloadUrl:(0,d.senderToMain)("notion:download-url"),getCookie:(0,d.invokerInMain)("notion:get-cookie"),setNavigationState:(0,d.senderToMain)("notion:set-navigation-state"),navigationReady:(0,d.senderToMain)("notion:ready"),snackbarReady:(0,d.senderToMain)("notion:set-snackbar-ready"),onNavigate:(0,d.getSimpleEmitter)("notion:navigate-to-url"),openAppMenu:(0,d.senderToMain)("notion:open-app-menu"),openDevTools:(0,d.senderToMain)("notion:open-dev-tools"),relayMessageToServiceWorker:(0,d.getSimpleEmitter)("notion:relay-message-to-service-worker"),relayMessageFromServiceWorker:(0,d.senderToMain)("notion:relay-message-from-service-worker"),setBadge:u.setBadge,setCookie:(0,d.senderToMain)("notion:set-cookie"),setCloseConfirmationDialogReason:(0,d.senderToMain)("notion:set-close-confirmation-dialog-reason"),networkEmulation:{getEmulationOptions:(0,d.invokerInMain)("notion:get-network-emulation-options"),getEmulation:(0,d.invokerInMain)("notion:get-network-emulation"),setEmulation:(0,d.invokerInMain)("notion:set-network-emulation"),onSetEmulation:(0,d.getSimpleEmitter)("notion:on-set-network-emulation")}},T={openInNewTab:(0,r.safeCast)((0,d.senderToMain)("notion:new-tab-from-notion")),setTabInitialFaviconAndTitle:(0,d.senderToMain)("notion:set-tab-initial-favicon-and-title"),newTab:(0,d.senderToMain)("notion:new-tab-from-tab-bar"),isMainTab:(0,d.invokerInMain)("notion:is-main-tab"),isActiveTab:{get:(0,d.invokerInMain)("notion:is-active-tab"),...(0,d.getSimpleEmitter)("notion:set-is-active-tab")},handleTabClicked:(0,d.senderToMain)("notion:tab-clicked"),electronTabs:(0,d.getSimpleEmitter)("notion:set-electron-tabs"),electronCurrentTab:(0,d.getSimpleEmitter)("notion:set-electron-current-tab"),setUnauthorizedIframeUrls:(0,d.getSimpleEmitter)("notion:set-unauthorized-iframe-urls"),setTabBreadcrumbs:(0,d.senderToMain)("notion:set-tab-breadcrumbs"),setTabColors:(0,d.senderToMain)("notion:set-tab-colors"),setIsMediaActive:(0,d.senderToMain)("notion:set-is-media-active"),setIsRecording:(0,d.senderToMain)("notion:set-is-recording"),setActiveBlocks:(0,d.senderToMain)("notion:set-active-blocks"),closeTab:(0,d.senderToMain)("notion:close-tab-from-notion"),navigateToBlockInTab:(0,d.senderToMain)("notion:navigate-to-block-in-tab"),navigateToBlock:(0,d.getSimpleEmitter)("notion:navigate-to-block"),pushToastInMainTab:(0,d.senderToMain)("notion:push-toast-in-main-tab"),pushToast:(0,d.getSimpleEmitter)("notion:push-toast"),dismissToastInAllTabs:(0,d.senderToMain)("notion:dismiss-toast-in-all-tabs"),dismissToast:(0,d.getSimpleEmitter)("notion:dismiss-toast"),addUnloadTabHandler:e=>(0,d.handleMainToRendererRequest)("notion:get-client-state-and-blockers",e),addGetClientStateHandler:e=>(0,d.handleMainToRendererRequest)("notion:get-client-state",e),getRestoredClientState:(0,d.invokerInMain)("notion:get-restored-client-state"),handleBackForwardNavigation:(0,d.getSimpleEmitter)("notion:back-forward-navigation-client"),handleMetaClick:(0,d.getSimpleEmitter)("notion:meta-click-client"),showTabHistoryMenu:(0,d.getSimpleEmitter)("notion:show-tab-history-menu-client"),setHoveredTab:(0,d.getSimpleEmitter)("notion:set-hovered-tab"),clearHoveredTab:(0,d.getSimpleEmitter)("notion:clear-hovered-tab")},w={tabSpacesState:(0,d.getSimpleEmitter)("notion:set-tab-spaces-state"),showSnackBarMessage:(0,d.getSimpleEmitter)("notion:show-snack-bar-message"),toggleTabSpacesMenuOpen:(0,d.senderToMain)("notion:toggle-tab-spaces-menu-open"),setTabSpacesMenuOpenState:(0,d.senderToMain)("notion:set-tab-spaces-menu-open-state"),addToggleMenuOpenListener:e=>d.handleMainToRendererEvent.addListener("notion:toggle-tab-spaces-menu-open",e),addOpenCreateMenuListener:e=>d.handleMainToRendererEvent.addListener("notion:open-create-menu",e),setActiveTabSection:(0,d.senderToMain)("notion:set-active-tab-section"),createTabSpace:(0,d.senderToMain)("notion:create-tab-space"),updateTabSpaceMetadata:(0,d.senderToMain)("notion:update-tab-space-metadata"),reorderTabSpaces:(0,d.senderToMain)("notion:reorder-tab-spaces"),deleteTabSpace:(0,d.senderToMain)("notion:delete-tab-space")},S={isRunningUnderArm64Translation:(0,d.invokerInMain)("notion:get-is-running-under-arm64-translation"),setLoggerData:(0,d.senderToMain)("notion:set-logger-data"),trackBatched:(0,d.getSimpleEmitter)("notion:track-batched"),getAppVersion:(0,d.invokerInMain)("notion:get-app-version"),getAnalyticsInfo:(0,d.invokerInMain)("notion:get-analytics-info"),getSqliteDiskUsage:(0,d.invokerInMain)("notion:get-sqlite-disk-usage"),logErrorForOffline:(0,d.senderToMain)("notion:log-error-for-offline"),writeSessionLogs:(0,d.invokerInMain)("notion:write-session-logs"),openSessionLogFile:(0,d.invokerInMain)("notion:open-session-log-file")},y={newWindow:(0,d.getSimpleEmitter)("notion:new-window"),openInNewWindow:(0,d.senderToMain)("notion:create-window"),openExternalUrl:(0,d.invokerInMain)("notion:open-external-url"),openOauthPopup:m.openOauthPopup,openGoogleDrivePickerPopup:m.openGoogleDrivePickerPopup,refreshAll:(0,r.safeCast)((0,d.invokerInMain)("notion:refresh-all")),isMainWindow:()=>n.electronApi.isMainTab?.()??!1,windowIsVisible:(0,d.invokerInMain)("notion:is-window-visible"),setWindowTitle:(0,d.senderToMain)("notion:set-window-title"),toggleMaximized:(0,d.senderToMain)("notion:toggle-maximized"),fullscreen:{get:(0,d.invokerInMain)("notion:get-fullscreen"),...(0,d.getSimpleEmitter)("notion:full-screen-changed")},zoom:{get:(0,r.safeCast)(void 0)},setOverlay:(0,r.safeCast)((0,d.senderToMain)("notion:set-tab-is-overlay-active")),setZenMode:(0,d.senderToMain)("notion:set-zen-mode"),openNotificationUrl:(0,d.senderToMain)("notion:open-notification-url"),relaunch:(0,d.senderToMain)("notion:relaunch")},I={sqliteServerEnabled:!0,getSqliteMeta:(0,d.invokerInMain)("notion:get-sqlite-meta")},E={sidebarState:{set:(0,d.senderToMain)("notion:set-sidebar-state"),addSetSidebarOpenListener:e=>d.handleMainToRendererEvent.addListener("notion:set-sidebar-open",e),removeSetSidebarOpenListener(e){d.handleMainToRendererEvent.removeListener("notion:set-sidebar-open",e)},addToggleListener:e=>d.handleMainToRendererEvent.addListener("notion:toggle-sidebar-expanded",e),removeToggleListener(e){d.handleMainToRendererEvent.removeListener("notion:toggle-sidebar-expanded",e)}},windowSidebarState:{get:(0,d.invokerInMain)("notion:get-window-sidebar-state"),set:(0,d.senderToMain)("notion:set-window-sidebar-state"),...(0,d.getSimpleEmitter)("notion:set-window-sidebar-state")},setAppStoreState:(0,d.senderToMain)("notion:set-app-store-state")},R={performShortcut:(0,d.getSimpleEmitter)("notion:perform-shortcut")},_={getMediaAccessStatus:(0,d.invokerInMain)("notion:get-media-access-status"),getRunningInputAudioProcesses:(0,d.invokerInMain)("notion:get-running-input-audio-processes"),initializeRunningInputAudioProcessDetection:(0,d.senderToMain)("notion:initialize-running-input-audio-process-detection"),runningInputAudioProcessesChanged:(0,d.getSimpleEmitter)("notion:running-input-audio-processes-changed")},P={getDeviceName:(0,d.invokerInMain)("notion:get-device-name"),getDeviceVolume:(0,d.invokerInMain)("notion:get-device-volume")},A={showMeetingNotification:(0,d.senderToMain)("notion:show-meeting-notification"),showMeetingTranscriptionState:(0,d.senderToMain)("notion:show-meeting-transcription-state"),meetingNotificationReady:(0,d.senderToMain)("notion:meeting-notification-ready"),closeMeetingNotification:(0,d.senderToMain)("notion:close-meeting-notification"),disableMeetingNotifications:(0,d.senderToMain)("notion:disable-meeting-notifications"),clickMeetingNotification:(0,d.senderToMain)("notion:click-meeting-notification"),meetingNotificationClicked:(0,d.getSimpleEmitter)("notion:meeting-notification-clicked"),meetingNotificationsDisabled:(0,d.getSimpleEmitter)("notion:meeting-notifications-disabled"),meetingNotificationState:{get:(0,d.invokerInMain)("notion:get-meeting-notification-state"),...(0,d.getSimpleEmitter)("notion:set-meeting-notification-state")},getMeetingNotificationNonce:(0,d.invokerInMain)("notion:get-meeting-notification-nonce")};n.electronApi={...S,...k,...h,...c.preferences,...v,...f,...I,...E,...T,...b,...y,...R,..._,...P,...A,clipboard:M,tabSpaces:w},a.contextBridge.exposeInMainWorld("__electronApi",n.electronApi),a.contextBridge.exposeInMainWorld("__isElectron",!0),a.contextBridge.exposeInMainWorld("__platform",i.default.platform),a.contextBridge.exposeInMainWorld("__desktopConfig",s.default),(0,p.setupProcessListeners)(),n.electronApi.getAnalyticsInfo().then(e=>{a.contextBridge.exposeInMainWorld("__desktopDeviceInfo",e)}).catch(e=>{console.error(e)}),"darwin"!==i.default.platform&&(window.addEventListener("keydown",e=>{"Alt"===e.key?e.ctrlKey||e.shiftKey||e.metaKey||(0,d.sendToMain)("notion:alt-key-down"):(0,d.sendToMain)("notion:cancel-alt-menu-open")}),window.addEventListener("keyup",e=>{"Alt"===e.key&&(0,d.sendToMain)("notion:alt-key-up")},{capture:!0})),(0,l.setupMessagePort)()},739:(e,n,t)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.openOauthPopup=async function(e){return(0,o.sendToMain)("notion:create-popup",e),new Promise(e=>{o.handleMainToRendererEvent.once("notion:popup-callback",(n,t)=>e(t))})},n.openGoogleDrivePickerPopup=async function(e){return(0,o.sendToMain)("notion:create-google-drive-picker",e),new Promise(e=>{o.handleMainToRendererEvent.once("notion:google-drive-picker-callback",(n,t)=>e(t))})};const o=t(326)},788:(e,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.copyImage=function(e,n){const t=document.createElement("canvas"),o=t.getContext("2d"),i=new Image;i.crossOrigin="Anonymous",i.onload=async()=>{if(t.height=i.height,t.width=i.width,o){o.drawImage(i,0,0);const e=t.toDataURL("image/png");await n.writeImage(e)}t.remove(),i.remove()},i.src=e}},852:e=>{e.exports=process}},n={};function t(o){var i=n[o];if(void 0!==i)return i.exports;var a=n[o]={exports:{}};return e[o].call(a.exports,a,a.exports,t),a.exports}void 0!==t&&(t.ab="/native_modules/"),t(584)})();
//# sourceMappingURL=preload.js.map