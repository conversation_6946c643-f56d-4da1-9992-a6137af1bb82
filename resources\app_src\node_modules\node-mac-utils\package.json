{"name": "node-mac-utils", "version": "1.2.1", "description": "A native Node.js module with utilities for macOS and Windows", "main": "index.js", "type": "index.d.ts", "scripts": {"build": "node-gyp rebuild", "clean": "node-gyp clean", "lint": "clang-format --dry-run --Werror mac_utils.mm && prettier --check index.js", "format": "clang-format -i mac_utils.mm && prettier --write index.js", "test": "node test-mic-monitor.js"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "dependencies": {"bindings": "1.5.0", "node-addon-api": "6.1.0"}, "devDependencies": {"clang-format": "1.8.0", "node-gyp": "9.4.0", "prettier": "2.8.8"}}