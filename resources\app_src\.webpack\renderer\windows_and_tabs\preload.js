(()=>{"use strict";var e={288:e=>{e.exports=require("electron")},326:function(e,n,r){var t=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(n,"__esModule",{value:!0}),n.handleMainToRendererEvent=void 0,n.getSimpleEmitter=function(e){return{addListener:r=>n.handleMainToRendererEvent.addListener(e,r),removeListener(r){n.handleMainToRendererEvent.removeListener(e,r)},listeners:()=>n.handleMainToRendererEvent.listeners(e)}},n.invokeInMainAndReturnResult=d,n.invokerInMain=function(e){return(...n)=>d(e,...n)},n.sendToMain=o,n.senderToMain=function(e){return(...n)=>o(e,...n)},n.handleMainToRendererRequest=function(e,n){const r=async(r,...t)=>{const d=await n(...t);i.default.ipcRenderer.send(e,...d)};return i.default.ipcRenderer.addListener(e,r),()=>i.default.ipcRenderer.removeListener(e,r)};const i=t(r(288));function d(e,...n){return i.default.ipcRenderer.invoke(e,...n)}function o(e,...n){i.default.ipcRenderer.send(e,...n)}n.handleMainToRendererEvent={addListener(e,n){const r=n;return i.default.ipcRenderer.addListener(e,r),()=>i.default.ipcRenderer.removeListener(e,r)},removeListener(e,n){i.default.ipcRenderer.removeListener(e,n)},listeners:e=>i.default.ipcRenderer.listeners(e),once(e,n){i.default.ipcRenderer.once(e,n)}}}},n={};function r(t){var i=n[t];if(void 0!==i)return i.exports;var d=n[t]={exports:{}};return e[t].call(d.exports,d,d.exports,r),d.exports}void 0!==r&&(r.ab="/native_modules/"),(()=>{const e=r(288),n={windowsAndTabsList:(0,r(326).invokerInMain)("notion:get-windows-and-tabs-list")};e.contextBridge.exposeInMainWorld("__windowListApi",n)})()})();
//# sourceMappingURL=preload.js.map