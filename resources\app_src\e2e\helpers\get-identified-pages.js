"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PageType = void 0;
exports.getIdentifiedPages = getIdentifiedPages;
const node_url_1 = require("node:url");
var PageType;
(function (PageType) {
    PageType["tabBar"] = "tabBar";
    PageType["tab"] = "tab";
    PageType["tabPreview"] = "tabPreview";
    PageType["extension"] = "extension";
    PageType["quickSearch"] = "quickSearch";
    PageType["blank"] = "blank";
})(PageType || (exports.PageType = PageType = {}));
function getIdentifiedPages(electronApp) {
    const result = {
        [PageType.tab]: [],
        [PageType.tabBar]: [],
        [PageType.tabPreview]: [],
        [PageType.quickSearch]: [],
        [PageType.extension]: [],
        [PageType.blank]: [],
    };
    const pwPages = electronApp.windows();
    for (const pwPage of pwPages) {
        const type = getTypeForPage(pwPage);
        if (type === PageType.tabBar) {
            result[PageType.tabBar].push(pwPage);
        }
        else if (type === PageType.tabPreview) {
            result[PageType.tabPreview].push(pwPage);
        }
        else if (type === PageType.quickSearch) {
            result[PageType.quickSearch].push(pwPage);
        }
        else if (type === PageType.extension) {
            result[PageType.extension].push(pwPage);
        }
        else if (type === PageType.blank) {
            result[PageType.blank].push(pwPage);
        }
        else if (type === PageType.tab) {
            result[PageType.tab].push(pwPage);
        }
    }
    return result;
}
function getTypeForPage(page) {
    const url = page.url();
    if (url.endsWith("renderer/tabs/index.html")) {
        return PageType.tabBar;
    }
    else if (url.endsWith("renderer/tab_preview/index.html")) {
        return PageType.tabPreview;
    }
    else if (url.endsWith("/quick-search")) {
        return PageType.quickSearch;
    }
    else if (url.startsWith("chrome-extension://")) {
        return PageType.extension;
    }
    else if (url === "about:blank") {
        return PageType.blank;
    }
    else {
        const { host, protocol } = new node_url_1.URL(url);
        if (protocol === "https:" && host.endsWith("notion.so")) {
            return PageType.tab;
        }
    }
    throw new Error(`Unknown page type for ${page.url()}.`);
}
