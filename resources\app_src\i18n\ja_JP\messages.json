{"activityMonitor.copyDiagnosticInformation": "診断情報をコピー", "activityMonitor.copyExecutablePath": "実行可能ファイルのパスをコピー", "activityMonitor.copyUrl": "URLをコピー", "activityMonitor.forceKillProcess": "強制終了プロセス", "activityMonitor.inspectActivityMonitor": "アクティビティモニターを確認する", "activityMonitor.killProcess": "終了プロセス", "activityMonitor.openDevTools": "DevToolsを開く", "activityMonitor.reload": "再読み込み", "clientPlaceholder.placeholderDescription": "クリックしてこのウィンドウに表示する", "clientPlaceholder.placeholderTitle": "現在、別のウィンドウでコンテンツを表示しています", "commandSearch.window.title": "Notion コマンド検索", "crashWatchdog.dialog.abnormalExit": "異常終了：ページがゼロ以外の終了コードで終了しました。", "crashWatchdog.dialog.buttonCloseTab": "タブを閉じる", "crashWatchdog.dialog.buttonCloseWindow": "ウィンドウを閉じる", "crashWatchdog.dialog.buttonRestartApp": "Notionを再起動する", "crashWatchdog.dialog.crashed": "クラッシュ：不明な理由によりページがクラッシュしました。", "crashWatchdog.dialog.details": "ページURL：{url}理由：{reason}終了コード：{exitCode} ", "crashWatchdog.dialog.integrityFailure": "整合性エラー：ページがコード整合性チェックに失敗しました。", "crashWatchdog.dialog.killed": "強制終了：ページが外部プロセスによって強制終了されました。", "crashWatchdog.dialog.launchFailed": "ローンチエラー：プロセスのローンチに失敗しました。", "crashWatchdog.dialog.message": "このタブを表示中に問題が発生し、自動的に復元できませんでした。", "crashWatchdog.dialog.oom": "OOM：ページのメモリが不足してクラッシュしました。", "crashWatchdog.dialog.title": "問題が発生しました", "crashWatchdog.dialog.urlUnknown": "不明", "desktop.activityMonitor.all": "すべて", "desktop.activityMonitor.cpuPercent": "% CPU", "desktop.activityMonitor.cpuPercentDescription": "プロセスによって使用される利用可能なCPU時間の割合です。", "desktop.activityMonitor.cpuTime": "CPU時間", "desktop.activityMonitor.cpuTimeDescription": "プロセスの起動後に使用されるCPU時間の合計秒数です。", "desktop.activityMonitor.creationTime": "作成日時", "desktop.activityMonitor.creationTimeDescription": "プロセスが作成されてからの時間です。", "desktop.activityMonitor.frames": "フレーム", "desktop.activityMonitor.framesDescription": "プロセスによって管理されるフレームの数です。多くのレンダラープロセスは、複数のフレームを担当します。", "desktop.activityMonitor.hidden": "非表示", "desktop.activityMonitor.hideColumns": "列を非表示", "desktop.activityMonitor.hideFilters": "フィルターを非表示", "desktop.activityMonitor.idleWakeupsPerSecond": "Idle Wakeups", "desktop.activityMonitor.idleWakeupsPerSecondDescription": "最後にアクティビティモニターを更新してから、プロセスがCPUを起動した回数です。", "desktop.activityMonitor.loading": "アクティビティデータを読み込み中", "desktop.activityMonitor.memCurrent": "メモリ（現在）", "desktop.activityMonitor.memCurrentDescription": "プロセスの「ワーキングセット」サイズ、すなわち現在RAMに存在するページのセットです。この数値は、オペレーティングシステムのメモリ圧縮、非アクティブおよびキャッシュされたページの管理、または他のメモリ管理技術を説明するものではありません。プロセスで使われる物理メモリの量は、おそらくはるかに小さいと思われます。", "desktop.activityMonitor.memPeak": "メモリ（ピーク）", "desktop.activityMonitor.memPeakDescription": "プロセスのピークのワーキングセットサイズ、すなわちプロセスが起動してから使われた物理メモリの最大量です。プロセスの「ワーキングセット」サイズ、つまり現在RAMに存在するページのセットです。この数値は、オペレーティングシステムのメモリ圧縮、非アクティブとキャッシュされたページの管理、または他のメモリ管理技術を説明するものではありません。プロセスで使われる物理メモリの量は、おそらくはるかに小さいと思われます。", "desktop.activityMonitor.memPrivate": "メモリ（プライベート）", "desktop.activityMonitor.memPrivateDescription": "JSヒープやHTMLコンテンツなど、他のプロセスと共有されていない、プロセスによって割り当てられた物理メモリの量です。", "desktop.activityMonitor.memShared": "メモリ（共有）", "desktop.activityMonitor.memSharedDescription": "共有ライブラリやマップされたファイルなど、他のプロセスと共有されている、プロセスによって割り当てられた物理メモリの量です。", "desktop.activityMonitor.mixed": "混合", "desktop.activityMonitor.parentWindowId": "親ウィンドウID", "desktop.activityMonitor.parentWindowIdDescription": "このタブを含むウィンドウの一意の識別子。", "desktop.activityMonitor.pid": "PID", "desktop.activityMonitor.pidDescription": "オペレーティングシステムによって使用されるプロセスのプロセスIDです。", "desktop.activityMonitor.processName": "プロセス名", "desktop.activityMonitor.showColumns": "列を表示", "desktop.activityMonitor.showFilters": "フィルターを表示", "desktop.activityMonitor.tabId": "タブID", "desktop.activityMonitor.tabIdDescription": "Notionアプリ内のタブの一意の識別子。", "desktop.activityMonitor.type": "種類", "desktop.activityMonitor.url": "URL", "desktop.activityMonitor.urlDescription": "プロセスのURLです。多くのレンダラープロセスは、複数のフレームを担当します。詳細については、フレーム列を参照してください。", "desktop.activityMonitor.visibilityState": "可視性", "desktop.activityMonitor.visibilityStateDescription": "プロセスの可視性の状態です。プロセスがレンダラープロセスの場合、これはメインフレームの可視性の状態になります。", "desktop.activityMonitor.visible": "表示", "desktop.tabBar.backButtonLabel": "戻る", "desktop.tabBar.closeSidebarLabel": "サイドバーを閉じる", "desktop.tabBar.closeTabLabel": "{tabTitle}のタブを閉じる", "desktop.tabBar.forwardButtonLabel": "進む", "desktop.tabBar.newTabButtonLabel": "新規タブ", "desktop.tabBar.openSidebarLabel": "サイドバーを開く", "desktop.tabBar.tabSpacesLabel": "タブスペース", "desktopExtensions.install.failed.title": "拡張機能のインストールに失敗しました", "desktopExtensions.manage.cancel": "キャンセル", "desktopExtensions.manage.disable": "無効にする", "desktopExtensions.manage.enable": "有効にする", "desktopExtensions.manage.message": "{extensionTitle} {extensionVersion}で何をしたいですか？", "desktopExtensions.manage.title": "拡張機能の管理", "desktopExtensions.manage.uninstall": "アンインストール", "desktopExtensions.manage.unload": "アンロード", "desktopExtensions.openFailed.noPopupMessage": "この拡張機能はポップアップを特定しませんでした(action.default_popup)", "desktopExtensions.openFailed.noPopupTitle": "拡張機能を開けませんでした", "desktopExtensions.unzip.failed.badFileRead": "CRXファイルの読み込みに失敗しました： {error}", "desktopExtensions.unzip.failed.badFileWrite": "ファイル {filePath} への書き込みに失敗しました： {error}", "desktopExtensions.unzip.failed.badFolderCreate": "{extensionPath} に拡張機能フォルダーを作成できませんでした： {error}", "desktopExtensions.unzip.failed.badManifest": "この拡張機能のmanifest.jsonを解析できませんでした。これは有効な拡張機能ですか？ エラー： {error}", "desktopExtensions.unzip.failed.badManifestNoName": "manifest.jsonに名前が見つかりません。", "desktopExtensions.unzip.failed.error": "CRXファイルの解凍に失敗しました： {error}", "desktopExtensions.unzip.failed.noManifest": "CRXにmanifest.jsonが見つかりません。これは有効な拡張機能ですか？", "desktopInstaller.failedToMove.detail": "アプリをアプリケーションフォルダーに移動できませんでした。手動で移動してください。", "desktopInstaller.failedToMove.title": "アプリの移動に失敗しました", "desktopInstaller.invalidInstallDialog.cancelButton.label": "キャンセル", "desktopInstaller.invalidInstallDialog.confirmMove": "Notionアプリが正しくインストールされていません。Notionアプリをアプリケーションフォルダーに移動してもよろしいですか？", "desktopInstaller.invalidInstallDialog.okButton.label": "OK", "desktopInstaller.invalidInstallDialog.title": "インストールが無効です", "desktopTopbar.appMenu.about": "Notionについて", "desktopTopbar.appMenu.checkForUpdate": "アップデートを確認…", "desktopTopbar.appMenu.checkForUpdate.noUpdateAvailable": "お使いのNotionは最新バージョンです。", "desktopTopbar.appMenu.checkForUpdate.title": "アップデートを確認", "desktopTopbar.appMenu.checkForUpdate.updateAvailable": "Notionの新しいバージョンが利用可能なため、現在バックグラウンドでダウンロード中です。最新バージョンにアップデートしていただきありがとうございます。", "desktopTopbar.appMenu.checkForUpdate.updateCheckFailed": "インターネット接続の問題か、アップデートサーバー自体の問題により、アップデートサーバーに接続できませんでした。時間をおいてもう一度お試しください。", "desktopTopbar.appMenu.downloadingUpdate": "アップデートをダウンロード中（{percentage}%）", "desktopTopbar.appMenu.hide": "Notionを非表示", "desktopTopbar.appMenu.hideOthers": "ほかを非表示", "desktopTopbar.appMenu.preferences": "環境設定…", "desktopTopbar.appMenu.quit": "終了", "desktopTopbar.appMenu.quitWithoutSavingTabs": "タブを保存せずに終了する", "desktopTopbar.appMenu.restartToApplyUpdate": "再起動してアップデートを適用", "desktopTopbar.appMenu.services": "サービス", "desktopTopbar.appMenu.unhide": "すべて表示する", "desktopTopbar.editMenu.copy": "コピー", "desktopTopbar.editMenu.copyLinkToCurrentPage": "現在のページへのリンクをコピー", "desktopTopbar.editMenu.copyLinkToCurrentPageBlockTitle": "現在のページの名前をコピー", "desktopTopbar.editMenu.cut": "切り取り", "desktopTopbar.editMenu.paste": "貼り付け", "desktopTopbar.editMenu.pasteAndMatchStyle": "貼り付けてスタイルを合わせる", "desktopTopbar.editMenu.redo": "やり直し", "desktopTopbar.editMenu.selectAll": "すべて選択", "desktopTopbar.editMenu.speech": "音声読み上げ", "desktopTopbar.editMenu.speech.startSpeaking": "音声読み上げを開始", "desktopTopbar.editMenu.speech.stopSpeaking": "音声読み上げを停止", "desktopTopbar.editMenu.title": "編集", "desktopTopbar.editMenu.undo": "元に戻す", "desktopTopbar.extensionsMenu.install": "拡張機能をインストール", "desktopTopbar.extensionsMenu.manage": "拡張機能の管理", "desktopTopbar.fileMenu.close": "ウィンドウを閉じる", "desktopTopbar.fileMenu.closeTab": "タブを閉じる", "desktopTopbar.fileMenu.newNotionWindow": "新しいNotionウィンドウ", "desktopTopbar.fileMenu.newTab": "新規タブ", "desktopTopbar.fileMenu.newWindow": "新しいウィンドウ", "desktopTopbar.fileMenu.print": "印刷…", "desktopTopbar.fileMenu.quit": "終了", "desktopTopbar.fileMenu.quitWithoutSavingTabs": "タブを保存せずに終了する", "desktopTopbar.fileMenu.reopenClosedTab": "最後に閉じたタブを再び開く", "desktopTopbar.fileMenu.title": "ファイル", "desktopTopbar.helpMenu.copyInstallId": "インストールIDをコピー", "desktopTopbar.helpMenu.disableAdvancedLogging": "詳細ログを無効にして再起動する", "desktopTopbar.helpMenu.disableDebugLogging": "詳細ログを無効にして再起動する", "desktopTopbar.helpMenu.disableHardwareAcceleration": "ハードウェアアクセラレーションを無効化して再起動", "desktopTopbar.helpMenu.enableAdvancedLogging": "詳細ログを有効にして再起動する", "desktopTopbar.helpMenu.enableDebugLogging": "詳細ログを有効にして再起動する", "desktopTopbar.helpMenu.enableHardwareAcceleration": "ハードウェアアクセラレーションを有効化して再起動", "desktopTopbar.helpMenu.openActivityMonitor": "アクティビティモニターを開く", "desktopTopbar.helpMenu.openConsole": "コンソールを開く", "desktopTopbar.helpMenu.openHelpAndSupport": "ヘルプと解説を開く", "desktopTopbar.helpMenu.recordingNetLog": "ネットワークログを記録中…", "desktopTopbar.helpMenu.recordNetLog": "ネットワークログを記録…", "desktopTopbar.helpMenu.recordNetLogConfirmation": "今ネットログがダウンロードフォルダに記録されています。記録を停止するには、トラブルシューティングメニューの「ネットワークログの記録を停止」ボタンをクリックするか、アプリを終了してください。", "desktopTopbar.helpMenu.recordNetLogFailed": "ネットワークログの記録に失敗しました。再度お試しいただくか、詳細についてはログを確認してください。", "desktopTopbar.helpMenu.recordNetLogFailedMessage": "再度お試しいただくか、詳細についてはログをご確認ください。エラーは次のとおりです。", "desktopTopbar.helpMenu.recordNetLogFailedTitle": "ネットワークログの記録に失敗しました", "desktopTopbar.helpMenu.recordNetLogStop": "ネットワークログの記録を停止中…", "desktopTopbar.helpMenu.recordPerformanceTrace": "パフォーマンストレースを記録…", "desktopTopbar.helpMenu.recordPerformanceTraceConfirm": "今から30秒間のパフォーマンストレースを記録しますか？完了すると、ダウンロードフォルダに保存されます。", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmCancel": "キャンセル", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmOk": "パフォーマンストレースを記録", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmTitle": "パフォーマンストレースを記録しますか？", "desktopTopbar.helpMenu.resetAndEraseAllLocalData": "すべてのローカルデータのリセットと消去", "desktopTopbar.helpMenu.showLogsInExplorer": "エクスプローラーでログを表示", "desktopTopbar.helpMenu.showLogsInFinder": "Finderでログを表示", "desktopTopbar.helpMenu.title": "ヘルプ", "desktopTopbar.historyMenu.historyBack": "戻る", "desktopTopbar.historyMenu.historyForward": "進む", "desktopTopbar.historyMenu.title": "履歴", "desktopTopbar.toggleDevTools": "開発者ツールの表示", "desktopTopbar.toggleWindowDevTools": "ウィンドウ開発者ツールの表示", "desktopTopbar.troubleshootingMenu.title": "トラブルシューティング", "desktopTopbar.viewMenu.actualSize": "100%表示", "desktopTopbar.viewMenu.forceReload": "強制的に再読み込み", "desktopTopbar.viewMenu.forceReloadDialog.cancel": "キャンセル", "desktopTopbar.viewMenu.forceReloadDialog.message": "現在オフラインです。このページを強制的に再読み込みすると、再度インターネットに接続するまでアクセスできなくなります。", "desktopTopbar.viewMenu.forceReloadDialog.ok": "再読み込みする", "desktopTopbar.viewMenu.forceReloadDialog.title": "強制的に再読み込みしてもよろしいですか？", "desktopTopbar.viewMenu.reload": "再読み込み", "desktopTopbar.viewMenu.showHideSidebar": "サイドバーの表示/非表示", "desktopTopbar.viewMenu.showHideTabSpaceButton": "タブグループを表示/非表示", "desktopTopbar.viewMenu.title": "表示", "desktopTopbar.viewMenu.togglefullscreen": "全画面表示のオン・オフ", "desktopTopbar.viewMenu.zoomIn": "拡大", "desktopTopbar.viewMenu.zoomOut": "縮小", "desktopTopbar.whatsNewMac.title": "Mac版Notionの最新情報を開く", "desktopTopbar.whatsNewWindows.title": "Windows版Notionの最新情報を開く", "desktopTopbar.windowMenu.close": "閉じる", "desktopTopbar.windowMenu.front": "前面", "desktopTopbar.windowMenu.maximize": "最大化", "desktopTopbar.windowMenu.minimize": "最小化", "desktopTopbar.windowMenu.showNextTab": "次のタブを表示", "desktopTopbar.windowMenu.showPreviousTab": "前のタブを表示", "desktopTopbar.windowMenu.title": "ウィンドウ", "desktopTopbar.windowMenu.zoom": "ズーム", "desktopTroubleshooting.resetData.cancel": "キャンセルする", "desktopTroubleshooting.resetData.closingWindows": "Notionウィンドウを閉じようとしています", "desktopTroubleshooting.resetData.deletingFiles": "ファイルを削除しています", "desktopTroubleshooting.resetData.done": "完了", "desktopTroubleshooting.resetData.doneMessage": "アプリがリセットされました。", "desktopTroubleshooting.resetData.failed": "リカバリプロセスで一部のファイルを削除できませんでした。ご不便をおかけして申し訳ございません。サポートが必要な場合はhttps://www.notion.so/helpにアクセスしてください。アプリを手動で強制初期化するには、Notionを完全に閉じてから、{userDataPath}のパスを削除します。", "desktopTroubleshooting.resetData.message": "これにより、キャッシュやローカル設定を含むローカルおよび内部データがすべて削除され、Notionアプリがインストールされたばかりの状態に復元されます。また、Notionから自動的にログアウトされます。ページやその他のアプリ内コンテンツはそのままの状態で残ります。続行しますか？", "desktopTroubleshooting.resetData.reset": "すべてのローカルデータをリセットする", "desktopTroubleshooting.resetData.restart": "再起動", "desktopTroubleshooting.resetData.title": "すべてのローカルデータのリセットと消去", "desktopTroubleshooting.showLogs.error.message.mac": "NotionがFinderでログを表示しようとした際に次のエラーが発生しました：", "desktopTroubleshooting.showLogs.error.message.windows": "Notionがエクスプローラーでログを表示しようとした際に次のエラーが発生しました：", "desktopTroubleshooting.showLogs.error.title": "ログの表示に失敗しました", "desktopTroubleshooting.startRecordingNetLog": "ネットワークログの記録を開始する", "desktopTroubleshooting.stopRecordingNetLog": "ネットワークログの記録を停止する", "menuBarIcon.menu.changeCommandSearchAndNotionAiShortcut": "ショートカットを編集", "menuBarIcon.menu.changeCommandSearchShortcut": "コマンド検索ショートカットを変更", "menuBarIcon.menu.enableQuickSearch": "クイック検索を有効化", "menuBarIcon.menu.keepInBackground": "バックグラウンドで保持", "menuBarIcon.menu.launchPreferences": "起動設定", "menuBarIcon.menu.openOnLogin": "ログイン時にNotionを開く", "menuBarIcon.menu.quitNotion": "Notionを終了する", "menuBarIcon.menu.showImmediately": "すぐに表示", "menuBarIcon.menu.showNotionInMenuBar": "メニューバーにNotionを表示する", "menuBarIcon.menu.toggleCommandSearch": "コマンド検索の表示/非表示", "menuBarIcon.menu.toggleNotionAi": "Notion AIを表示/非表示", "openAtLogin.dialog.detail": "{operatingSystem}により、Notionの「ログイン時に開く」を設定できませんでした。これは通常、Notionの起動がシステム設定で構成されているか、権限が不十分な場合に発生します。この設定は、システム設定で手動で行うこともできます。", "openAtLogin.dialog.title": "ログイン時に開く", "tabSpaces.deleteDialog.cancelButton": "キャンセル", "tabSpaces.deleteDialog.deleteButton": "削除", "tabSpaces.deleteDialog.detail": "このタブグループのすべてのタブのグループ化は解除されます。", "tabSpaces.deleteDialog.title": "「{title}」タブグループを削除しますか？", "tabSpaces.snackbar.switchedToTabGroup": "次に切り替えました： {title}", "tabSpaces.snackbar.switchedToUngroupedTabs": "グループ化されていないタブに切り替えました", "tabSpaces.snackbar.tabGroupPlaceholder": "タブグループ", "updatePrompt.detail": "今すぐインストールしますか？ウィンドウとタブは復元されます。", "updatePrompt.installAndRelaunch": "インストールして再起動", "updatePrompt.message": "Notionの新バージョンがリリースされました！", "updatePrompt.remindMeLater": "あとでリマインド", "window.closeDialog.cancelButton": "キャンセル", "window.closeDialog.confirmButton": "閉じる", "window.closeDialog.title.app": "Notionを閉じますか？", "window.closeDialog.title.tab": "Notionタブを閉じますか？", "window.closeDialog.title.window": "Notionウィンドウを閉じますか？", "window.loadingError.message": "Notionの読み込みに失敗しました。インターネットに接続してください。", "window.loadingError.reload": "再読み込み", "window.movedTabSnackbarMessage": "{tabTitle}を{tabSpaceTitle}に移動しました", "window.tabLoadingError.cancel": "キャンセル", "window.tabMenu.closeOtherTabs": "他のタブを閉じる", "window.tabMenu.closeTab": "タブを閉じる", "window.tabMenu.closeTabsToLeft": "左のタブを閉じる", "window.tabMenu.closeTabsToRight": "右のタブを閉じる", "window.tabMenu.copyLink": "リンクをコピー", "window.tabMenu.duplicateTab": "タブを複製", "window.tabMenu.moveTo": "移動先：", "window.tabMenu.moveToNewWindow": "タブを新しいウィンドウに移動", "window.tabMenu.moveToSubmenuNewWindow": "新しいウィンドウ", "window.tabMenu.pinTab": "タブを固定表示", "window.tabMenu.refresh": "タブを再読み込み", "window.tabMenu.reopenClosedTab": "最後に閉じたタブを開く", "window.tabMenu.replacePinnedTabUrl": "固定表示のURLを現在のURLで置換", "window.tabMenu.returnToPinnedTabUrl": "固定表示のURLに戻る", "window.tabMenu.ungroupTab": "タブのグループ化を解除", "window.tabMenu.unpinTab": "タブの固定表示を解除", "window.tabTitlePlaceholder": "タブ", "window.ungroupedTabSnackbarMessage": "{tabTitle}のグループ化を解除しました"}