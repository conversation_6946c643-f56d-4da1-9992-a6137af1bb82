{"activityMonitor.copyDiagnosticInformation": "Diagnoseinformationen kopieren", "activityMonitor.copyExecutablePath": "Ausführbaren Pfad kopieren", "activityMonitor.copyUrl": "URL kopieren", "activityMonitor.forceKillProcess": "Beendigung des Prozesses erzwingen", "activityMonitor.inspectActivityMonitor": "Aktivitätsmonitor prüfen", "activityMonitor.killProcess": "Prozess beenden", "activityMonitor.openDevTools": "DevT<PERSON><PERSON>", "activityMonitor.reload": "Neu laden", "clientPlaceholder.placeholderDescription": "<PERSON><PERSON> klicken, um es in diesem Fenster anzuzeigen", "clientPlaceholder.placeholderTitle": "Die Inhalte werden derzeit in einem anderen Fenster angezeigt", "commandSearch.window.title": "Notion – Direktsuche", "crashWatchdog.dialog.abnormalExit": "Abnormales Verlassen: Die Seite wurde mit einem Exit-Code ungleich Null verlassen.", "crashWatchdog.dialog.buttonCloseTab": "<PERSON><PERSON> s<PERSON>n", "crashWatchdog.dialog.buttonCloseWindow": "Fenster schließen", "crashWatchdog.dialog.buttonRestartApp": "Notion neu starten", "crashWatchdog.dialog.crashed": "Abgestürzt: Die Seite ist aus einem unbekannten Grund abgestürzt.", "crashWatchdog.dialog.details": "Seiten-URL: {url} Grund: {reason} Exit-Code: {exitCode}", "crashWatchdog.dialog.integrityFailure": "Integritätsfehler: Die Codeintegritätsprüfung der Seite ist fehlgeschlagen.", "crashWatchdog.dialog.killed": "Zerstört: Die Seite wurde durch einen externen Prozess zerstört.", "crashWatchdog.dialog.launchFailed": "Starten fehlgeschlagen: Der Prozess konnte nicht gestartet werden.", "crashWatchdog.dialog.message": "<PERSON>im Anzeigen dieses Tabs ist ein Fehler aufgetreten und wir konnten ihn nicht automatisch wiederherstellen.", "crashWatchdog.dialog.oom": "OOM: Die Seite hat keinen Speicher mehr und ist abgestürzt.", "crashWatchdog.dialog.title": "Es ist ein Fehler aufgetreten", "crashWatchdog.dialog.urlUnknown": "Unbekannt", "desktop.activityMonitor.all": "Alle", "desktop.activityMonitor.cpuPercent": "% CPU", "desktop.activityMonitor.cpuPercentDescription": "Der Anteil der verfügbaren CPU-Zeit, der vom Prozess verwendet wird.", "desktop.activityMonitor.cpuTime": "CPU-Zeit", "desktop.activityMonitor.cpuTimeDescription": "Die gesamte Anzahl der verwendeten Sekunden der CPU-Zeit seit dem Prozessstart.", "desktop.activityMonitor.creationTime": "<PERSON><PERSON><PERSON><PERSON>", "desktop.activityMonitor.creationTimeDescription": "Die Zeitspanne seit der Erstellung des Prozesses.", "desktop.activityMonitor.frames": "Frames", "desktop.activityMonitor.framesDescription": "Die Anzahl der Frames, die vom Prozess verwaltet werden. Viele Renderer-Prozesse sind für mehrere Frames verantwortlich.", "desktop.activityMonitor.hidden": "Ausgeblendet", "desktop.activityMonitor.hideColumns": "Spalten verbergen", "desktop.activityMonitor.hideFilters": "<PERSON><PERSON> au<PERSON>", "desktop.activityMonitor.idleWakeupsPerSecond": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "desktop.activityMonitor.idleWakeupsPerSecondDescription": "<PERSON><PERSON><PERSON>, wie oft der Prozess die CPU seit dem letzten Aktivitätsmonitor-Update geweckt hat.", "desktop.activityMonitor.loading": "Aktivitätsdaten werden geladen", "desktop.activityMonitor.memCurrent": "<PERSON><PERSON><PERSON><PERSON> (Aktuell)", "desktop.activityMonitor.memCurrentDescription": "Die Größe des „Arbeitssatzes“ des Prozesses, bei dem es sich um den Satz von Seiten handelt, die sich derzeit im RAM befinden. Diese <PERSON> berücksichtigt nicht die Speicherkomprimierung des Betriebssystems, die Verwaltung inaktiver und zwischengespeicherter Seiten oder andere Speicherverwaltungstechniken. Die Menge an physischem Speicher, der vom Prozess verwendet wird, ist wahrscheinlich deutlich kleiner.", "desktop.activityMonitor.memPeak": "<PERSON><PERSON><PERSON><PERSON> (Maximal)", "desktop.activityMonitor.memPeakDescription": "Die maximale Größe des Arbeitssatzes des Prozesses, d. h. die maximale Menge an physischem Speicher, die vom Prozess seit seinem Start verwendet wird. Die Größe des „Arbeitssatzes“ des Prozesses, bei dem es sich um den Satz von Se<PERSON>n handelt, die sich derzeit im RAM befinden. Die<PERSON> berücksichtigt nicht die Speicherkomprimierung des Betriebssystems, die Verwaltung inaktiver und zwischengespeicherter Seiten oder andere Speicherverwaltungstechniken. Die Menge an physischem Speicher, der vom Prozess verwendet wird, ist wahrscheinlich deutlich kleiner.", "desktop.activityMonitor.memPrivate": "<PERSON><PERSON><PERSON><PERSON> (Privat)", "desktop.activityMonitor.memPrivateDescription": "Die Menge an physischem Speicher, der vom Prozess verwaltet wird und nicht mit anderen Prozessen wie JS Heap oder HTML-Inhalten geteilt wird.", "desktop.activityMonitor.memShared": "Spe<PERSON>r (Geteilt)", "desktop.activityMonitor.memSharedDescription": "Die Menge an physischem Speicher, der vom Prozess verwaltet wird und mit anderen Prozessen wie freigegebenen Bibliotheken oder zugeordneten Dateien geteilt wird.", "desktop.activityMonitor.mixed": "<PERSON><PERSON><PERSON><PERSON>", "desktop.activityMonitor.parentWindowId": "ID des übergeordneten Fensters", "desktop.activityMonitor.parentWindowIdDescription": "Die eindeutige Kennung des Fensters, das diesen Tab enthält.", "desktop.activityMonitor.pid": "PID", "desktop.activityMonitor.pidDescription": "Die Prozess-ID des Prozesses, die vom Betriebssystem verwendet wird.", "desktop.activityMonitor.processName": "Prozessname", "desktop.activityMonitor.showColumns": "Spalten anzeigen", "desktop.activityMonitor.showFilters": "Filter anzeigen", "desktop.activityMonitor.tabId": "Tab-ID", "desktop.activityMonitor.tabIdDescription": "Die eindeutige Kennung des Tabs in der Notion-App.", "desktop.activityMonitor.type": "<PERSON><PERSON>", "desktop.activityMonitor.url": "URL", "desktop.activityMonitor.urlDescription": "Die URL des Prozesses. Viele Renderer-Prozesse sind für mehrere Frames verantwortlich. Weitere Informationen findest du in der Spalte „Frames“.", "desktop.activityMonitor.visibilityState": "Sichtbarkeit", "desktop.activityMonitor.visibilityStateDescription": "Der Sichtbarkeitszustand des Prozesses. Wenn der Prozess ein Renderer-Prozess ist, ist dies der Sichtbarkeitszustand des Haupt-Frames.", "desktop.activityMonitor.visible": "<PERSON><PERSON><PERSON>", "desktop.tabBar.backButtonLabel": "Zurück", "desktop.tabBar.closeSidebarLabel": "Seitenleiste schließen", "desktop.tabBar.closeTabLabel": "Tab sch<PERSON>, {tabTitle}", "desktop.tabBar.forwardButtonLabel": "<PERSON><PERSON>", "desktop.tabBar.newTabButtonLabel": "<PERSON><PERSON><PERSON>", "desktop.tabBar.openSidebarLabel": "Seitenleiste öffnen", "desktop.tabBar.tabSpacesLabel": "Tab-Bereiche", "desktopExtensions.install.failed.title": "Erweiterung konnte nicht installiert werden", "desktopExtensions.manage.cancel": "Abbrechen", "desktopExtensions.manage.disable": "Deaktivieren", "desktopExtensions.manage.enable": "Aktivieren", "desktopExtensions.manage.message": "Was möchtest du mit {extensionTitle} {extensionVersion} tun?", "desktopExtensions.manage.title": "Erweiterung verwalten", "desktopExtensions.manage.uninstall": "Deinstallieren", "desktopExtensions.manage.unload": "Entladen", "desktopExtensions.openFailed.noPopupMessage": "Diese Erweiterung hat kein Popup spezifiziert (action.default_popup)", "desktopExtensions.openFailed.noPopupTitle": "Erweiterung konnte nicht geöffnet werden", "desktopExtensions.unzip.failed.badFileRead": "CRX-<PERSON><PERSON> konnte nicht gelesen werden: {error}", "desktopExtensions.unzip.failed.badFileWrite": "Schreiben der Datei {filePath} fehlgeschlagen: {error}", "desktopExtensions.unzip.failed.badFolderCreate": "Erstellen des Erweiterungsordners unter {extensionPath} fehlgeschlagen: {error}", "desktopExtensions.unzip.failed.badManifest": "Die manifest.json-<PERSON><PERSON> in dieser Erweiterung konnte nicht geparst werden. Der Fehler war: {error}", "desktopExtensions.unzip.failed.badManifestNoName": "<PERSON>in Name in der manifest.json-Datei gefunden", "desktopExtensions.unzip.failed.error": "Entpacken der CRX-Datei fehlgeschlagen: {error}", "desktopExtensions.unzip.failed.noManifest": "In der CRX-Datei wurde keine manifest.json-Datei gefunden. Ist dies eine gültige Erweiterung?", "desktopInstaller.failedToMove.detail": "Wir konnten die App nicht in deinen Anwendungsordner verschieben. Verschiebe sie bitte manuell.", "desktopInstaller.failedToMove.title": "App konnte nicht verschoben werden", "desktopInstaller.invalidInstallDialog.cancelButton.label": "Abbrechen", "desktopInstaller.invalidInstallDialog.confirmMove": "Deine Notion-Anwendung ist nicht richtig installiert. Dürfen wir die Notion-App in deinen Anwendungsordner verschieben?", "desktopInstaller.invalidInstallDialog.okButton.label": "OK", "desktopInstaller.invalidInstallDialog.title": "Ungültige Installation", "desktopTopbar.appMenu.about": "Über Notion", "desktopTopbar.appMenu.checkForUpdate": "<PERSON><PERSON> nach Updates…", "desktopTopbar.appMenu.checkForUpdate.noUpdateAvailable": "Du verwendest die neueste Version von Notion!", "desktopTopbar.appMenu.checkForUpdate.title": "<PERSON><PERSON> nach Updates", "desktopTopbar.appMenu.checkForUpdate.updateAvailable": "Eine neue Version von Notion ist verfügbar und wird derzeit im Hintergrund heruntergeladen. <PERSON><PERSON>, dass du auf dem Laufenden bleibst!", "desktopTopbar.appMenu.checkForUpdate.updateCheckFailed": "Notion konnte keine Verbindung mit dem Update-Server herstellen. Dies kann an einem Problem mit deiner Internetverbindung oder dem Update-Server liegen. Bitte versuche es später erneut.", "desktopTopbar.appMenu.downloadingUpdate": "Update wird her<PERSON><PERSON><PERSON><PERSON><PERSON> ({percentage}%)", "desktopTopbar.appMenu.hide": "Notion verbergen", "desktopTopbar.appMenu.hideOthers": "Andere verbergen", "desktopTopbar.appMenu.preferences": "Einstellungen …", "desktopTopbar.appMenu.quit": "<PERSON>den", "desktopTopbar.appMenu.quitWithoutSavingTabs": "<PERSON><PERSON><PERSON> von <PERSON> abbrechen", "desktopTopbar.appMenu.restartToApplyUpdate": "<PERSON>eu starten, um das Update auszuführen", "desktopTopbar.appMenu.services": "<PERSON><PERSON><PERSON>", "desktopTopbar.appMenu.unhide": "Alle anzeigen", "desktopTopbar.editMenu.copy": "<PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.copyLinkToCurrentPage": "Link in aktuelle Seite kopieren", "desktopTopbar.editMenu.copyLinkToCurrentPageBlockTitle": "Name der aktuellen Seite kopieren", "desktopTopbar.editMenu.cut": "Ausschneiden", "desktopTopbar.editMenu.paste": "Einfügen", "desktopTopbar.editMenu.pasteAndMatchStyle": "Einfügen und Stil anpassen", "desktopTopbar.editMenu.redo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.selectAll": "Alles auswählen", "desktopTopbar.editMenu.speech": "Sprachausgabe", "desktopTopbar.editMenu.speech.startSpeaking": "<PERSON><PERSON><PERSON><PERSON> beginnen", "desktopTopbar.editMenu.speech.stopSpeaking": "<PERSON><PERSON><PERSON><PERSON>den", "desktopTopbar.editMenu.title": "<PERSON><PERSON><PERSON>", "desktopTopbar.editMenu.undo": "<PERSON><PERSON>g<PERSON><PERSON><PERSON> machen", "desktopTopbar.extensionsMenu.install": "Erweiterung installieren…", "desktopTopbar.extensionsMenu.manage": "Erweiterungen verwalten", "desktopTopbar.fileMenu.close": "Fenster schließen", "desktopTopbar.fileMenu.closeTab": "<PERSON><PERSON> s<PERSON>n", "desktopTopbar.fileMenu.newNotionWindow": "Neues Notion-Fenster", "desktopTopbar.fileMenu.newTab": "<PERSON><PERSON><PERSON>", "desktopTopbar.fileMenu.newWindow": "<PERSON><PERSON><PERSON>", "desktopTopbar.fileMenu.print": "<PERSON>ucken…", "desktopTopbar.fileMenu.quit": "<PERSON>den", "desktopTopbar.fileMenu.quitWithoutSavingTabs": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.fileMenu.reopenClosedTab": "Zuletzt geschlossenen Tab erneut öffnen", "desktopTopbar.fileMenu.title": "<PERSON><PERSON>", "desktopTopbar.helpMenu.copyInstallId": "Installations-<PERSON> kopieren", "desktopTopbar.helpMenu.disableAdvancedLogging": "Erweiterte Protokollierung und Neustart deaktivieren", "desktopTopbar.helpMenu.disableDebugLogging": "Erweitertes Anmelden deaktivieren und neu starten", "desktopTopbar.helpMenu.disableHardwareAcceleration": "Hardwarebeschleunigung deaktivieren und neu starten", "desktopTopbar.helpMenu.enableAdvancedLogging": "Erweiterte Protokollierung und Neustart aktivieren", "desktopTopbar.helpMenu.enableDebugLogging": "Erweitertes Anmelden aktivieren und neu starten", "desktopTopbar.helpMenu.enableHardwareAcceleration": "Hardwarebeschleunigung aktivieren und neu starten", "desktopTopbar.helpMenu.openActivityMonitor": "Aktivitätsmoni<PERSON>", "desktopTopbar.helpMenu.openConsole": "<PERSON><PERSON><PERSON>", "desktopTopbar.helpMenu.openHelpAndSupport": "Hilfe und Dokumentation öffnen", "desktopTopbar.helpMenu.recordingNetLog": "Netzwerkprotokoll wird aufgezeichnet…", "desktopTopbar.helpMenu.recordNetLog": "Netzwerkprotokoll aufzeichnen...", "desktopTopbar.helpMenu.recordNetLogConfirmation": "Ein Netzwerkprotokoll wird jetzt in deinem Download-Ordner gespeichert. Um die Aufzeichnung zu stoppen, klicke im Menü „Fehlerbehebung“ auf „Netzwerkprotokoll stoppen“ oder beende die App.", "desktopTopbar.helpMenu.recordNetLogFailed": "Das Aufzeichnen des Netzwerkprotokolls ist fehlgeschlagen. Versuche es erneut oder sieh in den Protokollen nach, um weitere Informationen zu erhalten.", "desktopTopbar.helpMenu.recordNetLogFailedMessage": "Versuche es erneut oder sieh in den Protokollen nach, um weitere Informationen zu erhalten. Der Fehler war:", "desktopTopbar.helpMenu.recordNetLogFailedTitle": "Das Aufzeichnen des Netzwerkprotokolls ist fehlgeschlagen", "desktopTopbar.helpMenu.recordNetLogStop": "Aufzeichnung des Netzwerkprotokolls beenden…", "desktopTopbar.helpMenu.recordPerformanceTrace": "Performance <PERSON> aufze<PERSON>…", "desktopTopbar.helpMenu.recordPerformanceTraceConfirm": "Möchtest du einen Leistungsnachweis für die nächsten 30 Sekunden aufzeichnen? Nach der Aufzeichnung wird dieser im Ordner „Downloads“ gespeichert.", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmCancel": "Abbrechen", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmOk": "Leistungsnachweis aufzeichnen", "desktopTopbar.helpMenu.recordPerformanceTraceConfirmTitle": "Leistungsnachweis aufzeichnen?", "desktopTopbar.helpMenu.resetAndEraseAllLocalData": "Alle lokalen Daten zurücksetzen und löschen", "desktopTopbar.helpMenu.showLogsInExplorer": "Protokolle im Explorer anzeigen", "desktopTopbar.helpMenu.showLogsInFinder": "Protokolle im Finder anzeigen", "desktopTopbar.helpMenu.title": "<PERSON><PERSON><PERSON>", "desktopTopbar.historyMenu.historyBack": "Zurück", "desktopTopbar.historyMenu.historyForward": "<PERSON><PERSON>", "desktopTopbar.historyMenu.title": "<PERSON><PERSON><PERSON><PERSON>", "desktopTopbar.toggleDevTools": "Entwicklerwerkzeuge aufklappen", "desktopTopbar.toggleWindowDevTools": "Fenster-Entwicklerwerkzeuge aufklappen", "desktopTopbar.troubleshootingMenu.title": "Fehlerbehebung", "desktopTopbar.viewMenu.actualSize": "Tatsächliche Größe", "desktopTopbar.viewMenu.forceReload": "Neuladen erzwingen", "desktopTopbar.viewMenu.forceReloadDialog.cancel": "Abbrechen", "desktopTopbar.viewMenu.forceReloadDialog.message": "Du bist derzeit offline. Wenn du das Neuladen dieser Seite erzwingst, verlierst du den Zugriff darauf, bis du wieder online bist.", "desktopTopbar.viewMenu.forceReloadDialog.ok": "Trotzdem neu laden", "desktopTopbar.viewMenu.forceReloadDialog.title": "Möchtest du das Neuladen wirklich erzwingen?", "desktopTopbar.viewMenu.reload": "Neu laden", "desktopTopbar.viewMenu.showHideSidebar": "Seitenleiste ein-/ausblenden", "desktopTopbar.viewMenu.showHideTabSpaceButton": "Tabgruppen anzeigen/ausblenden", "desktopTopbar.viewMenu.title": "<PERSON><PERSON><PERSON>", "desktopTopbar.viewMenu.togglefullscreen": "Vollbild umschalten", "desktopTopbar.viewMenu.zoomIn": "Vergrößern", "desktopTopbar.viewMenu.zoomOut": "Verkleinern", "desktopTopbar.whatsNewMac.title": "Neue Funktionen in Notion für macOS ansehen", "desktopTopbar.whatsNewWindows.title": "Neue Funktionen in Notion für Windows ansehen", "desktopTopbar.windowMenu.close": "Schließen", "desktopTopbar.windowMenu.front": "Vordergrund", "desktopTopbar.windowMenu.maximize": "<PERSON><PERSON><PERSON>", "desktopTopbar.windowMenu.minimize": "Minimieren", "desktopTopbar.windowMenu.showNextTab": "Nächsten Tab anzeigen", "desktopTopbar.windowMenu.showPreviousTab": "Vorherigen Tab anzeigen", "desktopTopbar.windowMenu.title": "<PERSON><PERSON>", "desktopTopbar.windowMenu.zoom": "Zoom", "desktopTroubleshooting.resetData.cancel": "Abbrechen", "desktopTroubleshooting.resetData.closingWindows": "Notion-Fenster werden geschlossen", "desktopTroubleshooting.resetData.deletingFiles": "Dateien werden gelöscht", "desktopTroubleshooting.resetData.done": "<PERSON><PERSON><PERSON><PERSON>", "desktopTroubleshooting.resetData.doneMessage": "Die App wurde zurückgesetzt.", "desktopTroubleshooting.resetData.failed": "Unser Wiederherstellungsprozess konnte einige Dateien nicht löschen. Wir entschuldigen uns für den Ärger – bitte besuche https://www.notion.so/de-de/help, um Hilfe zu erhalten. Um ein vollständiges Zurücksetzen der App manuell zu erzwingen, schließe Notion bitte vollständig. Lösche dann den folgenden Pfad: {userDataPath}", "desktopTroubleshooting.resetData.message": "Dad<PERSON>ch werden alle lokalen und internen Daten, einsch<PERSON>ßlich des Caches und der lokalen Einstellungen, gel<PERSON><PERSON><PERSON>, wodurch die Notion-App in einen neu installierten Zustand versetzt wird. Du wirst dabei von Notion abgemeldet. Deine Seiten und andere In-App-Inhalte bleiben unberührt. Möchtest du fortfahren?", "desktopTroubleshooting.resetData.reset": "Alle lokalen Daten zurücksetzen", "desktopTroubleshooting.resetData.restart": "Neustarten", "desktopTroubleshooting.resetData.title": "Alle lokalen Daten zurücksetzen und löschen", "desktopTroubleshooting.showLogs.error.message.mac": "Notion hat beim V<PERSON>, die Protokolle im Finder anzuzeigen, einen Fehler festgestellt:", "desktopTroubleshooting.showLogs.error.message.windows": "Notion hat beim V<PERSON>, die Protokolle im Explorer anzuzeigen, einen <PERSON>hler festgestellt:", "desktopTroubleshooting.showLogs.error.title": "Fehler beim Anzeigen der Protokolle", "desktopTroubleshooting.startRecordingNetLog": "Aufzeichnung des Netzwerkprotokolls starten", "desktopTroubleshooting.stopRecordingNetLog": "Aufzeichnung des Netzwerkprotokolls beenden", "menuBarIcon.menu.changeCommandSearchAndNotionAiShortcut": "Tastaturk<PERSON><PERSON><PERSON> bear<PERSON>", "menuBarIcon.menu.changeCommandSearchShortcut": "Tastaturkürzel für Direktsuche ändern", "menuBarIcon.menu.enableQuickSearch": "Quick Search aktivieren", "menuBarIcon.menu.keepInBackground": "Im <PERSON> halten", "menuBarIcon.menu.launchPreferences": "Start-Einstellungen", "menuBarIcon.menu.openOnLogin": "Notion bei Anmeldung öffnen", "menuBarIcon.menu.quitNotion": "Notion beenden", "menuBarIcon.menu.showImmediately": "<PERSON><PERSON> anzeigen", "menuBarIcon.menu.showNotionInMenuBar": "Notion in der Menüleiste anzeigen", "menuBarIcon.menu.toggleCommandSearch": "Direktsuche umschalten", "menuBarIcon.menu.toggleNotionAi": "Notion-KI umschalten", "openAtLogin.dialog.detail": "{operatingSystem} hat Notion daran geh<PERSON>, die Einstellung „Bei Anmeldung öffnen“ zu konfigurieren. Dies kommt meist vor, wenn der Notion-Startvorgang in den Systemeinstellungen konfiguriert wurde oder wenn du über unzureichende Zugriffsrechte verfügst. Du kannst diese Einstellung aber manuell in den Systemeinstellungen konfigurieren.", "openAtLogin.dialog.title": "Be<PERSON> Anmeldung ö<PERSON>", "tabSpaces.deleteDialog.cancelButton": "Abbrechen", "tabSpaces.deleteDialog.deleteButton": "Löschen", "tabSpaces.deleteDialog.detail": "Die Gruppierung aller Tabs in dieser Tab-Gruppe wird aufgehoben.", "tabSpaces.deleteDialog.title": "Möchtest du deine Tab-Gruppe „{title}“ löschen?", "tabSpaces.snackbar.switchedToTabGroup": "<PERSON><PERSON><PERSON><PERSON><PERSON> zu {title}", "tabSpaces.snackbar.switchedToUngroupedTabs": "Zu nicht gruppierten Tabs gewechselt", "tabSpaces.snackbar.tabGroupPlaceholder": "Tab-Gruppe", "updatePrompt.detail": "Möchtest du die Installation jetzt vornehmen? Wir öffnen dann deine Fenster und Tabs für dich erneut.", "updatePrompt.installAndRelaunch": "Installieren und neu starten", "updatePrompt.message": "Eine neue Version von Notion ist verfügbar!", "updatePrompt.remindMeLater": "Später erinnern", "window.closeDialog.cancelButton": "Abbrechen", "window.closeDialog.confirmButton": "Schließen", "window.closeDialog.title.app": "Notion schließen?", "window.closeDialog.title.tab": "Notion-Tab schließen?", "window.closeDialog.title.window": "Notion-<PERSON>ster schließen?", "window.loadingError.message": "<PERSON><PERSON> beim <PERSON> von Notion. Stelle eine Internet-Verbindung her, um loszulegen.", "window.loadingError.reload": "Neu laden", "window.movedTabSnackbarMessage": "{tabTitle} wurde nach {tabSpaceTitle} verschoben", "window.tabLoadingError.cancel": "Abbrechen", "window.tabMenu.closeOtherTabs": "Andere Tabs schließen", "window.tabMenu.closeTab": "<PERSON><PERSON> s<PERSON>n", "window.tabMenu.closeTabsToLeft": "Tabs links schließen", "window.tabMenu.closeTabsToRight": "Tabs rechts schließen", "window.tabMenu.copyLink": "<PERSON>", "window.tabMenu.duplicateTab": "<PERSON><PERSON> <PERSON>", "window.tabMenu.moveTo": "Verschieben nach", "window.tabMenu.moveToNewWindow": "Tab in neues Fenster verschieben", "window.tabMenu.moveToSubmenuNewWindow": "<PERSON><PERSON><PERSON>", "window.tabMenu.pinTab": "<PERSON><PERSON> anheften", "window.tabMenu.refresh": "Tab aktualisieren", "window.tabMenu.reopenClosedTab": "Zuletzt geschlossenen Tab erneut öffnen", "window.tabMenu.replacePinnedTabUrl": "Angeheftete URL durch aktuelle ersetzen", "window.tabMenu.returnToPinnedTabUrl": "Zurück zu angehefteter URL", "window.tabMenu.ungroupTab": "Tab-Gruppierung aufheben", "window.tabMenu.unpinTab": "<PERSON><PERSON><PERSON>", "window.tabTitlePlaceholder": "Tab", "window.ungroupedTabSnackbarMessage": "Gruppierung von {tabTitle} aufgehoben"}