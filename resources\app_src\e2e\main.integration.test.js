"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const test_1 = require("@playwright/test");
const helpers_1 = require("./helpers");
const MAX_TITLE_LENGTH = 20;
const PAGE_LENGTH = 10;
let app;
let identifiedPages;
let firstTab;
test_1.test.beforeAll(async () => {
    app = await (0, helpers_1.launchApp)();
    identifiedPages = await (0, helpers_1.waitForInitialWebContents)(app);
});
test_1.test.afterAll(async () => {
    await app?.close();
});
(0, test_1.test)("opens", () => {
    (0, test_1.expect)(identifiedPages[helpers_1.PageType.tabBar].length).toBeGreaterThan(0);
});
(0, test_1.test)("loads initial web contents", async () => {
    firstTab = identifiedPages[helpers_1.PageType.tab][0];
    await (0, helpers_1.waitForMainAppBoot)(firstTab);
    await firstTab.waitForLoadState("networkidle");
});
test_1.test.skip("it can login", async () => {
    const credentials = {
        email: process.env.NOTION_TEST_EMAIL,
        password: process.env.NOTION_TEST_PASSWORD,
    };
    if (!credentials.email || !credentials.password) {
        throw new Error("No test credentials found");
    }
    await firstTab.waitForSelector('input[type="email"]');
    await firstTab
        .locator('input[type="email"]')
        .pressSequentially(credentials.email);
    await firstTab.getByText("Continue", { exact: true }).click();
    const passwdSelector = 'input[type="password"]';
    await firstTab.waitForSelector(passwdSelector);
    await firstTab.focus(passwdSelector);
    await firstTab.locator(passwdSelector).pressSequentially(credentials.password);
    await firstTab.locator(passwdSelector).pressSequentially(" ");
    await firstTab.locator(passwdSelector).press("Backspace");
    await firstTab.waitForTimeout(500);
    await firstTab.getByText("Continue with password", { exact: true }).click();
    await firstTab.waitForSelector(".notion-cursor-listener");
});
test_1.test.skip("it can create a new page", async () => {
    await firstTab.locator("body").press("ControlOrMeta+N", { delay: 100 });
    const pageTitle = (0, helpers_1.getRandomStringNoNewLine)(MAX_TITLE_LENGTH);
    await firstTab.locator('h1[placeholder="Untitled"]').fill(pageTitle);
    await firstTab.keyboard.press("Enter");
    const pageContent = (0, helpers_1.getRandomString)(PAGE_LENGTH);
    await firstTab.locator('div[data-content-editable-leaf="true"]').focus();
    await firstTab
        .locator('div[data-content-editable-leaf="true"]')
        .pressSequentially(pageContent);
});
