"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRandomString = getRandomString;
exports.getRandomStringNoNewLine = getRandomStringNoNewLine;
const CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz01234567891234567890!@#$%^&*()-+.?;:,<>";
const CHARACTERS_WITH_NEW_LINE = `${CHARACTERS}\n`;
function getRandomString(length) {
    let result = "";
    for (let i = 0; i < length; i++) {
        result += getRandomCharacter();
    }
    return result;
}
function getRandomStringNoNewLine(length) {
    return CHARACTERS.charAt(Math.floor(Math.random() * CHARACTERS.length));
}
function getRandomCharacter() {
    return CHARACTERS_WITH_NEW_LINE.charAt(Math.floor(Math.random() * CHARACTERS_WITH_NEW_LINE.length));
}
